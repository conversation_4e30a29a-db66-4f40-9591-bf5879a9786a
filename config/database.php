<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => env('DB_PREFIX', ''),
        ],

        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', 3306),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => env('DB_CHARSET', 'utf8mb4'),
            'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX', ''),
            'strict' => env('DB_STRICT_MODE', true),
            'engine' => env('DB_ENGINE', null),
            'timezone' => env('DB_TIMEZONE', '+00:00'),
        ],
        'mysql-app' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_APP_BACKEND', '127.0.0.1'),
            'port' => env('DB_PORT_APP_BACKEND', 3306),
            'database' => env('DB_DATABASE_APP_BACKEND', 'forge'),
            'username' => env('DB_USERNAME_APP_BACKEND', 'forge'),
            'password' => env('DB_PASSWORD_APP_BACKEND', ''),
            'unix_socket' => env('DB_SOCKET_APP_BACKEND', ''),
            'charset' => env('DB_CHARSET_APP_BACKEND', 'utf8mb4'),
            'collation' => env('DB_COLLATION_APP_BACKEND', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX_APP_BACKEND', ''),
            'strict' => env('DB_STRICT_MODE_APP_BACKEND', true),
            'engine' => env('DB_ENGINE_APP_BACKEND', null),
            'timezone' => env('DB_TIMEZONE_APP_BACKEND', '+00:00'),
        ],
        '6Vr' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_APP_BACKEND', '127.0.0.1'),
            'port' => env('DB_PORT_APP_BACKEND', 3306),
            'database' => env('DB_DATABASE_APP_BACKEND', 'forge'),
            'username' => env('DB_USERNAME_APP_BACKEND', 'forge'),
            'password' => env('DB_PASSWORD_APP_BACKEND', ''),
            'unix_socket' => env('DB_SOCKET_APP_BACKEND', ''),
            'charset' => env('DB_CHARSET_APP_BACKEND', 'utf8mb4'),
            'collation' => env('DB_COLLATION_APP_BACKEND', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX_APP_BACKEND', ''),
            'strict' => env('DB_STRICT_MODE_APP_BACKEND', true),
            'engine' => env('DB_ENGINE_APP_BACKEND', null),
            'timezone' => env('DB_TIMEZONE_APP_BACKEND', '+00:00'),
        ],
        'aCh' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_APP_BACKEND_1', '127.0.0.1'),
            'port' => env('DB_PORT_APP_BACKEND_1', 3306),
            'database' => env('DB_DATABASE_APP_BACKEND_1', 'forge'),
            'username' => env('DB_USERNAME_APP_BACKEND_1', 'forge'),
            'password' => env('DB_PASSWORD_APP_BACKEND_1', ''),
            'unix_socket' => env('DB_SOCKET_APP_BACKEND_1', ''),
            'charset' => env('DB_CHARSET_APP_BACKEND_1', 'utf8mb4'),
            'collation' => env('DB_COLLATION_APP_BACKEND_1', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX_APP_BACKEND_1', ''),
            'strict' => env('DB_STRICT_MODE_APP_BACKEND_1', true),
            'engine' => env('DB_ENGINE_APP_BACKEND_1', null),
            'timezone' => env('DB_TIMEZONE_APP_BACKEND_1', '+00:00'),
        ],
        'Ca4' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_APP_BACKEND_2', '127.0.0.1'),
            'port' => env('DB_PORT_APP_BACKEND_2', 3306),
            'database' => env('DB_DATABASE_APP_BACKEND_2', 'forge'),
            'username' => env('DB_USERNAME_APP_BACKEND_2', 'forge'),
            'password' => env('DB_PASSWORD_APP_BACKEND_2', ''),
            'unix_socket' => env('DB_SOCKET_APP_BACKEND_2', ''),
            'charset' => env('DB_CHARSET_APP_BACKEND_2', 'utf8mb4'),
            'collation' => env('DB_COLLATION_APP_BACKEND_2', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX_APP_BACKEND_2', ''),
            'strict' => env('DB_STRICT_MODE_APP_BACKEND_2', true),
            'engine' => env('DB_ENGINE_APP_BACKEND_2', null),
            'timezone' => env('DB_TIMEZONE_APP_BACKEND_2', '+00:00'),
        ],
        'vtH' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST_APP_BACKEND_3', '127.0.0.1'),
            'port' => env('DB_PORT_APP_BACKEND_3', 3306),
            'database' => env('DB_DATABASE_APP_BACKEND_3', 'forge'),
            'username' => env('DB_USERNAME_APP_BACKEND_3', 'forge'),
            'password' => env('DB_PASSWORD_APP_BACKEND_3', ''),
            'unix_socket' => env('DB_SOCKET_APP_BACKEND_3', ''),
            'charset' => env('DB_CHARSET_APP_BACKEND_3', 'utf8mb4'),
            'collation' => env('DB_COLLATION_APP_BACKEND_3', 'utf8mb4_unicode_ci'),
            'prefix' => env('DB_PREFIX_APP_BACKEND_3', ''),
            'strict' => env('DB_STRICT_MODE_APP_BACKEND_3', true),
            'engine' => env('DB_ENGINE_APP_BACKEND_3', null),
            'timezone' => env('DB_TIMEZONE_APP_BACKEND_3', '+00:00'),
        ],
        'pgsql' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', 5432),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => env('DB_CHARSET', 'utf8'),
            'prefix' => env('DB_PREFIX', ''),
            'schema' => env('DB_SCHEMA', 'public'),
            'sslmode' => env('DB_SSL_MODE', 'prefer'),
        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', 1433),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => env('DB_CHARSET', 'utf8'),
            'prefix' => env('DB_PREFIX', ''),
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer set of commands than a typical key-value systems
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', false),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'lumen'), '_').'_database_'),
        ],

        'default' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_DB', 0),
        ],

        'cache' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_CACHE_DB', 1),
        ],

    ],

    'sync' => [
        'enabled' => (bool)env('DATABASE_SYNC_ENABLE', false),
        'default_connection' => env('DATABASE_SYNC_DEFAULT_CON', 'mysql-app'),
        'tables' => [
            'banners',
            'banner_segment',
            'betting_validation_rules',
            'bonuses',
            'bonus_info',
            'bonus_info_segment',
            'bonus_slot',
            'bonus_slot_provider',
            'bonus_trigger_sessions',
            'countries',
            'countries_currencies_pivot',
            'countries_languages_pivot',
            'country_slots_providers',
            'currencies',
            'data_importer_migrations',
            'data_subscriptions',
            'exchange_rates',
            'free_spins',
            'languages',
            //'migrations',
            'migration_logs',
            'oauth_clients',
            'oauth_personal_access_clients',
            'payout_fee_settings',
            'payout_without_full_information',
            'promocodes',
            'promocodes_balances',
            'requisites_blacklist',
            'risky_info_by_countries',
            'sections',
            'segments_statistics',
            'settings',
            'slots',
            'slots_categories',
            'slots_categories_pivot',
            'slots_providers',
            'slots_providers_sections',
            'static_pages',
            'surveys',
            'surveys_answers',
            'surveys_questions'
        ],
    ],
];
