<?php

return [
    'client_id' => (int)env('OAUTH_CLIENT_ID', 2),
    'whitelabel_client_id' => 2,
    'oauth_host' => env('OAUTH_HOST', 'https://4rabetsite.com'),
    'client_secret' => env('OAUTH_CLIENT_SECRET', 'NaeCBfS44xY3y6fs5M1qz4Ml607YBTqu0DZUIrYZ'),
    'app_debug' => env('APP_DEBUG', false),
    'ultimate_client' => env('ULTIMATE_CLIENT', 2),
    'passport' => [
        'token' => [
            'expires_in' => env('PASSPORT_TOKEN_EXPIRES_IN_MINUTES', 14400),
        ]
    ],
    'throttler' => [
        'default' => [
            'attempts' => env('THROTTLER_ATTEMPTS', 1),
            'timeout' => env('THROTTLER_INTERVAL_SECONDS', 20),
        ],
        'registration' => [
            'attempts' => env('THROTTLER_REGISTRATION_ATTEMPTS', 1),
            'timeout' => env('THROTTLER_REGISTRATION_INTERVAL_SECONDS', 10),
        ],
        'login' => [
            'attempts' => env('THROTTLER_LOGIN_ATTEMPTS', 5),
            'timeout' => env('THROTTLER_LOGIN_INTERVAL_SECONDS', 10),
        ],
        'logout' => [
            'attempts' => env('THROTTLER_LOGOUT_ATTEMPTS', 5),
            'timeout' => env('THROTTLER_LOGOUT_INTERVAL_SECONDS', 10),
        ],
        'refresh' => [
            'attempts' => env('THROTTLER_REFRESH_ATTEMPTS', 5),
            'timeout' => env('THROTTLER_REFRESH_INTERVAL_SECONDS', 10),
        ],
        'whitelist_expiration_time' => env('THROTTLER_WHITELIST_IP_EXPIRATION_MINUTES', 60)
    ],
    'encrypt' => [
        'default' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',
        'custom' => env('APP_CUSTOM_ENCRYPT', 'FVQIOSABPDZLRHEGCNXWMKTYUJtcwfohuqylmjekazxbvsrdginp2570641893+/'),
    ],
    'alanbase' => [
        'api_key' => env('ALANBASE_API_KEY', ''),
        'api_url' => env('ALANBASE_API_URL', 'https://4rabet.api.alanbase.com')
    ],
];
