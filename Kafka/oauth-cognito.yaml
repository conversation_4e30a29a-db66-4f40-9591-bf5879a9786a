---
version: '3.4'
services:

  kafka-ui:
    container_name: kafka-ui
    image: provectuslabs/kafka-ui:local
    ports:
      - 8080:8080
    depends_on:
      - kafka0 # OMITTED, TAKE UP AN EXAMPLE FROM OTHER COMPOSE FILES
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL: SSL
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka0:29092
      AUTH_TYPE: OAUTH2_COGNITO
      AUTH_COGNITO_ISSUER_URI: "https://cognito-idp.eu-central-1.amazonaws.com/eu-central-xxxxxx"
      AUTH_COGNITO_CLIENT_ID: ""
      AUTH_COGNITO_CLIENT_SECRET: ""
      AUTH_COGNITO_SCOPE: "openid"
      AUTH_COGNITO_USER_NAME_ATTRIBUTE: "username"
      AUTH_COGNITO_LOGOUT_URI: "https://<domain>.auth.eu-central-1.amazoncognito.com/logout"