---
version: '2'
services:
  nginx:
    image: nginx:latest
    volumes:
      - ./proxy.conf:/etc/nginx/conf.d/default.conf
    ports:
      - 8080:80

  kafka-ui:
    container_name: kafka-ui
    image: provectuslabs/kafka-ui:latest
    ports:
      - 8082:8080
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
      SERVER_SERVLET_CONTEXT_PATH: /kafka-ui
