{"name": "s3-sink", "config": {"connector.class": "io.confluent.connect.s3.S3SinkConnector", "topics": "github-issues, github-pull_requests, github-commits", "tasks.max": "1", "s3.region": "eu-central-1", "s3.bucket.name": "kafka-ui-s3-sink-connector", "s3.part.size": "5242880", "flush.size": "3", "storage.class": "io.confluent.connect.s3.storage.S3Storage", "format.class": "io.confluent.connect.s3.format.json.JsonFormat", "schema.generator.class": "io.confluent.connect.storage.hive.schema.DefaultSchemaGenerator", "partitioner.class": "io.confluent.connect.storage.partitioner.DefaultPartitioner", "schema.compatibility": "BACKWARD"}}