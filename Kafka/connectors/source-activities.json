{"name": "source_postgres_activities", "config": {"connector.class": "io.confluent.connect.jdbc.JdbcSourceConnector", "connection.url": "***************************************", "connection.user": "dev_user", "connection.password": "12345", "topic.prefix": "source-", "poll.interval.ms": 3600000, "table.whitelist": "public.activities", "mode": "bulk", "transforms": "extractkey", "transforms.extractkey.type": "org.apache.kafka.connect.transforms.ExtractField$Key", "transforms.extractkey.field": "id", "key.converter": "org.apache.kafka.connect.storage.StringConverter", "key.converter.schema.registry.url": "http://schemaregistry0:8085", "value.converter": "io.confluent.connect.avro.AvroConverter", "value.converter.schema.registry.url": "http://schemaregistry0:8085"}}