{"name": "sink_postgres_activities", "config": {"connector.class": "io.confluent.connect.jdbc.JdbcSinkConnector", "connection.url": "***************************************", "connection.user": "dev_user", "connection.password": "12345", "topics": "source-activities", "table.name.format": "sink_activities", "key.converter": "org.apache.kafka.connect.storage.StringConverter", "key.converter.schema.registry.url": "http://schemaregistry0:8085", "value.converter": "io.confluent.connect.avro.AvroConverter", "value.converter.schema.registry.url": "http://schemaregistry0:8085", "auto.create": "true", "pk.mode": "record_value", "pk.fields": "id", "insert.mode": "upsert"}}