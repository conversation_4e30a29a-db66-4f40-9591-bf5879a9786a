---
version: '2'
services:

    kafka-ui:
        container_name: kafka-ui
        image: provectuslabs/kafka-ui:latest
        ports:
            - 8080:8080
        depends_on:
            - kafka0
            - schemaregistry0
        environment:
            kafka.clusters.0.name: SerdeExampleCluster
            kafka.clusters.0.bootstrapServers: kafka0:29092
            kafka.clusters.0.schemaRegistry: http://schemaregistry0:8085
            # optional auth and ssl properties for SR
            #kafka.clusters.0.schemaRegistryAuth.username: "use"
            #kafka.clusters.0.schemaRegistryAuth.password: "pswrd"
            #kafka.clusters.0.schemaRegistrySSL.keystoreLocation: /kafka.keystore.jks
            #kafka.clusters.0.schemaRegistrySSL.keystorePassword: "secret"
            #kafka.clusters.0.schemaRegistrySSL.truststoreLocation: /kafka.truststore.jks
            #kafka.clusters.0.schemaRegistrySSL.truststorePassword: "secret"

            kafka.clusters.0.defaultKeySerde: Int32  #optional
            kafka.clusters.0.defaultValueSerde: String #optional

            kafka.clusters.0.serde.0.name: ProtobufFile
            kafka.clusters.0.serde.0.topicKeysPattern: "topic1"
            kafka.clusters.0.serde.0.topicValuesPattern: "topic1"
            kafka.clusters.0.serde.0.properties.protobufFiles.0: /protofiles/key-types.proto
            kafka.clusters.0.serde.0.properties.protobufFiles.1: /protofiles/values.proto
            kafka.clusters.0.serde.0.properties.protobufMessageNameForKey: test.MyKey # default type for keys
            kafka.clusters.0.serde.0.properties.protobufMessageName: test.MyValue # default type for values
            kafka.clusters.0.serde.0.properties.protobufMessageNameForKeyByTopic.topic1: test.MySpecificTopicKey # keys type for topic "topic1"
            kafka.clusters.0.serde.0.properties.protobufMessageNameByTopic.topic1: test.MySpecificTopicValue # values type for topic "topic1"

            kafka.clusters.0.serde.1.name: String
            #kafka.clusters.0.serde.1.properties.encoding: "UTF-16" #optional, default is UTF-8
            kafka.clusters.0.serde.1.topicValuesPattern: "json-events|text-events"

            kafka.clusters.0.serde.2.name: AsciiString
            kafka.clusters.0.serde.2.className: com.provectus.kafka.ui.serdes.builtin.StringSerde
            kafka.clusters.0.serde.2.properties.encoding: "ASCII"

            kafka.clusters.0.serde.3.name: SchemaRegistry # will be configured automatically using cluster SR
            kafka.clusters.0.serde.3.topicValuesPattern: "sr-topic.*"

            kafka.clusters.0.serde.4.name: AnotherSchemaRegistry
            kafka.clusters.0.serde.4.className: com.provectus.kafka.ui.serdes.builtin.sr.SchemaRegistrySerde
            kafka.clusters.0.serde.4.properties.url: http://schemaregistry0:8085
            kafka.clusters.0.serde.4.properties.keySchemaNameTemplate: "%s-key"
            kafka.clusters.0.serde.4.properties.schemaNameTemplate: "%s-value"
            #kafka.clusters.0.serde.4.topicValuesPattern: "sr2-topic.*"
            # optional auth and ssl properties for SR:
            #kafka.clusters.0.serde.4.properties.username: "user"
            #kafka.clusters.0.serde.4.properties.password: "passw"
            #kafka.clusters.0.serde.4.properties.keystoreLocation:  /kafka.keystore.jks
            #kafka.clusters.0.serde.4.properties.keystorePassword: "secret"
            #kafka.clusters.0.serde.4.properties.truststoreLocation: /kafka.truststore.jks
            #kafka.clusters.0.serde.4.properties.truststorePassword: "secret"

            kafka.clusters.0.serde.5.name: UInt64
            kafka.clusters.0.serde.5.topicKeysPattern: "topic-with-uint64keys"
        volumes:
            - ./proto:/protofiles

    kafka0:
        image: confluentinc/cp-kafka:7.2.1
        hostname: kafka0
        container_name: kafka0
        ports:
            - "9092:9092"
            - "9997:9997"
        environment:
            KAFKA_BROKER_ID: 1
            KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT'
            KAFKA_ADVERTISED_LISTENERS: 'PLAINTEXT://kafka0:29092,PLAINTEXT_HOST://localhost:9092'
            KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
            KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
            KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
            KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
            KAFKA_JMX_PORT: 9997
            KAFKA_JMX_HOSTNAME: localhost
            KAFKA_JMX_OPTS: -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=kafka0 -Dcom.sun.management.jmxremote.rmi.port=9997
            KAFKA_PROCESS_ROLES: 'broker,controller'
            KAFKA_NODE_ID: 1
            KAFKA_CONTROLLER_QUORUM_VOTERS: '1@kafka0:29093'
            KAFKA_LISTENERS: 'PLAINTEXT://kafka0:29092,CONTROLLER://kafka0:29093,PLAINTEXT_HOST://0.0.0.0:9092'
            KAFKA_INTER_BROKER_LISTENER_NAME: 'PLAINTEXT'
            KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
            KAFKA_LOG_DIRS: '/tmp/kraft-combined-logs'
        volumes:
            - ./scripts/update_run.sh:/tmp/update_run.sh
        command: "bash -c 'if [ ! -f /tmp/update_run.sh ]; then echo \"ERROR: Did you forget the update_run.sh file that came with this docker-compose.yml file?\" && exit 1 ; else /tmp/update_run.sh && /etc/confluent/docker/run ; fi'"

    schemaregistry0:
        image: confluentinc/cp-schema-registry:7.2.1
        ports:
            - 8085:8085
        depends_on:
            - kafka0
        environment:
            SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS: PLAINTEXT://kafka0:29092
            SCHEMA_REGISTRY_KAFKASTORE_SECURITY_PROTOCOL: PLAINTEXT
            SCHEMA_REGISTRY_HOST_NAME: schemaregistry0
            SCHEMA_REGISTRY_LISTENERS: http://schemaregistry0:8085

            SCHEMA_REGISTRY_SCHEMA_REGISTRY_INTER_INSTANCE_PROTOCOL: "http"
            SCHEMA_REGISTRY_LOG4J_ROOT_LOGLEVEL: INFO
            SCHEMA_REGISTRY_KAFKASTORE_TOPIC: _schemas
