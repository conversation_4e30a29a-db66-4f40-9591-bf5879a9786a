<?php

use App\Providers\EventServiceProvider;
use Deversin\Signature\SignatureServiceProvider;

require_once __DIR__.'/../vendor/autoload.php';

if (file_exists(__DIR__ . '/../.env')) {
    (new Laravel\Lumen\Bootstrap\LoadEnvironmentVariables(
        dirname(__DIR__)
    ))->bootstrap();
}

date_default_timezone_set(env('APP_TIMEZONE', 'UTC'));

/*
|--------------------------------------------------------------------------
| Create The Application
|--------------------------------------------------------------------------
|
| Here we will load the environment and create the application instance
| that serves as the central piece of this framework. We'll use this
| application as an "IoC" container and router for this framework.
|
*/

$app = new Laravel\Lumen\Application(
    dirname(__DIR__)
);

 $app->withFacades();
 $app->withEloquent();

/*
|--------------------------------------------------------------------------
| Register Container Bindings
|--------------------------------------------------------------------------
|
| Now we will register a few bindings in the service container. We will
| register the exception handler and the console kernel. You may add
| your own bindings here if you like or you can make another file.
|
*/

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->middleware([
    App\Http\Middleware\AddTokenToRequest::class,
    App\Http\Middleware\IpAddress::class,
    App\Http\Middleware\CheckForAppCalls::class,
    App\Http\Middleware\CensorMiddleware::class,
]);

$app->routeMiddleware([
    'check' => App\Http\Middleware\CheckPermission::class,
    'users' => App\Http\Middleware\Authenticate::class,
    'auth' => App\Http\Middleware\HasUltimateToken::class,
    'sign' => \Deversin\Signature\Http\Middleware\SignatureMiddleware::class,
    'logRequest' => App\Http\Middleware\LogRequestMiddleware::class,
    'throttler' => App\Http\Middleware\Throttler::class,
    'adminCredential' => App\Http\Middleware\AdminCredentialMiddleware::class,
    'hideDataForUser' => \App\Http\Middleware\HideDataForUserMiddleware::class,
    'clusterGrant' => \AppV2\Core\Http\Middleware\CoreClusterConnectionMiddleware::class,
    'encryptPaymentSystem' => \AppV2\Core\Http\Middleware\EncryptPaymentSystemMiddleware::class,
]);

$app->configure('auth');
$app->configure('common');
$app->configure('cors');
$app->configure('queue');
$app->configure('signature');
$app->configure('external-services');
$app->configure('free-spins');

$app->register(App\Providers\AppServiceProvider::class);
$app->register(App\Providers\JobServiceProvider::class);
$app->register(App\Providers\AuthServiceProvider::class);
$app->register(Laravel\Passport\PassportServiceProvider::class);
$app->register(Dusterio\LumenPassport\PassportServiceProvider::class);
$app->register(Illuminate\Redis\RedisServiceProvider::class);
$app->register(SignatureServiceProvider::class);
$app->register(EventServiceProvider::class);
$app->register(\App\Providers\QueueServiceProvider::class);

$app->register(Fruitcake\Cors\CorsServiceProvider::class);
$app->middleware([Fruitcake\Cors\HandleCors::class]);
if (env('APP_DEBUG')) {
    $app->register(Laravel\Tinker\TinkerServiceProvider::class);
    $app->register(Thedevsaddam\LumenRouteList\LumenRouteListServiceProvider::class);
}

if (!$app->runningInConsole()) {
    $app->configure('newrelic');
    if (config('newrelic.enabled') || ($_GET['NEWRELIC_ENABLED'] ?? null) === 'true') {
        $app->middleware([
            \App\Http\Middleware\NewRelicMiddleware::class,
        ]);

        $app->register(\App\Providers\NewRelicServiceProvider::class);
    }
}

/*
|--------------------------------------------------------------------------
| Load The Application Routes
|--------------------------------------------------------------------------
|
| Next we will include the routes file so that they can all be added to
| the application. This will provide all of the URLs the application
| can respond to, as well as the controllers that may handle them.
|
*/

$app->router->group([
    'namespace' => '\WhiteLabelAdmin\Controllers',
], function ($router) {
    require __DIR__.'/../routes/web.php';
});
$app->router->group([
    'namespace' => 'App\Http\Controllers',
], function ($router) {
    require __DIR__.'/../routes/web-app.php';
});
$app->router->group([], function ($router) {
    require __DIR__ . '/../routes/proxy.php';
});
$app->router->group([], function ($router) {
    require __DIR__ . '/../routes/v4/api.php';
});

return $app;
