<?php

use Dev<PERSON>in\Signature\SignatureServiceProvider;
use <PERSON><PERSON>\Lumen\Routing\Router;

require __DIR__."/../vendor/laravel/lumen-framework/src/helpers.php";

require __DIR__.'/../vendor/autoload.php';

if (file_exists(__DIR__ . '/../.env')) {
    (new Laravel\Lumen\Bootstrap\LoadEnvironmentVariables(
        dirname(__DIR__)
    ))->bootstrap();
}

date_default_timezone_set(env('APP_TIMEZONE', 'UTC'));

/*
|--------------------------------------------------------------------------
| Create The Application
|--------------------------------------------------------------------------
|
| Here we will load the environment and create the application instance
| that serves as the central piece of this framework. We'll use this
| application as an "IoC" container and router for this framework.
|
*/

$app = new Dusterio\LumenPassport\Lumen7Application(
    dirname(__DIR__)
);

$app->instance('path.config', app()->basePath() . DIRECTORY_SEPARATOR . 'config');
$app->instance('path.storage', app()->basePath() . DIRECTORY_SEPARATOR . 'storage');

 $app->withFacades();
 $app->withEloquent();

/*
|--------------------------------------------------------------------------
| Register Container Bindings
|--------------------------------------------------------------------------
|
| Now we will register a few bindings in the service container. We will
| register the exception handler and the console kernel. You may add
| your own bindings here if you like or you can make another file.
|
*/

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->configure('common');
$app->configure('auth');
$app->configure('broadcasting');
$app->configure('mail');
$app->configure('database');
$app->configure('enqueue');
$app->configure('cors');
$app->configure('gateways');
$app->configure('signature');
$app->configure('responsecache');
$app->configure('personal-responsecache');
$app->configure('logging');
$app->configure('kafka');
$app->configure('covery');
$app->configure('clients');
$app->configure('payouts');
$app->configure('passport');
$app->configure('clients');
$app->configure('services');
$app->configure('wager');
$app->configure('alanbase');
$app->configure('tournaments');
$app->configure('singleflight');
$app->configure('testing');
$app->configure('url-signer');

$app->middleware([
    // ClusterConnectionMiddleware should be first, AddAttributesToRequest should be second
    App\Http\Middleware\ClusterConnectionMiddleware::class,
    App\Http\Middleware\AddAttributesToRequest::class,
    \App\Http\Middleware\DefineRealIpAddress::class,
    AppV3\Player\Http\Middleware\PlayerBlockedMiddleware::class,
    App\Http\Middleware\TrimStrings::class,
    App\Http\Middleware\CensorMiddleware::class,
    App\Http\Middleware\LocalizationMiddleware::class,
    \AppV3\Core\Cache\Http\Middleware\CacheSkipAndRefreshMiddleware::class
]);
 $app->routeMiddleware([
     'players' => App\Http\Middleware\Authenticate::class,
     'admins' => App\Http\Middleware\HasAdminToken::class,
     'cuckoo_check' => App\Http\Middleware\CuckooMiddleware::class,
     'throttler' => App\Http\Middleware\Throttler::class,
     'throttler_general' => App\Http\Middleware\ThrottlerGeneral::class,
     'paykassma_webhook' => App\Http\Middleware\PaykassmaWebhookMiddleware::class,
     'saas' => App\Http\Middleware\SaasCalling::class,
     'cache' => \App\Http\Middleware\CacheResponse::class,
     'cache_personal' => \Deversin\PersonalResponseCache\Middlewares\CacheResponse::class,
     'log_request' => \App\Http\Middleware\LogRequestMiddleware::class,
     'sign' => \Deversin\Signature\Http\Middleware\SignatureMiddleware::class,
     'covery_registration' => \App\Http\Middleware\CoveryRegistrationMiddleware::class,
     'covery_profile_update' => \App\Http\Middleware\CoveryProfileUpdateMiddleware::class,
     'check_player_id_for_payments' => \App\Http\Middleware\CheckPlayerIdForPayments::class,
     'slot_route_id_resolver' => \AppV3\Casino\Http\Middleware\SlotRouteIdResolver::class,
     'check_fingerprint' => \App\Http\Middleware\CheckFingerprintMiddleware::class,
     'captcha_check' => \AppV3\Player\Http\Middleware\CaptchaMiddleware::class,
     'idempotent' => \AppV3\Balance\Http\Middleware\IdempotencyMiddleware::class,
     'vip_request' => \AppV3\Player\Http\Middleware\VipPlayerRequestMiddleware::class,
     'testing_endpoints' => \App\Http\Middleware\Testing\TestingMiddleware::class,
     'testing_autotest_endpoints' => \App\Http\Middleware\Testing\TestingAutotestMiddleware::class,
 ]);


$app->register(App\Providers\AppServiceProvider::class);
$app->register(App\Providers\JobServiceProvider::class);
$app->register(App\Providers\AdminProvider::class);
$app->register(App\Providers\RepositoryProvider::class);
$app->register(App\Providers\SearchProvider::class);
$app->register(App\Providers\SymfonyStuffProvider::class);
$app->register(Dusterio\LumenPassport\PassportServiceProvider::class);
$app->register(App\Providers\AuthServiceProvider::class);
$app->register(Illuminate\Redis\RedisServiceProvider::class);
$app->register(Illuminate\Mail\MailServiceProvider::class);
$app->register(Enqueue\LaravelQueue\EnqueueServiceProvider::class);
$app->register(Spatie\ResponseCache\ResponseCacheServiceProvider::class);
$app->register(\Deversin\PersonalResponseCache\ResponseCacheServiceProvider::class);
$app->register(App\Providers\MfaProvider::class);
$app->register(App\Providers\BroadcastServiceProvider::class);
$app->register(Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class);
$app->register(SignatureServiceProvider::class);
$app->register(\AppV3\Core\Providers\KafkaServiceProvider::class);
$app->register(App\Providers\EventServiceProvider::class);
$app->register(\AppV3\Core\Services\Elasticsearch\Providers\ElasticsearchServiceProvider::class);
$app->register(\AppV3\Core\Providers\GoogleCustomGrantServiceProvider::class);
$app->register(\App\Providers\ExtendedCentrifugoServiceProvider::class);


$app->alias('mail.manager', Illuminate\Mail\MailManager::class);
$app->alias('mail.manager', Illuminate\Contracts\Mail\Factory::class);
$app->alias('mailer', Illuminate\Mail\Mailer::class);
$app->alias('mailer', Illuminate\Contracts\Mail\Mailer::class);
$app->alias('mailer', Illuminate\Contracts\Mail\MailQueue::class);

$app->register(Laravel\Tinker\TinkerServiceProvider::class);
$app->register(Thedevsaddam\LumenRouteList\LumenRouteListServiceProvider::class);
$app->register(Fruitcake\Cors\CorsServiceProvider::class);
$app->middleware([Fruitcake\Cors\HandleCors::class]);

if (!$app->runningInConsole()) {
    $app->configure('newrelic');
    if (config('newrelic.enabled') || ($_GET['NEWRELIC_ENABLED'] ?? null) === 'true') {
        $app->middleware([
            \App\Http\Middleware\NewRelicMiddleware::class,
        ]);

        $app->register(\App\Providers\NewRelicServiceProvider::class);
    }
}

$app->router->group([], function (Router $router) {
    $router->group(['prefix' => ''], function (Router $router) {
        require __DIR__.'/../routes/service.php';
    });
    require __DIR__.'/../routes/web.php';
    require __DIR__.'/../routes/api-v3.php';
});

require __DIR__.'/../appV3/bootstrap.php';

return $app;
