<html>

<head>
    <title>Centrifugo quick start</title>
</head>

<body>
<div id="counter">-</div>
<script src="https://unpkg.com/centrifuge@3.1.0/dist/centrifuge.js"></script>
<script type="text/javascript">
    const container = document.getElementById('counter');

    const centrifuge = new Centrifuge("ws://localhost:8000/connection/websocket", {
        token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZTk2YzMyYy1mMGU2LTQ0YWMtODllYy1lM2UyZDBiYjc4ZTMifQ.rqtAzpMsyX7KePUDntPTZE9_00Tn35QdTr73Oirb_S4"
    });

    centrifuge.on('connecting', function (ctx) {
        console.log(`connecting: ${ctx.code}, ${ctx.reason}`);
    }).on('connected', function (ctx) {
        console.log(`connected over ${ctx.transport}`);
    }).on('disconnected', function (ctx) {
        console.log(`disconnected: ${ctx.code}, ${ctx.reason}`);
    }).connect();

    const sub = centrifuge.newSubscription("personal:#ae96c32c-f0e6-44ac-89ec-e3e2d0bb78e3");

    sub.on('publication', function (ctx) {
        container.innerHTML = (ctx.data.balance.amount / 100) + ctx.data.balance.currency;

        let date = new Date(Date.now());
        document.title = date.getHours() + ":" + date.getMinutes() + ":" + date.getSeconds();
    }).on('subscribing', function (ctx) {
        console.log(`subscribing: ${ctx.code}, ${ctx.reason}`);
    }).on('subscribed', function (ctx) {
        console.log('subscribed', ctx);
    }).on('unsubscribed', function (ctx) {
        console.log(`unsubscribed: ${ctx.code}, ${ctx.reason}`);
    }).subscribe();
</script>
</body>

</html>
