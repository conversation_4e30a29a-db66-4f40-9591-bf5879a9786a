<?php

namespace Tests;

use App\Repositories\RestPlayerRepository;
use App\Services\PlayerAppClient;
use PHPUnit\Framework\MockObject\MockObject;
use ReflectionClass;
use Skipper\Repository\Exceptions\RepositoryException;
use WhiteLabelAdmin\Entities\Player;

final class RestPlayerRepositoryTest extends TestCase
{
    private RestPlayerRepository $players;

    /**
     * @var PlayerAppClient|MockObject
     */
    private $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = $this->createMock(PlayerAppClient::class);
        $this->players = new RestPlayerRepository($this->client);
    }

    private function createPlayer(): Player
    {
        return new Player([
            'client_id' => 123,
            'id' => 1,
            'data' => 'data',
        ]);
    }

    public function testDelete(): void
    {
        $this->expectException(RepositoryException::class);
        $this->players->delete($this->createPlayer());
    }

    public function testCreate(): void
    {
        $this->expectException(RepositoryException::class);
        $player = $this->createPlayer();
        $r = new ReflectionClass($player);
        $prop = $r->getProperty('id');
        $prop->setAccessible(true);
        $prop->setValue($player, null);
        $this->players->save($player);
    }

    public function testSave(): void
    {
        $this->markTestSkipped(); //Skipped failed test.

        $player = $this->createPlayer();
        $player->setUpdated([
            'first_name' => 'newName',
            'last_name' => 'newSurname',
            'birth' => '1992',
            'email' => '<EMAIL>',
            'phone' => '+375-12-123-12-12',

            'blocked' => true,
            'casino' => false,
            'withdraw' => 111,
            'wager' => 0,
            'comment' => 'good boi',
            'fake' => false,
            'suspect' => true,

            'someRandomField' => 'lol',
        ]);

        $this->client->expects(self::once())
            ->method('updateProfile')
            ->with(1, [
                'first_name' => 'newName',
                'last_name' => 'newSurname',
                'birth' => '1992',
                'email' => '<EMAIL>',
                'phone' => '+375-12-123-12-12',
            ])
            ->willReturn(['data' => 'new']);
        $this->client->expects(self::once())
            ->method('updatePlayerMeta')
            ->with(1, [
                'blocked' => true,
                'casino' => false,
//                'withdraw' => 111,
//                'wager' => 0,
                'comment' => 'good boi',
                'fake' => false,
                'suspect' => true,
            ])
            ->willReturn(['data' => 'new_again']);

        self::assertTrue($this->players->save($player));

        self::assertEmpty($player->getUpdated());
    }

    public function testSearch(): void
    {
        $this->client->expects(self::once())
            ->method('playerSearch')
            ->with([
                'pagination' => [
                    'limit' => 1,
                    'offset' => 12,
                ],
                'ids' => '1,2,3',
                'blocked' => '1',
                'country' => 'US',
                'desc' => 'name',
                'asc' => 'email',
            ])
            ->willReturn(['data']);

        $data = $this->players->searchPlayers([
            'filter' => [
                'id' => [['operator' => 'in', 'value' => [1, 2, 3]]],
                'blocked' => [['value' => true]],
                'country' => [['value' => 'US']],
            ],
            'sort' => [
                'name' => 'desc',
                'email' => 'asc',
            ],
            'pagination' => [
                'limit' => 1,
                'offset' => 12,
            ],
        ]);

        self::assertEquals(['data'], $data);
    }
}
