<?php declare(strict_types=1);

namespace Tests;

use Lara<PERSON>\Lumen\Testing\TestCase as BaseTestCase;
use Prophecy\PhpUnit\ProphecyTrait;
use Illuminate\Contracts\Console\Kernel;

abstract class TestCase extends BaseTestCase
{
    use ProphecyTrait;

    /**
     * Creates the application.
     *
     * @return \Laravel\Lumen\Application
     */
    public function createApplication()
    {
        $app = require __DIR__.'/../bootstrap/app.php';

        $app->make(Kernel::class)->bootstrap();

        return $app;
    }
}
