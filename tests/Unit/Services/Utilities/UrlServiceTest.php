<?php

declare(strict_types=1);

namespace Tests\Unit\Services\Utilities;

use App\Services\Utilities\UrlService;
use PHPUnit\Framework\TestCase;

class UrlServiceTest extends TestCase
{
    private UrlService $urlService;

    protected function setUp(): void
    {
        $this->urlService = new UrlService();
    }

    public function testReplaceDomain(): void
    {
        $url = 'http://example.com/path?query=1';
        $newDomain = 'newdomain.com';
        $expected = 'http://newdomain.com/path?query=1';

        $result = $this->urlService->replaceDomain($url, $newDomain);

        $this->assertEquals($expected, $result);
    }

    public function testReplaceDomain2(): void
    {
        $url = 'http://example.com?query=1';
        $newDomain = 'newdomain.com';
        $expected = 'http://newdomain.com?query=1';

        $result = $this->urlService->replaceDomain($url, $newDomain);

        $this->assertEquals($expected, $result);
    }

    public function testReplaceDomainWithBrackets(): void
    {
        $url = 'http://example.com?query={click_id}';
        $newDomain = 'newdomain.com';
        $expected = 'http://newdomain.com?query={click_id}';

        $result = $this->urlService->replaceDomain($url, $newDomain);

        $this->assertEquals($expected, $result);
    }


    public function testReplaceDomainWithoutQuery(): void
    {
        $url = 'http://example.com/path';
        $newDomain = 'newdomain.com';
        $expected = 'http://newdomain.com/path';

        $result = $this->urlService->replaceDomain($url, $newDomain);

        $this->assertEquals($expected, $result);
    }

    public function testReplaceDomainWithoutPath(): void
    {
        $url = 'http://example.com';
        $newDomain = 'newdomain.com';
        $expected = 'http://newdomain.com';

        $result = $this->urlService->replaceDomain($url, $newDomain);

        $this->assertEquals($expected, $result);
    }

    public function testGetDomainFromUrl(): void
    {
        $url = 'http://example.com/path?query=1';
        $expected = 'example.com';

        $result = $this->urlService->getDomainFromUrl($url);

        $this->assertEquals($expected, $result);
    }
}
