<?php

namespace Tests\Unit\SmartLinks;

use App\Http\Clients\AlanBaseHttpClient;
use App\Http\Services\SmartLinkService;
use App\Models\SmartLinkLog;
use App\Repositories\SmartLinkChangeLogRepository;
use App\Services\Utilities\UrlService;
use AppV2\SmartLink\Dto\AlanbaseOfferDto;
use AppV2\SmartLink\Dto\SmartLinkHistoryDto;
use AppV2\SmartLink\Dto\SmartLinkHistoryResultDto;
use AppV2\SmartLink\Dto\SmartLinksPageDto;
use AppV2\SmartLink\Exceptions\SameDomainException;
use Faker\Factory;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use IntegratorCore\Services\Dto\ResponseOutputDto;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\TestCase;

final class SmartLinkServiceTest extends TestCase
{
    private SmartLinkService $service;
    private MockObject $changeLogRepository;
    private MockObject $alanBaseHttpClient;
    protected $faker;


    protected function setUp(): void
    {
        $this->faker = Factory::create();

        $this->changeLogRepository = $this->createMock(SmartLinkChangeLogRepository::class);
        $this->alanBaseHttpClient = $this->createMock(AlanBaseHttpClient::class);

        $this->service = new SmartLinkService(
            $this->changeLogRepository,
            $this->alanBaseHttpClient,
            new UrlService()
        );
    }

    public function testGetPage(): void
    {
        $page = 1;
        $perPage = 10;
        $mockContent = $this->mockedContent('getPage');

        $mockResponse = new ResponseOutputDto();
        $mockResponse->content = $mockContent;

        $this->alanBaseHttpClient
            ->expects($this->once())
            ->method('getByPage')
            ->with($page, $perPage)
            ->willReturn($mockResponse);

        $result = $this->service->getPage($page, $perPage);

        $this->assertInstanceOf(SmartLinksPageDto::class, $result);
        $this->assertEquals(1, $result->getTotal());
        $this->assertCount(1, $result->getItems());
        $this->assertEquals('Test', $result->getItems()[0]->getName());
    }

    public function testSearchUsingCacheByName(): void
    {
        $this->alanBaseHttpClient
            ->expects($this->once())
            ->method('getAll')
            ->willReturn($this->mockedContent('searchUsingCache'));

        $result = $this->service->searchUsingCache('right', 1, 10);

        $this->assertInstanceOf(SmartLinksPageDto::class, $result);
        $this->assertCount(1, $result->getItems());
        $this->assertEquals(2, $result->getItems()[0]->getId());
    }

    public function testSearchUsingCacheByUrl()
    {
        $this->alanBaseHttpClient
            ->expects($this->once())
            ->method('getAll')
            ->willReturn($this->mockedContent('searchUsingCache'));

        $result = $this->service->searchUsingCache('example', 1, 10);

        $this->assertInstanceOf(SmartLinksPageDto::class, $result);
        $this->assertCount(1, $result->getItems());
        $this->assertEquals(2, $result->getItems()[0]->getId());
    }

    public function testHistory(): void
    {
        $historyDto = new SmartLinkHistoryDto(1, '2024-12-12', '2024-12-15', 1);
        $mockItems = new EloquentCollection([
            new SmartLinkLog([
                'id' => 1,
                'smart_link_id' => 1,
                'name' => $this->faker->name,
                'old_value' => $this->faker->url,
                'new_value' => $this->faker->url,
                'old_domain' => $this->faker->domainName,
                'new_domain' => $this->faker->domainName,
                'admin_email' => $this->faker->email
            ]),
            new SmartLinkLog([
                'id' => 2,
                'smart_link_id' => 1,
                'name' => $this->faker->name,
                'old_value' => $this->faker->url,
                'new_value' => $this->faker->url,
                'old_domain' => $this->faker->domainName,
                'new_domain' => $this->faker->domainName,
                'admin_email' => $this->faker->email
            ]),
            new SmartLinkLog([
                'id' => 3,
                'smart_link_id' => 1,
                'name' => $this->faker->name,
                'old_value' => $this->faker->url,
                'new_value' => $this->faker->url,
                'old_domain' => $this->faker->domainName,
                'new_domain' => $this->faker->domainName,
                'admin_email' => $this->faker->email
            ])
        ]);
        $resultDto = new SmartLinkHistoryResultDto(1, 3, 3, $mockItems);

        $this->changeLogRepository
            ->expects($this->once())
            ->method('getByDto')
            ->with($historyDto)
            ->willReturn($resultDto);

        $result = $this->service->history($historyDto);

        $this->assertEquals($result, $resultDto);
    }

    public function testUpdateDomain(): void
    {
        $oldDomain = 'example.com';
        $newDomain = 'new-example.com';
        $adminEmail = $this->faker->email;

        $offer = new AlanbaseOfferDto([
            'id' => 1,
            'name' => 'Test',
            'poster' => $this->faker->name,
            'status' => 2,
            'privacy_level' => 3,
            'descriptions' => [],
            'hold_period' => 4,
            'target_link' => "https://$oldDomain/path?click_id={click_id}",
            'trafficback_url' => $this->faker->url,
            'postclick' => 5,
            'postclick_from' => [],
            'product' => [],
            'tracking_domain' => ['id' => 1],
            'tags' => [],
            'partners' => [],
            'landings' => [],
            'created_at' => $this->faker->dateTime->format('Y-m-d H:i:s'),
            'updated_at' => $this->faker->dateTime->format('Y-m-d H:i:s'),
            'conditions' => [],
            'individual_conditions' => [],
        ]);

        $updatedOffer = clone $offer;
        $updatedOffer->targetLink = "https://$newDomain/path?click_id={click_id}";

        $this->alanBaseHttpClient
            ->expects($this->once())
            ->method('getOfferById')
            ->with(1)
            ->willReturn($offer);

        $this->alanBaseHttpClient
            ->expects($this->once())
            ->method('updateDomain')
            ->with($offer, "https://$newDomain/path?click_id={click_id}")
            ->willReturn($updatedOffer);

        $this->changeLogRepository
            ->expects($this->once())
            ->method('add')
            ->with($offer, $oldDomain, $newDomain, "https://$newDomain/path?click_id={click_id}", $adminEmail);

        $result = $this->service->updateDomain(1, $newDomain, $adminEmail);

        $this->assertEquals($updatedOffer, $result);
    }

    public function testUpdateDomainSameDomain(): void
    {
        $oldDomain = 'example.com';
        $newDomain = 'example.com';

        $offer = new AlanbaseOfferDto([
            'id' => 1,
            'name' => 'Test',
            'poster' => $this->faker->name,
            'status' => 2,
            'privacy_level' => 3,
            'descriptions' => [],
            'hold_period' => 4,
            'target_link' => "https://$oldDomain/path?click_id={click_id}",
            'trafficback_url' => $this->faker->url,
            'postclick' => 5,
            'postclick_from' => [],
            'product' => [],
            'tracking_domain' => ['id' => 1],
            'tags' => [],
            'partners' => [],
            'landings' => [],
            'created_at' => $this->faker->dateTime->format('Y-m-d H:i:s'),
            'updated_at' => $this->faker->dateTime->format('Y-m-d H:i:s'),
            'conditions' => [],
            'individual_conditions' => [],
        ]);

        $this->alanBaseHttpClient
            ->expects($this->once())
            ->method('getOfferById')
            ->with(1)
            ->willReturn($offer);

        $this->expectException(SameDomainException::class);

        $this->service->updateDomain(1, $newDomain, $this->faker->email);
    }

    private function mockedContent(string $context): array
    {
        switch ($context) {
            case 'getPage':
                return [
                    'data' => [
                        [
                            'id' => 1,
                            'name' => 'Test',
                            'target_link' => 'http://example.com',
                            'tracking_domain' => ['id' => 1],
                            'privacy_level' => 1,
                        ]
                    ],
                    'meta' => [
                        'page' => 1,
                        'per_page' => 10,
                        'total_count' => 1,
                        'last_page' => 1,
                    ],
                ];

            case 'searchUsingCache':
                return [
                    [
                        'id' => 1,
                        'name' => 'Wrong',
                        'target_link' => 'http://wrong.com',
                        'tracking_domain' => ['id' => 1],
                        'privacy_level' => 1,
                    ],
                    [
                        'id' => 2,
                        'name' => 'It\' Right name',
                        'target_link' => 'http://Example.com',
                        'tracking_domain' => ['id' => 1],
                        'privacy_level' => 1,
                    ],
                ];
            case 'update':
                return [
                    'data' => [
                        'id' => 1,
                        'name' => 'Tested',
                        'target_link' => 'http://new-example.com',
                        'tracking_domain' => ['id' => 1],
                        'privacy_level' => 1,
                    ]
                ];

            default:
                throw new \InvalidArgumentException("Unknown mock context: $context");
        }
    }

}
