<?php declare(strict_types=1);

namespace Tests;

use App\Enums\InternalPayoutStatusEnum;
use App\Jobs\RetryPayoutChangeStatusJob;
use Carbon\Carbon;
use Illuminate\Contracts\Bus\Dispatcher;
use IntegratorCore\Services\Dto\ResponseOutputDto;
use Illuminate\Support\Facades\DB;
use App\Services\Payout\WithdrawOrderService;

class RetryPayoutChangeStatusJobTest extends TestCase
{
    protected array $retryPayout;
    protected int $internalPayoutId;
    protected string $withdrawOrderToken;

    protected function setUp(): void
    {
        parent::setUp();

        $this->retryPayout = [
            'payout_id'             => 682382,
            'is_fake'               => false,
            'order_number'          => '1607',
            'withdraw_order_token'  => 'l7KShsBgICzMOrw6goo7fHJoLozclbmc',
            'withdraw_method_token' => 'r0AF6K',
            'fake_result_success'   => true,
            'jwtData'               =>
                [
                    'aud' => 2,
                ],
        ];
        $this->internalPayoutId = 1;
        $this->withdrawOrderToken = 'l7KShsBgICzMOrw6goo7fHJoLozclbmc';
    }

    public function handleTestDataProvider(): array
    {
        return [
            [
                'checkInProgress'   => false,
                'payoutStatus'      => null,
                'adminPayoutStatus' => null,
                'retryStatus'       => null,
                'retryMessage'      => null,
            ],
            [
                'checkInProgress'   => false,
                'payoutStatus'      => null,
                'adminPayoutStatus' => null,
                'retryStatus'       => \App\Enums\PayoutServicePayoutStatusEnum::INVALID,
                'retryMessage'      => 'message',
            ],
            [
                'checkInProgress'   => true,
                'payoutStatus'      => InternalPayoutStatusEnum::PENDING,
                'adminPayoutStatus' => null,
                'retryStatus'       => null,
                'retryMessage'      => null,
            ],
            [
                'checkInProgress'   => false,
                'payoutStatus'      => null,
                'adminPayoutStatus' => null,
                'retryStatus'       => '422',
                'retryMessage'      => null,
            ],
            [
                'checkInProgress'            => true,
                'payoutStatus'               => InternalPayoutStatusEnum::PENDING,
                'adminPayoutStatus'          => null,
                'retryStatus'                => null,
                'retryMessage'               => null,
                'adminPayoutStatusUpdatedAt' => Carbon::now()->subHours(2),
            ],
        ];
    }

    /**
     * @dataProvider handleTestDataProvider
     *
     * @param bool        $checkInProgress
     * @param string|null $payoutStatus
     * @param string|null $adminPayoutStatus
     * @param string|null $retryStatus
     * @param string|null $retryMessage
     * @param Carbon|null $adminPayoutStatusUpdatedAt
     *
     * @return void
     */
    public function testHandle(bool $checkInProgress, ?string $payoutStatus, ?string $adminPayoutStatus, ?string $retryStatus, ?string $retryMessage, ?Carbon $adminPayoutStatusUpdatedAt = null): void
    {
        $this->markTestSkipped('This test is trying to use DB');

        $payoutClientProphecy = $this->prophesize(\App\Services\PayoutServiceHttpClient::class);
        $payoutClientProphecy->setJson($this->retryPayout)->willReturn($payoutClientProphecy);
        $dispatcherProphecy = $this->prophesize(Dispatcher::class);

        $withdrawOrderServiceProphecy = $this->prophesize(WithdrawOrderService::class);

        $payout = new \App\Models\Payout();
        $payout->id = 1;
        $payout->status = $payoutStatus;
        $withdrawOrderServiceProphecy->getInternalPayout($this->withdrawOrderToken)->willReturn($payout);

        $adminPayoutStatus = new \App\Models\PayoutStatus();
        $adminPayoutStatus->id = 1;
        $adminPayoutStatus->status = $adminPayoutStatus;
        if ($adminPayoutStatusUpdatedAt === null) {
            $adminPayoutStatus->updated_at = Carbon::now();
        } else {
            $withdrawOrderServiceProphecy->updateInternalPayoutStatus(
                \Prophecy\Argument::any(),
                InternalPayoutStatusEnum::UNDEFINED_ERROR,
                \Prophecy\Argument::any(),
                \Prophecy\Argument::any(),
            )->shouldBeCalledOnce();
        }

        $withdrawOrderServiceProphecy->getInternalPayoutStatus(\Prophecy\Argument::any())->willReturn($adminPayoutStatus);

        $response = new ResponseOutputDto();
        if ($retryStatus !== null) {
            if ($retryStatus === '422') {
                $response->isSuccess = false;
                $payoutClientProphecy->doPostRequest('admin/withdraw/order/retry')->willReturn($response);

                $withdrawOrderServiceProphecy->updateInternalPayoutStatus(
                    \Prophecy\Argument::any(),
                    InternalPayoutStatusEnum::RETRY_FAILED,
                    \Prophecy\Argument::any(),
                    \Prophecy\Argument::any(),
                )->shouldBeCalledOnce();
            } else {
                $response->content = [
                    'status' => $retryStatus
                ];
                $payoutClientProphecy->doPostRequest('admin/withdraw/order/retry')->willReturn($response);

                $withdrawOrderServiceProphecy->updateInternalPayoutStatus(
                    \Prophecy\Argument::any(),
                    \Prophecy\Argument::any(),
                    \Prophecy\Argument::any(),
                    \Prophecy\Argument::any()
                )->shouldBeCalledOnce();
            }
        } else if ($checkInProgress === false) {
            $payoutClientProphecy->doPostRequest('admin/withdraw/order/retry')->willReturn($response);
            $dispatcherProphecy->dispatch(\Prophecy\Argument::any())->shouldBeCalledOnce();
        }

        $job = new RetryPayoutChangeStatusJob($this->internalPayoutId, $this->withdrawOrderToken, $this->retryPayout, $checkInProgress);
        $job->onConnection('sync');

        if ($payoutStatus === InternalPayoutStatusEnum::PENDING) {
            $withdrawOrderServiceProphecy->deleteInternalPayoutStatus($adminPayoutStatus->id)->shouldBeCalledOnce();
        } else if ($retryStatus !== '422') {
            $withdrawOrderServiceProphecy->deleteInternalPayoutStatus(\Prophecy\Argument::any())->shouldNotBeCalled();
        }

        DB::beginTransaction();
        $job->handle($payoutClientProphecy->reveal(), $withdrawOrderServiceProphecy->reveal(), $dispatcherProphecy->reveal());
        DB::rollBack();
    }
}
