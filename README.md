# App backend CORE

## Description

App backend is the main core backend and manages all users and admins actions.

## Installation

This project is a part of our docker-compose multi-container application, that contains all backend infrastructure projects and instruction to installation: https://gitlab.deversin.com/white-label/docker-backend-services

### Run migrations
* `php artisan migrate` - default migrations
* `php artisan migrate --database=mysql_casino --path=database/migrations_casino` - migrations for casino database

### Run unit tests
* `./vendor/bin/phpunit` - default
* `./vendor/bin/phpunit tests/appV3/Core/Cache/CacheManagerTest.php` - specific test file
* `./vendor/bin/phpunit --filter testGetWithFallbackFunction` - specific test function


### Helpfully information and commands:
* Main .env file configs:
  * `DB_DATABASE` and `DB_DATABASE_CASINO` - this project need to have two separated databases: main and for casino logic
  * `ADMIN_CLIENT_ID` - This ID uses for identify admin`s bearer tokens. By default, admin-backend project configured by CLIENT_ID=1, and all players have value =2. But if you want give to all users admins privilege for testing - set this value = 2
  * `LOGSTASH_ENABLED` - set value = true if you want to use ELK for logging
  * `LOG_DATABASE_QUERY` - set value = true if you want to log all database query
  * `LOG_DATABASE_QUERY_PERFORMANCE` - set value = true if you want to log all database query statistics
  * `PASSPORT_PUBLIC_KEY_BASE64` & `PASSPORT_PRIVATE_KEY_BASE64` - you can use it instead of storing keys in files inside storage folder
* Code:
  * Always use helper function `get_current_cluster_connection()` instead if explicitly connection name because of sharding
* Commands:
  * `php artisan queue:work redis --queue=name` - run queue worker

## Infra

### php
* php >=7.4
* ext_pdo
* ext_opcache
* ext_openssl
* ext_mongo
* ext_bcmath
* the rest should be already in php
* `public` - document root
* nginx to route request 1.12.1, publish 80 port
* mysql >=5.7
* redis 5.0

### The service will not work without:
* mysql
* redis
* CCG (registration and authorization will not work)
* mirrors-monitoring-service (redirect to mirrors will not work, the application will not know which mirrors are currently active, which mirrors are linked to the user)

### External services will not be able to receive data without:
* kafka (sending data to several services will not work (Lex - pull out a list))
* centrifugo (balance updating will not work)
* Statistics Service
* Bonus Managment Service (Smartico)
* Payment Service
* Payout Service
* playa
* elk
* mobivate
* openexchangerates
* google metrics
* Adapters:
  * tvbet
  * eventbet
  * playngo
  * gamicorp
  * slotegrator
  * aviatrix
  * betby

### Will not affect the operation of the service:
* Admin Backend
