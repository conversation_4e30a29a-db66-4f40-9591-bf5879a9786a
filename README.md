# Docker Backend Services

## Table of contents

- [Getting started](#getting-started)
- [<PERSON><PERSON><PERSON>](#modules)
    - [Deversin](#deversin)
    - [Kafka](#kafka)
    - [ELK](#elk)
- [Installation](#installation)
- [PHPStorm xdebug setup](#phpstorm-xdebug-setup)

## Getting started

This repository contains docker configurations for deploy backend projects on local machine. Each module has own network, you can see all existing nets with `docker network ls`. Each network contains several containers, and some containers belongs with several networks for interactions with modules. You can inspect network with `docker network inspect NETWORK_NAME`. Therefore, you can address between containers using the container names, because of mutual network.

## Modules

### Deversin

This folder contains docker-compose.yml file for deploying all backend projects with common environment.
Each project has own dockerfile in `build` directory and must be located in `src` folder. General config for each php container for xdebug located in `etc/php/conf.d/xdebug.ini`

This module also contains nginx with config for each project in `etc/nginx/conf.d`. All logs will be written to appropriate file in `ect/nginx/log`

The database will be used locally, because it needs a stock mysql 5.7+ database (you can use 8.0+) 

### Kafka

Default Kafka package, just build it with `docker compose up -d --build`

### ELK

Default ELK package, just build it with `docker compose up -d --build`

## Installation

1. In the `Deversin/docker-compose.yml` file, you can comment out the comment-signed lines with the subnet config if you do not need to install Kafka or ELK to some project`s containers
2. Build containers for Kafka and ELK (if they need for at least one project)
3. Create `src` in `Deversin/` and pull in it `app-backend`, `frontend-oauth-service`, `admin-backend` projects or delete configuration for unnecessary projects.
From `Deversin/src/` run: `docker-compose build` and `docker-compose up -d`
4. Create all the necessary databases locally, save them in .env files
5. For app-backend ask your colleagues to get `oauth-private.key` and `oauth-public.key` and put them into `storage/`, copy `geo-ip2-country.mmdb` file to `storage/app/`.For `frondend-oauth-service` copy `geo-ip2-country.mmdb` file to `storage/app/`
6. At first install you might run `composer install --ignore-platform-req=ext-newrelic --ignore-platform-req=ext-rdkafka` or just comment them in composer.json and run install. Do not forget to uncomment them after.
7. Configs for `.env` for app-backend and admin-backend:
    - Mysql 
        ```dotenv
        DB_HOST=host.docker.internal
        DB_PORT=3306
        ```
    - Redis
      ```dotenv
       REDIS_HOST=redis
        ```
    - Kafka
      ```dotenv
       KAFKA_DSN=rdkafka://kafka0:9092
       KAFKA_METADATA_BROKER_LIST=kafka0:9092
        ```
    - ELK
      ```dotenv
       LOGSTASH_ENABLED=true
       ELK_HOSTELK_HOST=logstash
       ELK_PORT=12201
       ELK_TRANSPORT_PROTOCOL=udp
        ```
8. Get dump of db from https://confluence.deversin.com/display/NEW/Optimized+Dump+30.06.2023 for app-backend
9. In ELK, you need to add an index for parsing. To do this, send any one log with the correct .env config, then go to `http://localhost:5601/app/management/kibana/indexPatterns/patterns` (login=elastic, password=changeme), add a pattern for the proposed file with log in the form of `lumen-whitelabel-api-local-*` (this is for the app-backend). After that, you can go to Discover and watch logs
10. Add to your local machine host file (for mac it located in `/etc/hosts`):
   ```
   127.0.0.1 admin.whitelabel.dd
   127.0.0.1 app.whitelabel.dd
   127.0.0.1 oauth.whitelabel.dd
   127.0.0.1 payment.whitelabel.dd
   127.0.0.1 bonus-management.whitelabel.dd
   ```

### PHPStorm xdebug setup

In PhpStorm, you need to create server for debugger. Go to Preferences->Php->Servers and create server used following params:
  - Host `app.whitelabel.dd` (this is example for app-backend)
  - Port `80`
  - Use path mapping `true`
  - Absolute path on the server `/var/www/html` => file directory = path to this project on your local machine
