# WhiteLabel Admin App

## Overview
Admin Backend is a management system designed to provide full control over the casino platform. It enables administrators to configure payment methods, manage bonuses, process payouts, monitor player activity, and apply restrictions such as blocking players and setting or removing labels. Additionally, the system offers tools for moderating slots, providers, and sections, allowing sorting, activation, and deactivation.

Here is a revised version of the text with improved context:

---

## Features

- **Payment Management**: Create and configure various payment methods for players.
- **Bonus Management**: Create and assign bonuses to individual players or multiple users.
- **Payout Processing**: Efficiently handle player withdrawal requests.
- **Player Management**: Track player activity, apply restrictions, and manage player tags.
- **Content Moderation**: Sort, enable, or disable slots, providers, and sections as needed.
- **Permission Management**: Assign roles and permissions to other users.

---

## System Requirements

### PHP Requirements

- PHP version `>= 7.4`
- Required PHP extensions:
    - `ext_pdo`
    - `ext_opcache`
    - `ext_openssl`
    - `ext_bcmath`
    - `ext_mongo`
- The rest of the required extensions should already be present in PHP.
- Document root should be set to the `public` directory.

### Infrastructure

- **Web Server**: Nginx (version `1.12.1`), with port `80` exposed to route incoming requests.
- **Database**:
    - MySQL `>= 5.7` for data storage.
    - MongoDB `4.4.1` for NoSQL storage.
- **(Optional)**: Redis `5.0` for caching and queue management.
- **Internal Services**:
    - App Backend.
    - Payout.
    - Payment.
    - Payment Manager.
    - KYCAID.
    - CCG.
    - Bonus Management (Smartico).

---

## Installation
1. Clone the repository:
   ```sh
   <NAME_EMAIL>:white-label/admin-backend.git
   ```
2. Navigate to the project directory:
   ```sh
   cd admin-backend
   ```
3. Install dependencies:
   ```sh
   composer install
   ```
4. Set up the environment variables:
   ```sh
   cp .env.example .env
   ```
5. Update the `.env` file with the required configuration settings.
   ```php
    #admin mysql connection
    DB_CONNECTION=mysql
    DB_HOST=
    DB_PORT=3306
    DB_DATABASE=backend_admin
    DB_USERNAME=
    DB_PASSWORD=
   
    #core mysql connection
    DB_CONNECTION_APP_BACKEND=mysql-app
    DB_HOST_APP_BACKEND=
    DB_PORT_APP_BACKEND=3306
    DB_DATABASE_APP_BACKEND=
    DB_USERNAME_APP_BACKEND=
    DB_PASSWORD_APP_BACKEND=
   
    #payout service host
    PAYOUT_HOST=https://payout.deversin.com
    #payment service host
    PAYMENT_HOST=https://payment.deversin.com/
    #payment-managment service host
    PAYMENT_MANAGEMENT_HOST=http://payment-manager.whitelabel.dd/
    #kycaid service host
    VERIFICATION_SERVICE_API_URL=http://verification.whitelabel.dd
    #ccg host
    CORE_CLUSTER_GATEWAY_HOST=https://ccg.deversin.com
    #bonus managment host
    BONUS_MANAGEMENT_HOST=http://bonus-management.deversin.com
    #free spin host
    FREE_SPIN_HOST=https://slotegrator-bets.deversin.com
    #admin host
    OAUTH_HOST=http://admin.whitelabel.dd
    #core host
    PLAYER_HOST=http://app.whitelabel.dd/
   ```
   
6. Run migrations and seed the database:
   ```sh
   php artisan migrate --seed
   ```
7. Set up oauth keys:
   ```sh
   php artisan passport:keys --force
   ```
8. Create app client:
   ```sh
   php artisan passport:client --client
   ```
9. Change PLAYER_CLIENT* with props you get from app
    ```php
    PLAYER_CLIENT_ID=
    PLAYER_CLIENT_SECRET=
    APP_CLIENT_ID=
    ```
10. Create a client for a back/front-end: 
   ```sh
   php artisan passport:client --password
   ```
11. Change OAUTH_CLIENT_* with props you get from app
   ```php
    OAUTH_CLIENT_ID=
    OAUTH_CLIENT_SECRET=
   ```
12. Match your oauth clients with end-user clients
   ```sh
   php artisan passport:grant
   ```
13. Create admin user for the client (OAUTH_CLIENT_ID)
   ```sh
   php artisan passport:admin
   ```
14. Assign permissions to admin
   ```sh
   php artisan permission:set-all-for-admin
   ```

---

## Route
### Proxy endpoints to core(proxy.php) 
```php
<?php
namespace App\Services;

use App\Enums\MiddlewareEnum;
use WhiteLabelAdmin\Entities\PermissionList;

final class RoutePermitter
{
    /**
     * @return AllowedRoute[]
     */
    public function getRoutes(): array
    {
        return [
            ...
            new AllowedRoute('[METHOD]', '[CORE ENDPOINT]', [PERMISSION],[MIDDLEWARE]),
            ...
        ];
    }
}
```

---

## Add Permission
The new permission must be added to the following list, its assignment is managed through the [admin panel](https://4ra-admin.deversin.com/settings/roles) and configured through the front.
```php
<?php

namespace WhiteLabelAdmin\Entities;

interface PermissionList
{
    ...
    public const PERMISSION_CONSTANT_NAME = 'permission-name';
    ...
}

```

---

## Run tests
PHPUnit is used to write the unit tests, to run the tests run the following command after composer install is done.
```sh
  >./vendor/bin/phpunit
```

---

## Swagger Documentation

- **Swagger YAML File Path**: `./public/swagger.yaml`
- **API Documentation URL**: [http://admin.whitelabel.dd/](http://admin.whitelabel.dd/)

---

## QueueRateLimiter Middleware
- **Usage Documentation**: [QueueRateLimiter Middleware](https://confluence.deversin.com/display/NEW/QueueRateLimiter+Middleware)
