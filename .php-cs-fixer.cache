{"php": "8.3.20", "version": "3.64.0:v3.64.0#58dd9c931c785a79739310aef5178928305ffa67", "indent": "    ", "lineEnding": "\n", "rules": {"binary_operator_spaces": {"default": "at_least_single_space"}, "blank_line_after_opening_tag": true, "blank_line_between_import_groups": true, "blank_lines_before_namespace": true, "braces_position": {"allow_single_line_empty_anonymous_classes": true}, "class_definition": {"inline_constructor_arguments": false, "space_before_parenthesis": true}, "compact_nullable_type_declaration": true, "declare_equal_normalize": true, "lowercase_cast": true, "lowercase_static_reference": true, "new_with_parentheses": true, "no_blank_lines_after_class_opening": true, "no_extra_blank_lines": {"tokens": ["use"]}, "no_leading_import_slash": true, "no_whitespace_in_blank_line": true, "ordered_class_elements": {"order": ["use_trait"]}, "ordered_imports": {"imports_order": ["class", "function", "const"], "sort_algorithm": "none"}, "return_type_declaration": true, "short_scalar_cast": true, "single_import_per_statement": {"group_to_single_imports": false}, "single_space_around_construct": {"constructs_followed_by_a_single_space": ["abstract", "as", "case", "catch", "class", "const_import", "do", "else", "elseif", "final", "finally", "for", "foreach", "function", "function_import", "if", "insteadof", "interface", "namespace", "new", "private", "protected", "public", "static", "switch", "trait", "try", "use", "use_lambda", "while"], "constructs_preceded_by_a_single_space": ["as", "else", "elseif", "use_lambda"]}, "single_trait_insert_per_statement": true, "ternary_operator_spaces": true, "unary_operator_spaces": {"only_dec_inc": true}, "visibility_required": true, "blank_line_after_namespace": true, "constant_case": true, "control_structure_braces": true, "control_structure_continuation_position": true, "elseif": true, "function_declaration": true, "indentation_type": true, "line_ending": true, "lowercase_keywords": true, "method_argument_space": {"attribute_placement": "ignore", "on_multiline": "ensure_fully_multiline"}, "no_break_comment": true, "no_closing_tag": true, "no_multiple_statements_per_line": true, "no_space_around_double_colon": true, "no_spaces_after_function_name": true, "no_trailing_whitespace": true, "no_trailing_whitespace_in_comment": true, "single_blank_line_at_eof": true, "single_class_element_per_statement": {"elements": ["property"]}, "single_line_after_imports": true, "spaces_inside_parentheses": true, "statement_indentation": true, "switch_case_semicolon_to_colon": true, "switch_case_space": true, "encoding": true, "full_opening_tag": true}, "hashes": {".docker-backend-services/Deversin/docker-compose.yml": "24da5a84d13765af81fb626f6399a672"}}