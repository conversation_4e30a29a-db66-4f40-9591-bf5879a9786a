{"name": "whitelabel/app", "require": {"php": "^7.4", "doctrine/annotations": "^1.10", "doctrine/cache": "^1.10", "dusterio/lumen-passport": "^0.3.1", "illuminate/mail": "^8.7", "illuminate/redis": "^8.7", "laravel/lumen-framework": "^8.0", "league/oauth2-client": "^2.5", "moneyphp/money": "^3.3", "mongodb/mongodb": "^1.7", "predis/predis": "^1.1", "skipper/base-api": "dev-master", "skipper/exceptions": "dev-master", "skipper/repository": "dev-master", "skipper/search": "dev-master", "skipper/strategies": "dev-master", "symfony/intl": "^5.1", "symfony/lock": "^5.1", "symfony/validator": "^5.1", "zircote/swagger-php": "^3.1"}, "require-dev": {"fzaninotto/faker": "^1.9.1", "laravel/tinker": "^2.4", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^9.3"}, "autoload": {"psr-4": {"App\\": "app/", "WhiteLabelApp\\": "src/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"classmap": ["tests/"]}, "config": {"preferred-install": "dist", "sort-packages": true, "secure-http": false, "optimize-autoloader": true}, "repositories": [{"type": "vcs", "url": "********************:internal-dependencies/repository.git"}, {"type": "vcs", "url": "********************:internal-dependencies/exceptions.git"}, {"type": "vcs", "url": "********************:internal-dependencies/search.git"}, {"type": "vcs", "url": "********************:internal-dependencies/base-api.git"}, {"type": "vcs", "url": "********************:internal-dependencies/strategies.git"}], "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}