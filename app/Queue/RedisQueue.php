<?php

namespace App\Queue;

use App\Jobs\RateLimiterPipeline\QueueRateLimiterInterface;
use App\Jobs\RateLimiterPipeline\QueueRateLimiterMiddleware;
use Illuminate\Queue\LuaScripts;
use Illuminate\Support\Facades\Redis;

class RedisQueue extends \Illuminate\Queue\RedisQueue
{
    public function deleteAndRelease($queue, $job, $delay)
    {
        $queue = $this->getQueue($queue);

        $this->getConnection()->eval(
            LuaScripts::release(), 2, $queue.':delayed', $queue.':reserved',
            $this->getReservedJob($job), $this->availableAt($delay)
        );
    }

    private function getReservedJob($job): string
    {
        $payload = $job->payload();

        if (!$this->isJobInstanceOfQueueRateLimiter($payload)) {
            return $job->getReservedJob();
        }

        if ($this->hasExceededException($job)) {
            return $job->getReservedJob();
        }

        return json_encode($payload);
    }

    private function isJobInstanceOfQueueRateLimiter(array $payload): bool
    {
        return isset($payload['displayName']) && is_subclass_of($payload['displayName'], QueueRateLimiterInterface::class);
    }

    private function hasExceededException($job): bool
    {
        return (bool) Redis::get(QueueRateLimiterMiddleware::jobExceptionCounterKey($job->getJobId()));
    }
}
