<?php

namespace App\Providers;

use App\Auth\PassportScopeRepository;
use App\Models\User;
use Dusterio\LumenPassport\LumenPassport;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Laravel\Passport\Bridge\ScopeRepository;
use WhiteLabelAdmin\Entities\PermissionList;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(ScopeRepository::class, PassportScopeRepository::class);
    }

    /**
     * Boot the authentication services for the application.
     *
     * @return void
     */
    public function boot()
    {
        LumenPassport::routes($this->app, ['prefix' => '/api/v1/oauth']);
        LumenPassport::allowMultipleTokens();
        LumenPassport::tokensExpireIn(Carbon::now()->addMinutes(config('common.passport.token.expires_in', 5)));

        Gate::before(function (User $user, string $ability) {
            if (null === $user->role_id) {
                return false;
            }
            return in_array($ability, $user->role->getRolePermissions() ?? []) || $user->isAdmin();
        });
    }
}
