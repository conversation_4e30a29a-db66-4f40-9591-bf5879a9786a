<?php

namespace App\Providers;

use App\Factories\HttpRequestFactory;
use App\Http\Clients\AlanBaseHttpClient;
use App\Repositories\AccessToken\ClusterConnectionAccessTokenRepository;
use App\Repositories\EloquentRoleRepository;
use App\Repositories\EloquentUserRepository;
use App\Repositories\RestCountryRepository;
use App\Repositories\RestCurrencyRepository;
use App\Repositories\RestPaymentRepository;
use App\Repositories\RestPlayerRepository;
use App\Repositories\RestSettingsRepository;
use App\Services\AuthHttpService;
use App\Services\BannerManagementServiceHttpClient;
use App\Services\Payment\PaymentClient;
use App\Services\PaymentManagementServiceHttpClient;
use App\Services\BonusManagementServiceHttpClient;
use App\Services\PaymentServiceHttpClient;
use App\Services\Payout\PayoutClient;
use App\Services\PayoutServiceHttpClient;
use App\Services\PlayerAppClient;
use App\Services\Statistics\StatisticsClient;
use App\Services\Utilities\DatabaseSyncManager;
use AppV2\Core\Http\Api\CoreClusterGatewayApiClient;
use AppV2\Core\Services\ClusterConnectionService;
use AppV2\Core\Services\PaymentSystemEncryptService;
use AppV2\Player\Http\ApiClients\VerificationApiClient;
use Deversin\Signature\SignatureInterface;
use Doctrine\Common\Annotations\AnnotationRegistry;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Client as GuzzleHttpClient;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\ServiceProvider;
use Laravel\Lumen\Application;
use Laravel\Passport\Bridge\AccessTokenRepository;
use MongoDB\Client;
use MongoDB\Collection;
use Psr\Http\Message\RequestInterface;
use Skipper\Search\Contracts\MetaGeneratorInterface;
use Skipper\Search\Contracts\SearchRouter;
use Skipper\Search\Integrations\Laravel\LumenSearchRouter;
use Skipper\Search\Integrations\Symfony\Validator;
use Skipper\Search\Services\MetaGenerator;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Repositories\ClientSettingsRepositoryInterface;
use WhiteLabelAdmin\Repositories\CountryRepositoryInterface;
use WhiteLabelAdmin\Repositories\CurrencyRepositoryInterface;
use WhiteLabelAdmin\Repositories\PaymentGatewayRepositoryInterface;
use WhiteLabelAdmin\Repositories\PlayerRepositoryInterface;
use WhiteLabelAdmin\Repositories\RoleRepositoryInterface;
use WhiteLabelAdmin\Repositories\UserRepositoryInterface;

/**
 * Class AppServiceProvider
 * @package App\Providers
 */
class AppServiceProvider extends ServiceProvider
{

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(ValidatorInterface::class, function () {
            AnnotationRegistry::registerLoader([require __DIR__ . '/../../vendor/autoload.php', 'loadClass']);

            return Validation::createValidatorBuilder()
                ->enableAnnotationMapping()
                ->getValidator();
        });
        $host = env('PAYMENT_HOST');

        $defaultOptions = [
            'base_uri' => $host,
            'timeout' => 10,
            'verify' => false,
            'http_errors' => false,
            'headers' => [
                'Accept' => 'application/json'
            ]
        ];

        $this->app->bind(PayoutClient::class, function () {
            return new PayoutClient(
                env('PAYOUT_HOST'),
                $this->app->make(SignatureInterface::class),
                [
                    'verify' => env('HTTP_VERIFY', true),
                ]);
        });

        $this->app->bind(PaymentClient::class, function () {
            return new PaymentClient(
                env('PAYMENT_HOST'),
                $this->app->make(SignatureInterface::class),
                [
                    'verify' => env('HTTP_VERIFY', true),
                ]);
        });

        $this->app->bind(AuthHttpService::class, function () {
            $client = new \GuzzleHttp\Client(
                [
                    'base_uri' => config('common.oauth_host'),
                ]
            );

            return new AuthHttpService($client);
        });

        $this->app->bind(PayoutServiceHttpClient::class, function () {
            $instance = new PayoutServiceHttpClient($this->getHttpClientInstance(config('external-services.payout.host')));
            $instance->enableLog();
            return $instance;
        });

        $this->app->bind(PaymentServiceHttpClient::class, function () {
            $instance = new PaymentServiceHttpClient($this->getHttpClientInstance(config('external-services.payment.host')));
            $instance->enableLog();
            return $instance;
        });

        $this->app->bind(PaymentManagementServiceHttpClient::class, function () {
            $instance = new PaymentManagementServiceHttpClient($this->getHttpClientInstance(config('external-services.payment_management.host')));
            $instance->enableLog();
            return $instance;
        });

        $this->app->bind(BonusManagementServiceHttpClient::class, function () {
            $instance = new BonusManagementServiceHttpClient($this->getHttpClientInstance(config('external-services.bonus_management.host')));
            $instance->enableLog();
            return $instance;
        });

        $this->app->bind(BannerManagementServiceHttpClient::class, function () {
            $client = $this->getHttpClientInstance(
                config('external-services.banner_management.host'),
                [
                    'Tenant-Token' => config('external-services.banner_management.tenant_token'),
                    'Accept' => 'application/json'
                ]
            );

            $instance = new BannerManagementServiceHttpClient($client);
            $instance->enableLog();
            return $instance;
        });

        $this->app->singleton(\Skipper\Search\Contracts\ValidatorInterface::class, Validator::class);
        $this->app->singleton(SearchRouter::class, LumenSearchRouter::class);
        $this->app->singleton(MetaGeneratorInterface::class, MetaGenerator::class);
        $this->app->singleton(RequestFactoryInterface::class, HttpRequestFactory::class);
        $this->app->singleton(RoleRepositoryInterface::class, EloquentRoleRepository::class);
        $this->app->singleton(UserRepositoryInterface::class, EloquentUserRepository::class);
        $this->app->singleton(PlayerRepositoryInterface::class, RestPlayerRepository::class);
        $this->app->singleton(CurrencyRepositoryInterface::class, RestCurrencyRepository::class);
        $this->app->singleton(CountryRepositoryInterface::class, RestCountryRepository::class);
        $this->app->singleton(PaymentGatewayRepositoryInterface::class, RestPaymentRepository::class);
        $this->app->singleton(ClientSettingsRepositoryInterface::class, RestSettingsRepository::class);

        $this->app->singleton(Collection::class, function () {
            $client = new Client(env('MONGO_DSN'));

            return $client->selectDatabase(env('MONGO_DB'))->selectCollection('pages');
        });

        $this->app->singleton(PlayerAppClient::class, function () {
            return new PlayerAppClient(
                env('PLAYER_HOST'),
                env('PLAYER_CLIENT_ID'),
                env('PLAYER_CLIENT_SECRET'),
                $this->app->make('cache')->driver(),
                $this->app->make(Request::class),
                $this->app->make(SignatureInterface::class),
                $this->app->make(ClusterConnectionService::class),
                [
                    'verify' => !env('APP_DEBUG'),
                ]
            );
        });

        $this->app->singleton(VerificationApiClient::class, function () {
            return new VerificationApiClient($this->getHttpClientInstance(config('external-services.verification.api_url', '')));
        });
        $this->app->singleton(CoreClusterGatewayApiClient::class, function () {
            return new CoreClusterGatewayApiClient($this->getHttpClientInstance(config('external-services.core_cluster_gateway.host', '')));
        });
        $this->app->singleton(AccessTokenRepository::class, function() {
            return $this->app->make(ClusterConnectionAccessTokenRepository::class);
        });
        $this->app->singleton(ClusterConnectionService::class);

        $this->app->singleton(DatabaseSyncManager::class, function(Application $app) {
            /** @var ClusterConnectionService $clusterService */
            $clusterService = $app->make(ClusterConnectionService::class);
            return new DatabaseSyncManager(
                config('database.sync.enabled'),
                $clusterService->getUserClusterConnection() ?? config('database.sync.default_connection'),
                $clusterService->getClusterSlugList(),
                config('database.sync.tables'),
            );
        });

        $this->app->singleton(PaymentSystemEncryptService::class);

        $this->app->singleton(StatisticsClient::class, function () {
            return new StatisticsClient(
                env('STATISTIC_HOST', ''),
                env('STATISTIC_AUTH_TOKEN', ''),
                ['verify' => env('HTTP_VERIFY', true)]
            );
        });

        $this->app->singleton(AlanBaseHttpClient::class, function () {
            return new AlanBaseHttpClient(
                new GuzzleHttpClient(
                    [
                        'base_uri' => config('common.alanbase.api_url'),
                        'headers' => [
                            'API-KEY' => config('common.alanbase.api_key'),
                            'Content-Type' => 'application/json',
                        ],
                    ]
                ),
            );
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        JsonResource::withoutWrapping();
    }

    protected function getHttpClientInstance(string $host, $headers = []): \GuzzleHttp\Client
    {
        $signatureService = $this->app->make(SignatureInterface::class);

        $stack = new HandlerStack();
        $stack->setHandler(new CurlHandler());

        $stack->push(function ($handler) use ($signatureService) {
            return static function (
                RequestInterface $request,
                array            $options
            ) use ($handler, $signatureService) {
                if (!$request->hasHeader(SignatureInterface::SIGNATURE_HEADER)) {
                    // Generating signature
                    $stream = $request->getBody();
                    $body = $stream->getContents();
                    if (empty($body)) {
                        $body = json_encode([],JSON_THROW_ON_ERROR);
                    }
                    $signature = $signatureService->sign($body);

                    $request = $request
                        ->withBody($stream)
                        ->withHeader(SignatureInterface::SIGNATURE_HEADER, $signature);
                }

                /** @var ClusterConnectionService $clusterConnectionService */
                $clusterConnectionService = app(ClusterConnectionService::class);
                $cluster = $clusterConnectionService->getUserClusterConnection();
                if ($cluster) {
                    $request = $request->withHeader(ClusterConnectionService::CLUSTER_CONNECTION_HEADER, $cluster);
                }

                return $handler($request, $options);
            };
        });

        return new \GuzzleHttp\Client(
            [
                'base_uri' => $host,
                'handler'  => $stack,
                'verify'   => config('external-services.http_verify'),
                'headers'  => $headers
            ]
        );
    }
}
