<?php

namespace App\Providers;

use App\Listeners\SqlListener;
use Illuminate\Database\Events\QueryExecuted;
use Lara<PERSON>\Lumen\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        QueryExecuted::class => [
            SqlListener::class,
        ],
    ];
}
