<?php declare(strict_types=1);


namespace App\Providers;


use App\Providers\Dto\JobInfoDto;
use Illuminate\Cache\RateLimiter;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobProcessing;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\ServiceProvider;
use Ramsey\Uuid\Uuid;

class JobServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->app->make('queue');

        Queue::before(function (JobProcessing $event) {
            $jobInfo = $this->getJobInfo($event->job);
            Log::info("Queue | $jobInfo->jobName | started", [
                'jobProperties'  => $jobInfo->properties,
                'setNewUniqueId' => Uuid::uuid4()->toString(),
            ]);
        });

        Queue::after(function (JobProcessed $event) {
            $jobInfo = $this->getJobInfo($event->job);
            if ($event->job->isReleased()) {
                $status = 'released';
            } else {
                $status = 'success completed';
            }
            Log::info("Queue | $jobInfo->jobName | $status", [
                'jobProperties' => $jobInfo->properties,
            ]);
        });

        Queue::failing(function (JobFailed $event) {
            $jobInfo = $this->getJobInfo($event->job);

            Log::error("Queue | $jobInfo->jobName | fail | {$event->exception->getMessage()}", [
                'jobProperties' => $jobInfo->properties,
                'trace' => $event->exception->getTraceAsString(),
            ]);
        });

        /**
         * @var RateLimiter $rateLimiter
         * @deprecated Delete it with the config queue.limits
         */
        $rateLimiter = $this->app->get(RateLimiter::class);
        $rateLimiter->for('oneTimeBonusJob', function () {
            return Limit::perMinute(config('queue.limits.one_time_bonus_job_per_minute'));
        });
    }

    /**
     * @throws \ReflectionException
     * @throws \JsonException
     */
    protected function getJobInfo(\Illuminate\Contracts\Queue\Job $job): JobInfoDto
    {
        $serializedJob = json_decode($job->getRawBody(), true, 512, JSON_THROW_ON_ERROR)['data']['command'];
        $job = unserialize($serializedJob, ['allowed_classes' => true]);

        $jobReflection = new \ReflectionClass($job);
        $jobName = $jobReflection->getShortName();
        $properties = $jobReflection->getProperties();

        $result = new JobInfoDto();
        foreach ($properties as $property) {
            $class = $property->getDeclaringClass()->getShortName();

            if ($class === $jobName) {
                $propertyName = $property->getName();
                $property->setAccessible(true);
                try {
                    $result->properties[$propertyName] = $property->getValue($job);
                } catch (\Throwable $e) {
                    $result->properties[$propertyName] = null;
                }
            }
        }

        $result->jobName = $jobName;

        return $result;
    }
}
