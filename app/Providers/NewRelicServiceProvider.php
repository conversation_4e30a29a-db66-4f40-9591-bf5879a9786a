<?php declare(strict_types=1);

namespace App\Providers;

use Intouch\Newrelic\Newrelic;
use Illuminate\Support\ServiceProvider;

class NewRelicServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton(Newrelic::class, function() {
            $newRelic = new Newrelic();
            $newRelic->setAppName(config('newrelic.app'), config('newrelic.license'));
            return $newRelic;
        });
    }

}

