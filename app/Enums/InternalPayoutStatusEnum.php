<?php declare(strict_types=1);

namespace App\Enums;

class InternalPayoutStatusEnum
{
    public const CREATED = 'created';
    public const PENDING = 'pending';
    public const APPROVED = 'approved';
    public const DECLINED = 'error';
    public const PLAYER_DECLINED = 'declined';
    public const ADMIN_DECLINE = 'rejected';
    public const CHANGING_AGGREGATOR = 'changing';
    public const CLOSED = 'closed';

    // Internal statuses on admin backend side
    public const IN_PROCESS = 'in_process';
    public const RETRY_FAILED = 'retry_failed';
    public const CLOSE_FAILED = 'close_failed';
    public const CLOSE_CORE_FAILED = 'close_core_failed';
    public const UNDEFINED_ERROR = 'undefined_error';
}
