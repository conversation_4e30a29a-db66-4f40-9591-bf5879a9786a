<?php

declare(strict_types=1);

namespace App\Enums\Payments;

use App\Core\Utils\Enum;

class PaymentStatusEnum extends Enum
{
    public const PENDING = 'pending';

    public const APPROVED = 'approved';
    public const BOOSTED = 'boosted';

    public const DECLINED = 'declined';

    public const CANCELLED = 'cancelled';

    public const CASHED_OUT = 'cashed_out';
    public const DEACTIVATED = 'deactivated';
    public const TRANSFERRED = 'transferred';
    public const BANKRUPT = 'bankrupt';
    public const EXPIRED = 'expired';
    public const WAITING = 'waiting';
}
