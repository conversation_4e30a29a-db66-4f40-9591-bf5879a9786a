<?php

declare(strict_types=1);

namespace App\Enums\Payments;

use App\Core\Utils\Enum;

class PaymentSourceEnum extends Enum
{
    public const BET_SOURCE = 'bet';
    public const WITHDRAW_LIMIT_SOURCE = 'limit';
    public const WAGER_SOURCE = 'wager';
    public const DEPOSIT_SOURCE = 'deposit';
    public const PAYOUT_SOURCE = 'withdraw';
    public const SLOT_SOURCE = 'slot';
    public const BONUS_TRANSFER_SOURCE = 'bonus_transfer';
    public const BONUS_GRANT_SOURCE = 'bonus_grant';
}
