<?php declare(strict_types=1);


namespace App\Core\Helpers;


use Money\Currencies\ISOCurrencies;
use Money\Currency;
use Money\Formatter\DecimalMoneyFormatter;
use Money\Money;
use Money\Parser\DecimalMoneyParser;

/**
 * Class MoneyHelper
 * @package App\Core\Helpers
 */
class MoneyHelper
{
    /**
     * @param int    $amount
     * @param string $currency
     *
     * @return float
     */
    public static function convertMoneyToFloat(int $amount, string $currency = 'USD'): float
    {
        $money = new Money($amount, new Currency($currency));
        $currencies = new ISOCurrencies();

        $moneyFormatter = new DecimalMoneyFormatter($currencies);

        return (float)$moneyFormatter->format($money);
    }

    /**
     * @param string|float $amount
     * @param string       $currency
     *
     * @return int
     */
    public static function convertMoneyToInteger($amount, string $currency = 'USD'): int
    {
        $currencies = new ISOCurrencies();
        $moneyParser = new DecimalMoneyParser($currencies);
        $money = $moneyParser->parse((string)$amount, new Currency($currency));

        return TypeHelper::convertValueToProbableType($money->getAmount());
    }
}
