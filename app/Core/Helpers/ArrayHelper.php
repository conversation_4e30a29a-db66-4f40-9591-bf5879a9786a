<?php declare(strict_types=1);


namespace App\Core\Helpers;


use Illuminate\Support\Str;

/**
 * Class ArrayHelper
 * @package App\Core\Helpers
 */
class ArrayHelper
{
    /**
     * @param $array
     *
     * @return array
     */
    public static function convertKeysToCamelCase($array): array
    {
        $arr = [];
        foreach ($array as $key => $value) {
            $key = Str::camel($key);

            if (is_array($value)) {
                $value = self::convertKeysToCamelCase($value);
            }

            $arr[$key] = $value;
        }
        return $arr;
    }

    /**
     * @param $array
     *
     * @return array
     */
    public static function convertKeysToSnakeCase($array): array
    {
        $arr = [];
        foreach ($array as $key => $value) {
            $key = Str::snake($key);

            if (is_array($value)) {
                $value = self::convertKeysToSnakeCase($value);
            }

            $arr[$key] = $value;
        }
        return $arr;
    }
}
