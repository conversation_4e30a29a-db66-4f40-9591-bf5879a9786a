<?php

declare(strict_types=1);

namespace App\Core\Exceptions;

class ExceptionDTO
{
    protected string $class;
    protected int $code;
    protected string $message;

    public function __construct(
        string $class,
        int $code,
        string $message
    ) {
        $this->class = $class;
        $this->code = $code;
        $this->message = $message;
    }

    public function getClass(): string
    {
        return $this->class;
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function getMessage(): string
    {
        return $this->message;
    }
}
