<?php

declare(strict_types=1);

namespace App\Core\Utils;

use InvalidArgumentException;
use JsonSerializable;
use Stringable;

abstract class Enum implements JsonSerializable, Stringable
{
    private string $state;

    /**
     * @throws InvalidArgumentException
     */
    public function __construct(string $state)
    {
        $r = new \ReflectionClass(static::class);
        if (false === in_array($state, $r->getConstants())) {
            throw new InvalidArgumentException($r->getShortName(), $state, [
                'allowed' => $r->getConstants(),
            ]);
        }

        $this->state = $state;
    }

    public static function getValues(): array
    {
        return (new \ReflectionClass(static::class))->getConstants();
    }

    /**
     * @return string
     */
    public function getState(): string
    {
        return $this->state;
    }

    public function jsonSerialize(): string
    {
        return $this->state;
    }

    public function __toString(): string
    {
        return $this->state;
    }

    public static function cases(): array
    {
        return array_values(self::getValues());
    }
}
