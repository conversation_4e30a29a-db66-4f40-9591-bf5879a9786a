<?php declare(strict_types=1);

namespace App\Core\Validator;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Support\Facades\Validator as BaseValidator;
use IntegratorCore\Services\Dto\DtoInterface;
use RuntimeException;

/**
 * Class ValidationService
 * @package App\Common\Validator
 */
class ValidationService extends BaseValidator
{
    protected Validator $validator;

    /**
     * @param string|integer|bool|float|array|DtoInterface $data
     * @param string|array $config
     *
     * @return Validator
     */
    public function createValidator($data, $config): Validator
    {
        if (!is_array($config)) {
            if (is_string($config)) {
                $config = new $config;
            }
            if (!$config instanceof ValidatorConfigInterface) {
                throw new RuntimeException('Wrong validator config', 500);
            }

            $config = $config();
        }

        return $this::make((array)$data, $config);
    }
}
