<?php declare(strict_types=1);

namespace App\Models;

use App\Models\Traits\HasAppBackendCluster;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PaymentsBlacklist
 * @package App\Models
 * @property int $player_id
 * @property int $payout_id
 * @property string $ip_address
 * @property mixed $blacklist_id
 */
final class PaymentsBlacklist extends Model
{
    use HasAppBackendCluster;

    protected $connection = 'mysql-app';
    protected $table = 'payments_requisites_blacklist';
    protected $fillable = ['player_id', 'payout_id'];

    public $timestamps = false;
}
