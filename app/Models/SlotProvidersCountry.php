<?php

namespace App\Models;

use App\Models\Traits\HasAppBackendCluster;
use App\Models\Traits\HasUuid;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class SlotProvidersCountry
 * @package App\Models
 * @property int id
 * @property int country_id
 * @property int slot_provider_id
 * @property bool is_enabled
 */
class SlotProvidersCountry extends Pivot
{
    use HasAppBackendCluster;
    use HasUuid;

    /**
     * @var string
     */
    protected $connection = 'mysql-app';

    /**
     * @var string
     */
    protected $table = 'country_slots_providers';

    public $timestamps = false;

    /**
     * @var string
     */
    protected $primaryKey = 'id';

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }
}
