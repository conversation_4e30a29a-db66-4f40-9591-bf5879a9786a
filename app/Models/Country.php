<?php
namespace App\Models;

use App\Models\Traits\HasAppBackendCluster;
use App\Models\Traits\HasUuid;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Country
 * @package App\Models
 * @property int id
 * @property int client_id
 * @property string iso_code
 * @property string|null name
 * @property int|null default_currency_id
 * @property bool registration_forbidden
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 */
class Country extends Model
{
    use HasAppBackendCluster;
    use HasUuid;

    /**
     * @var string
     */
    protected $connection = 'mysql-app';

    /**
     * @var string
     */
    protected $table = 'countries';

}
