<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @package App\Models
 * @property int $id
 * @property int $client_id
 * @property string $provider
 * @property string $provider_company
 * @property string $sub_id
 * @property string $access_token
 * @property string $game_token
 * @property bool $is_approved
 * @property bool $is_slot
 * @property \DateTimeInterface $created_at
 * @property \DateTimeInterface $updated_at
 */
final class DataSubscription extends Model
{
    protected $table = 'data_subscriptions';

    protected $connection = 'mysql-app';
    protected $casts = [
        'client_id' => 'integer',
        'is_approved' => 'boolean',
    ];
}
