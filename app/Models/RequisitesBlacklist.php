<?php declare(strict_types=1);


namespace App\Models;

use App\Models\Traits\HasAppBackendCluster;
use App\Models\Traits\HasUuid;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class RequisitesBlacklist
 * @package App\Models
 * @property int $id
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 * @property DateTimeInterface $deleted_at
 * @property int|mixed $admin_id
 * @property int|mixed $delete_admin_id
 * @property mixed|string $requisite
 * @property mixed|string $comment
 */

class RequisitesBlacklist extends Model
{
    use SoftDeletes;
    use HasAppBackendCluster;
    use HasUuid;

    /**
     * @var string
     */
    protected $connection = 'mysql-app';
    /**
     * @var string
     */
    protected $table = 'requisites_blacklist';

    protected $dates = ['deleted_at'];

    protected $fillable = [
        'id',
        'admin_id',
        'delete_admin_id',
        'requisite',
        'comment',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * @return HasOne
     */
    public function adminInfo(): HasOne
    {
        return $this->setConnection('mysql')->hasOne(User::class, 'id', 'admin_id')->without('role');
    }

}
