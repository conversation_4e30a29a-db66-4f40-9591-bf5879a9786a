<?php declare(strict_types=1);


namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $payouts_id
 * @property int $payouts_system_status
 * @property string|null $transaction_token
 * @property int|null $player_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $message
 * @property string|null $status
 */
class PayoutStatus extends Model
{
    /**
     * @var string
     */
    protected $table = 'payout_statuses';
    /**
     * @var string
     */
    protected $primaryKey = 'id';
    /**
     * @var string[]
     */
    protected $fillable = [
        'id',
        'payouts_id',
        'payouts_system_status',
        'status',
        'transaction_token',
        'player_id',
        'message',
        'created_at',
        'updated_at',
    ];
}
