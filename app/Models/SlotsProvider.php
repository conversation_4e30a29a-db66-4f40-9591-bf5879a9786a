<?php

namespace App\Models;

use App\Models\Traits\HasAppBackendCluster;
use App\Models\Traits\HasUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class SlotProvider
 * @package App\Models
 * @property int id
 * @property int client_id
 * @property string name
 * @property int section_id
 * @property string image
 * @property boolean suspended
 * @property-read int slots_count
 */
final class SlotsProvider extends Model
{
    use HasAppBackendCluster;
    use HasUuid;

    /**
     * @var string
     */
    protected $connection = 'mysql-app';
    /**
     * @var string
     */
    protected $table = 'slots_providers';

    /**
     * @return HasMany
     */
    public function getProviders(): HasMany
    {
        return $this->hasMany(Slots::class, 'external_provider_id', 'id');
    }

    public function countries(): BelongsToMany
    {
        return $this->belongsToMany(Country::class, 'country_slots_providers', 'slot_provider_id', 'country_id')
            ->using(SlotProvidersCountry::class)
            ->withPivot(['id', 'slot_provider_id', 'country_id', 'is_enabled']);
    }

}
