<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use WhiteLabelAdmin\Entities\Role as RoleEntity;

/**
 * Class Role
 * @package App\Models
 * @property int $client_id
 * @property int $id
 * @property string $name
 * @property \DateTimeInterface $created_at
 * @property \DateTimeInterface $updated_at
 * @property Collection $permissions
 */
final class Role extends Model
{
    protected $table = 'roles';
    protected $primaryKey = 'id';
    protected $casts = [
        'client_id' => 'integer',
    ];
    protected $with = [
        'permissions',
    ];

    /**
     * @return BelongsToMany
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'roles_permissions', 'role_id', 'permission_id', 'id');
    }

    /**
     * @return array
     */
    public function getRolePermissions(): array
    {
        return $this->permissions->pluck('name')->toArray();
    }

    public function delete()
    {
        $this->users()->update([
            'role_id' => null,
        ]);

        return parent::delete();
    }

    /**
     * @return HasMany
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'role_id', 'id');
    }

    /**
     * @return RoleEntity
     */
    public function toEntity(): RoleEntity
    {
        $role = new RoleEntity($this->client_id, $this->name, $this->getRolePermissions());
        $role->setId($this->id);

        return $role;
    }

}
