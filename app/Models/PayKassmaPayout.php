<?php
namespace App\Models;

use App\Models\Traits\HasAppBackendCluster;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PayKassmaPayout
 * @package App\Models
 * @property int $id
 * @property int $player_id
 * @property int $amount
 * @property string $currency
 * @property string $wallet_type
 * @property array $details
 * @property string|null $transaction_id
 * @property string $status
 * @property \DateTimeInterface $created_at
 * @property \DateTimeInterface $updated_at
 */
final class PayKassmaPayout extends Model
{
    use HasAppBackendCluster;

    protected $connection = 'mysql-app';
    protected $table = 'paykassma_payouts';
    protected $primaryKey = 'id';
    protected $casts = [
        'player_id' => 'integer',
        'amount' => 'integer',
        'details' => 'array',
    ];

}
