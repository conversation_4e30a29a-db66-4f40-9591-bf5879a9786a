<?php


namespace App\Models;


use App\Models\Traits\HasAppBackendCluster;
use App\Models\Traits\HasUuid;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SegmentStatistics
 * @package App\Models
 * @property int $id
 * @property int $external_id
 * @property string $name
 * @property bool $is_active
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 */
class SegmentStatistics extends Model
{
    use HasAppBackendCluster;
    use HasUuid;

    /**
     * @var string
     */
    protected $connection = 'mysql-app';

    /**
     * @var string
     */
    protected $table = 'segments_statistics';

    /**
     * @var string
     */
    protected $primaryKey = 'id';
}
