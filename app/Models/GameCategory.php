<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use WhiteLabelAdmin\Entities\GameCategory as GameCategoryEntity;

/**
 * Class GameCategory
 * @package App\Models
 * @property int client_id
 * @property string slug
 * @property string type
 * @property string name
 * @property bool enabled
 * @property int weight
 */
final class GameCategory extends Model
{
    protected $table = 'categories';
    protected $primaryKey = 'id';
    protected $casts  = [
        'client_id' => 'integer',
        'weight' => 'integer',
        'enabled' => 'boolean',
    ];

    public function toEntity(): GameCategoryEntity
    {
        $e = new GameCategoryEntity($this->client_id, $this->slug, $this->type);
        $e->setWeight($this->weight)
            ->setEnabled($this->enabled)
            ->setName($this->name);

        return $e;
    }

    public function fromEntity(GameCategoryEntity $e): void
    {
        $this->client_id = $e->getClientId();
        $this->slug = $e->getSlug();
        $this->type = $e->getType();
        $this->weight = $e->getWeight();
        $this->enabled = $e->isEnabled();
        $this->name = $e->getName();
    }
}
