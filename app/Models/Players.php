<?php


namespace App\Models;


use App\Models\Traits\HasAppBackendCluster;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Players
 * @package App\Models
 * @property int $id
 * @property int $client_id
 * @property int|null welcome_bonus_id
 * @property string $password
 * @property string $username
 * @property string $email
 * @property string $uuid
 * @property DateTimeInterface|null $email_verified_at
 * @property string|null $phone
 * @property string|null $phone_code
 * @property DateTimeInterface|null $phone_verified_at
 * @property string $first_name
 * @property string|null $last_name
 * @property DateTimeInterface|null $birth
 * @property string|null $country
 * @property string|null $city
 * @property string|null $language
 * @property string|null $click_id
 * @property string|null $gender
 * @property int|null bonus_balance_id
 * @property string $currency
 * @property string $last_seen_ip
 * @property bool $subscribed
 * @property bool $is_under_moderation
 * @property bool $blocked
 * @property string $comment
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 */
class Players extends Model
{
    use HasAppBackendCluster;
    /**
     * @var string
     */
    protected $connection = 'mysql-app';

    protected $hidden = ['password'];
    /**
     * @var string
     */
    protected $table = 'players';

    /**
     * @var string
     */
    protected $primaryKey = 'id';
}
