<?php

namespace App\Models;

use App\Models\Traits\HasAppBackendCluster;
use App\Models\Traits\HasUuid;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Bonuses
 * @package App\Models
 * @property int $id
 * @property int $client_id
 * @property string $type
 * @property string $name
 * @property array $bonuses
 * @property array $max_transfers
 * @property array $deposit_factors
 * @property string $bet
 * @property string $currency
 * @property int $transfer
 * @property array $bonus_name
 * @property array $description
 * @property array $condition
 * @property int $duration
 * @property string $image
 * @property int $min_deposit
 * @property int $min_bet
 * @property float $wager
 * @property bool $active
 * @property bool $casino
 * @property bool $bets
 * @property array $data
 * @property DateTimeInterface $active_from
 * @property DateTimeInterface $active_til
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 */
class Bonuses extends Model
{
    use HasAppBackendCluster;
    use HasUuid;

    /**
     * @var string
     */
    protected $connection = 'mysql-app';
    /**
     * @var string
     */
    protected $table = 'bonuses';
    /**
     * @var string
     */
    protected $primaryKey = 'id';

    protected $dates = [
        'active_from',
        'active_til',
    ];

    protected $casts = [
        'bonuses' => 'array',
        'max_transfers' => 'array',
        'deposit_factors' => 'array',
    ];

    const ONE_TIME = 'onetime';
    const FREE_BET = 'freebet';
}
