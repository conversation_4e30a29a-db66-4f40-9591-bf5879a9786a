<?php


namespace App\Models;


use App\Models\Traits\HasAppBackendCluster;
use App\Models\Traits\HasUuid;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * Class DataSubscriptions
 * @package App\Models
 * @property int $id
 * @property int $client_id
 * @property string $provider
 * @property string $sub_id
 * @property string $access_token
 * @property string $game_token
 * @property bool $is_approved
 * @property bool $is_slot
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 */
class DataSubscriptions extends Model
{
    use HasAppBackendCluster;
    use HasUuid;

    /**
     * @var string
     */
    protected $connection = 'mysql-app';
    /**
     * @var string
     */
    protected $table = 'data_subscriptions';
    /**
     * @var string
     */
    protected $primaryKey = 'id';

}
