<?php

namespace App\Models\Traits;

use App\Services\Utilities\DatabaseSyncManager;
use AppV2\Core\Services\ClusterConnectionService;

trait HasAppBackendCluster
{
    public function __construct(array $attributes = [])
    {
        $this->connection = $this->getClusterConnection();

        parent::__construct($attributes);
    }

    public function getClusterConnection(): string
    {
        /** @var ClusterConnectionService $clusterService */
        $clusterService = app(ClusterConnectionService::class);
        /** @var DatabaseSyncManager $databaseSyncManager */
        $databaseSyncManager = app(DatabaseSyncManager::class);

        $connection = $clusterService->getUserClusterConnection();
        if (!$connection) {
            $connection = $databaseSyncManager->getDefaultConnection();
        }

        return $connection;
    }
}
