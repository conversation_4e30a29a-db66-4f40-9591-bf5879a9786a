<?php


namespace App\Models;


use App\Models\Traits\HasAppBackendCluster;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class Payout
 * @package App\Models
 * @property int $id
 * @property int $player_id
 * @property int $amount
 * @property string $currency
 * @property string $status
 * @property string $gateway
 * @property string|null $aggregator
 * @property string $id_hash
 * @property string $transaction_id
 * @property string $transaction_token
 * @property string $log_key
 * @property string $comment
 * @property \DateTimeInterface $action_at
 * @property \DateTimeInterface $created_at
 * @property \DateTimeInterface $updated_at
 */
class Payout extends Model
{
    use HasAppBackendCluster;

    /**
     * @var string
     */
    protected $connection = 'mysql-app';
    /**
     * @var string
     */
    protected $table = 'payouts';
    /**
     * @var string
     */
    protected $primaryKey = 'id';
    /**
     * @var string[]
     */
    protected $fillable = [
        'id',
        'player_id',
        'amount',
        'currency',
        'status',
        'gateway',
        'aggregator',
        'id_hash',
        'transaction_id',
        'transaction_token',
        'log_key',
        'comment',
        'action_at',
        'created_at',
        'updated_at',
    ];

    public function detail(): BelongsTo
    {
        return $this->belongsTo(PayKassmaPayout::class, 'transaction_id', 'id');
    }
}
