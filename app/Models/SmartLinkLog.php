<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $smart_link_id
 * @property string $name
 * @property string $old_value
 * @property string $new_value
 * @property string $old_domain
 * @property string $new_domain
 * @property string $admin_email
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class SmartLinkLog extends Model
{
    protected $fillable = [
        'smart_link_id',
        'name',
        'old_value',
        'new_value',
        'old_domain',
        'new_domain',
        'admin_email',
    ];
}
