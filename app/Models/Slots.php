<?php


namespace App\Models;


use App\Models\Traits\HasAppBackendCluster;
use App\Models\Traits\HasUuid;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Slots
 * @package App\Models
 * @property int id
 * @property int client_id
 * @property string provider
 * @property int version
 * @property int external_provider_id
 * @property string name
 * @property string slug
 * @property string internal_id
 * @property string external_id
 * @property bool enabled
 * @property bool suspended
 * @property array meta
 * @property string image
 * @property string description
 * @property bool is_mobile
 * @property bool is_bonus_ready
 * @property bool is_wager_ready
 * @property bool is_desktop
 * @property bool has_lobby
 * @property DateTimeInterface created_at
 * @property DateTimeInterface updated_at
 * @property DateTimeInterface deleted_at
 */
class Slots extends Model
{
    use HasAppBackendCluster;
    use HasUuid;

    /**
     * @var string
     */
    protected $connection = 'mysql-app';
    /**
     * @var string
     */
    protected $table = 'slots';
    /**
     * @var string
     */
    protected $primaryKey = 'id';

}
