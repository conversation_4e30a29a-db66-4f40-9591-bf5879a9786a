<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class RolePermission
 * @package App\Models\
 * @property int $role_id
 * @property int $permission_id
 */
final class RolePermission extends Model
{
    protected $primaryKey = 'id';
    protected $table = 'roles_permissions';
    public $timestamps = false;

    /**
     * @param int $roleId
     * @return array
     */
    public static function getPermissionsArray(int $roleId): array
    {
        $userPermissions = self::select('permissions.name')
            ->leftjoin('permissions', 'permissions.id', '=', 'roles_permissions.permission_id')
            ->where('role_id', $roleId)
            ->where('name', 'LIKE', '%-col-%')
            ->get()->toArray();

        return array_column($userPermissions,'name');
    }
}
