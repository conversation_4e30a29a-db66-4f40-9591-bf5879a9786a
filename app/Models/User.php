<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Lumen\Auth\Authorizable;
use Laravel\Passport\HasApiTokens;
use WhiteLabelAdmin\Entities\PermissionList;

/**
 * Class User
 * @package App\Models
 * @property int $id
 * @property int $client_id
 * @property int $role_id
 * @property Role $role
 * @property string $login
 * @property string $email
 * @property string $password
 * @property string $last_seen_ip
 * @property DateTimeInterface $created_at
 * @property DateTimeInterface $updated_at
 * @property-read bool $is_admin
 */
class User extends Model implements AuthenticatableContract, AuthorizableContract
{
    use Authenticatable;
    use Authorizable;
    use HasApiTokens;
    use SoftDeletes;
    use HasFactory;

    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $hidden = ['password'];
    protected $casts = [
        'role_id' => 'integer',
        'client_id' => 'integer',
    ];
    protected $with = [
        'role',
    ];
    protected $fillable = [
        'last_seen_ip',
    ];

    /**
     * @return bool|null
     * @throws \Exception
     */
    public function delete()
    {
        $this->role_id = null;

        return parent::delete();
    }

    /**
     * @return bool
     */
    public function getIsAdminAttribute(): bool
    {
        return $this->isAdmin();
    }

    /**
     * @return bool
     */
    public function isAdmin(): bool
    {
        return in_array(PermissionList::ADMIN, $this->role->getRolePermissions());
    }

    /**
     * @return BelongsTo
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }
}
