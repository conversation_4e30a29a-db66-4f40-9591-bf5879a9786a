<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Role
 * @package App\Models
 * @property int $id
 * @property string $type
 * @property string $name
 * @property string $params
 * @property \DateTimeInterface $created_at
 * @property \DateTimeInterface $updated_at
 */
final class SearchTemplate extends Model
{
    protected $fillable = [
        'type',
        'name',
        'params'
    ];

    protected $casts = [
        'params' => 'array'
    ];
}
