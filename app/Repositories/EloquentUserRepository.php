<?php
namespace App\Repositories;

use App\Models\User as UserModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\DataTransferObjects\Filter;
use Skipper\Repository\Integrations\Laravel\EloquentRepository;
use WhiteLabelAdmin\Entities\User;
use WhiteLabelAdmin\Repositories\UserRepositoryInterface;

final class EloquentUserRepository extends EloquentRepository implements UserRepositoryInterface
{
    /**
     * @return string
     */
    public function getModelClassName(): string
    {
        return UserModel::class;
    }

    /**
     * @param Model|UserModel $model
     * @param Entity|User $entity
     */
    protected function mapModel(Model $model, Entity $entity): void
    {
        $model->client_id = $entity->getClientId();
        $model->email = $entity->getEmail();
        $model->role_id = $entity->getRole() ? $entity->getRole()->getId() : null;
        $model->login = $entity->getLogin();
        $model->password = $entity->getPassword();
        $model->last_seen_ip = $entity->getLastSeenIp();
    }

    /**
     * @return string[]
     */
    protected function getColumnMapping(): array
    {
        return [
            'clientId' => 'client_id',
            'client' => 'client_id',
            'role' => 'role_id',
            'roleId' => 'role_id',
            'trashed' => function (Builder $builder, Filter $filter) {
                if ($filter->getValue()) {
                    $builder->withTrashed();
                }
            },
            'roleName' => function (Builder $builder, Filter $filter) {
                $builder->whereHas('role', function (Builder $query) use ($filter) {
                    $query->where('name', $this->getSqlOperator($filter->getOperator()), $filter->getValue());
                });
            },
            'lastSeenIp' => 'last_seen_ip',
            'ip' => 'last_seen_ip',
        ];
    }

    /**
     * @param Model|UserModel $model
     * @return Entity|User
     */
    protected function mapModelToEntity(Model $model): User
    {
        $user = new User($model->client_id, $model->login, $model->email, $model->password);
        $user->setLastSeenIp($model->last_seen_ip)
            ->setRole($model->role ? $model->role->toEntity() : null)
            ->setId($model->id);

        return $user;
    }

    /**
     * @param int $userId
     * @param int $clientId
     * @return User
     * @throws \Skipper\Repository\Exceptions\EntityNotFoundException
     */
    public function getUserByIdAndClientId(int $userId, int $clientId): User
    {
        return $this->findOneBy([
            'filter' => [
                'clientId' => [['value' => $clientId]],
                'id' => [['value' => $userId]],
            ],
        ]);
    }
}
