<?php

namespace App\Repositories;

use App\Models\SlotProvidersCountry;
use App\Models\SlotsProvider;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Collection;

/**
 * Class SlotProviderRepository
 * @package App\Repositories
 */
class SlotProviderRepository
{
    /**
     * @return SlotsProvider[] | Collection
     */
    public function getSlotProvidersOutsideCountry(int $countryId): Collection
    {
        return SlotsProvider::query()
            ->whereDoesntHave('countries', function (Builder $builder) use ($countryId) {
                $builder->where('countries.id', $countryId);
            })->get();
    }

    /**
     * @throws ModelNotFoundException
     */
    public function updateIsEnabledParam(int $slotProviderId, int $countryId, bool $isEnabled): bool
    {
        $slotProviderCountry = SlotProvidersCountry::query()
            ->where('slot_provider_id', $slotProviderId)
            ->where('country_id', $countryId)
            ->firstOrFail();

        return $slotProviderCountry->update(['is_enabled' => $isEnabled]);
    }

    /**
     * @throws ModelNotFoundException
     */
    public function deleteCountrySlotProviderByCountry(int $countryId): bool
    {
        return  SlotProvidersCountry::query()->where('country_id',$countryId)->delete();
    }
}
