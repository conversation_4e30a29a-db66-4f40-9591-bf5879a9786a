<?php
namespace App\Repositories;

use App\Models\Permission;
use App\Models\Role as RoleModel;
use Illuminate\Cache\Repository;
use Illuminate\Contracts\Cache\Factory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\DataTransferObjects\Filter;
use Skipper\Repository\Integrations\Laravel\EloquentRepository;
use WhiteLabelAdmin\Entities\PermissionList;
use WhiteLabelAdmin\Entities\Role;
use WhiteLabelAdmin\Repositories\RoleRepositoryInterface;

final class EloquentRoleRepository extends EloquentRepository implements RoleRepositoryInterface
{
    /**
     * @var Factory
     */
    private Factory $cache;

    public function __construct(Factory $cache)
    {
        $this->cache = $cache;
        parent::__construct();
    }

    /**
     * @return string
     */
    public function getModelClassName(): string
    {
        return  RoleModel::class;
    }

    /**
     * @param Model|RoleModel $model
     * @param Entity|Role $entity
     */
    protected function mapModel(Model $model, Entity $entity): void
    {
        $model->name = $entity->getName();
        $model->client_id = $entity->getClientId();
    }

    /**
     * @return string[]
     */
    protected function getColumnMapping(): array
    {
        return [
            'clientId' => 'client_id',
            'client' => 'client_id',
            'permissions' => function (Builder $builder, Filter $filter) {
                $builder->whereHas('permissions', function (Builder $builder) use ($filter) {
                    if (is_array($filter->getValue())) {
                        $builder->whereIn('name', $filter->getValue());
                    } else {
                        $builder->where('name', $this->getSqlOperator($filter->getOperator()), $filter->getValue());
                    }
                });
            },
        ];
    }

    /**
     * @param Model|RoleModel $model
     * @return Entity|Role
     */
    protected function mapModelToEntity(Model $model): Role
    {
        return $model->toEntity();
    }

    /**
     * @param Role $role
     * @param string[] $permissionNames
     * @return Role
     * @throws \Skipper\Repository\Exceptions\EntityNotFoundException
     */
    public function updateRolePermissions(Role $role, array $permissionNames): Role
    {
        /** @var Collection $permissions */
        $permissions = Permission::whereIn('name', $permissionNames)->get();
        /** @var RoleModel $roleModel */
        $roleModel = $this->findModelById($role->getId());
        $roleModel->permissions()->sync($permissions);
        $newRole = new Role($role->getClientId(), $role->getName(), $permissions->pluck('name')->toArray());
        $newRole->setId($role->getId());

        return $newRole;
    }

    /**
     * @return string[]
     */
    public function getAllPermissions(): array
    {
        return $this->cache->store()->remember('permissions', 300, function () {
            $p = (new \ReflectionClass(PermissionList::class))->getConstants();
            $stage = [];
            foreach ($p as $constant) {
                $parts = explode('-', $constant);
                $stage[ucfirst(reset($parts))][] = [
                    'id' => $constant,
                    'name' =>  implode(' ', array_map(fn($word) => ucfirst($word), $parts))
                ];
            }
            $result = [];
            foreach ($stage as $name => $permissions) {
                $result[] = [
                    'id' => null,
                    'name' => $name,
                    'permissions' => $permissions,
                ];
            }

            return $result;
        });
    }
}
