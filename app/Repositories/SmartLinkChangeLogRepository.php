<?php

namespace App\Repositories;

use App\Models\SmartLinkLog;
use AppV2\SmartLink\Dto\AlanbaseOfferDto;
use AppV2\SmartLink\Dto\SmartLinkHistoryDto;
use AppV2\SmartLink\Dto\SmartLinkHistoryResultDto;
use Illuminate\Database\Eloquent\Collection;

class SmartLinkChangeLogRepository extends BaseRepository
{
    const HISTORY_PER_PAGE = 3;

    public function add(
        AlanbaseOfferDto $smartLink,
        string $oldDomain,
        string $newDomain,
        string $newTargetLink,
        string $adminEmail
    ): void {
        $model = new SmartLinkLog();

        $model->smart_link_id = $smartLink->id;
        $model->name = $smartLink->name;
        $model->old_value = $smartLink->targetLink;
        $model->new_value = $newTargetLink;
        $model->old_domain = $oldDomain;
        $model->new_domain = $newDomain;
        $model->admin_email = $adminEmail;

        $this->save($model);
    }

    public function getByDto(SmartLinkHistoryDto $dto): SmartLinkHistoryResultDto
    {
        $query = SmartLinkLog::query()
            ->where('smart_link_id', $dto->getOfferId());

        if ($dto->getDateFrom() !== null && $dto->getDateTo() !== null) {
            $query->whereBetween('created_at', [
                $dto->getDateFrom() . ' 00:00:00',
                $dto->getDateTo() . ' 23:59:59'
            ]);
        } elseif ($dto->getDateFrom() !== null) {
            $query->where('created_at', '>=', $dto->getDateFrom() . ' 00:00:00');
        } elseif ($dto->getDateTo() !== null) {
            $query->where('created_at', '<=', $dto->getDateTo() . ' 23:59:59');
        }

        $totalItems = $query->count();

        if ($totalItems === 0) {
            return new SmartLinkHistoryResultDto($dto->getPage(), self::HISTORY_PER_PAGE, 0, new Collection());
        }

        $logs = $query
            ->orderBy('created_at', 'desc')
            ->offset(($dto->getPage() - 1) * self::HISTORY_PER_PAGE)
            ->limit(self::HISTORY_PER_PAGE)
            ->get();

        return new SmartLinkHistoryResultDto($dto->getPage(), self::HISTORY_PER_PAGE, $totalItems, $logs);
    }
}
