<?php declare(strict_types=1);


namespace App\Repositories\RequisitesBlackList;

use App\Models\PaymentsBlacklist;
use App\Models\RequisitesBlacklist;
use Illuminate\Pagination\LengthAwarePaginator;

class RequisitesBlacklistRepository
{

    public function get(?string $requisite, ?array $pagination): LengthAwarePaginator
    {
        $query =  RequisitesBlacklist::query()->with(['adminInfo']);

        if (!is_null($requisite)) {
             $query->where('requisite', $requisite);
        }

        return  $query->orderBy('id', 'DESC')
            ->paginate($pagination['limit'], ['*'], 'page', $pagination['page']);
    }

    public function create(int $adminId, string $requisite, ?string $comment): int
    {
        $requisitesBlacklist = new RequisitesBlacklist();

        $requisitesBlacklist->admin_id = $adminId;
        $requisitesBlacklist->requisite = $requisite;
        $requisitesBlacklist->comment = $comment;

        $requisitesBlacklist->save();

        return $requisitesBlacklist->id;
    }

    public function destroy(int $deleteAdminId, $id): void
    {
        $requisite = RequisitesBlacklist::query()->findOrFail($id);

        $requisite->delete_admin_id = $deleteAdminId;

        $requisite->save();
        $requisite->delete();

        PaymentsBlacklist::query()->where('blacklist_id', $id)->delete();
    }

    public function saveIntoRequisiteBlacklist($playerId, $payoutId, $blacklistId): void
    {
        $paymentsRequisiteBlacklist = new PaymentsBlacklist();

        $paymentsRequisiteBlacklist->player_id = $playerId;
        $paymentsRequisiteBlacklist->payout_id = $payoutId;
        $paymentsRequisiteBlacklist->blacklist_id = $blacklistId;

        $paymentsRequisiteBlacklist->save();
    }
}
