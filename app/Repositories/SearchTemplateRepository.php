<?php

namespace App\Repositories;

use App\Http\Controllers\Dto\SearchTemplateSaveDto;
use App\Http\Controllers\Dto\SearchTemplateUpdateDto;
use App\Models\SearchTemplate;
use Illuminate\Database\Eloquent\Collection;

class SearchTemplateRepository
{
    public function findTemplatesByType(string $searchTemplateType): Collection
    {
        return SearchTemplate::query()->where('type', $searchTemplateType)->get();
    }

    public function insertNewTemplate(SearchTemplateSaveDto $searchTemplateSaveDto)
    {
        return SearchTemplate::query()
            ->create([
                'type' => $searchTemplateSaveDto->type,
                'name' => $searchTemplateSaveDto->name,
                'params' => $searchTemplateSaveDto->params
            ]);
    }

    public function updateNewTemplate(SearchTemplateUpdateDto $searchTemplateUpdateDto): bool
    {

        return SearchTemplate::query()
            ->where('id', $searchTemplateUpdateDto->id)
            ->update([
                'type' => $searchTemplateUpdateDto->type,
                'name' => $searchTemplateUpdateDto->name,
                'params' => $searchTemplateUpdateDto->params
            ]);
    }

    public function deleteNewTemplate(int $id): bool
    {
        return SearchTemplate::query()
            ->where('id', $id)
            ->delete();
    }
}
