<?php
namespace App\Repositories;

use App\Services\PlayerAppClient;
use Illuminate\Http\Request;
use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\CriteriaAwareRepository;
use WhiteLabelAdmin\Entities\Country;
use WhiteLabelAdmin\Repositories\CountryRepositoryInterface;

final class RestCountryRepository extends CriteriaAwareRepository implements CountryRepositoryInterface
{
    private PlayerAppClient $client;
    private Request $request;

    public function __construct(PlayerAppClient $client, Request $request)
    {
        $this->client = $client;
        $this->request = $request;
    }

    /**
     * @param Entity|Country $entity
     * @return bool
     */
    public function save(Entity $entity): bool
    {
        if (false === $entity->isAccessible()) {
            return $this->delete($entity);
        }

        $settings = $this->client->getSettings()['countries'];
        if (false !== array_search($entity->getIsoCode(), $settings)) {
            return true;
        }
        $settings[] = $entity->getIsoCode();
        $this->client->updateSettings([
            'countries' => $settings,
        ]);

        return true;
    }

    /**
     * @param Entity $entity
     * @return bool
     */
    public function delete(Entity $entity): bool
    {
        $settings = $this->client->getSettings()['countries'];
        if (false === $key = array_search($entity->getIsoCode(), $settings)) {
            return true;
        }
        unset($settings[$key]);
        $this->client->updateSettings([
            'countries' => $settings,
        ]);

        return true;
    }

    /**
     * @param array $criteria
     * @return Entity[]
     */
    public function findAll(array $criteria): array
    {
        /** @var Country[] $countries */
        $countries = array_map(\Closure::fromCallable([$this, 'fromIsoToEntity']), $this->client->getSettings()['countries']);
        foreach ($this->getFiltersFromCriteria($criteria) as $filter) {
            if ($filter->getColumn() === 'iso') {
                foreach ($countries as $country) {
                    if ($country->getIsoCode() === $filter->getValue()) {
                        return [$country];
                    }
                }
                return [];
            }
        }

        return $countries;
    }

    private function fromIsoToEntity(string $iso): Country
    {
        $e = new Country($iso, $this->request->attributes->get('token')->client->real_client_id);
        $e->setIsAccessible(true);

        return $e;
    }

    /**
     * @param array $criteria
     * @return int
     */
    public function count(array $criteria): int
    {
        return count($this->findAll($criteria));
    }
}
