<?php
namespace App\Repositories;

use App\Services\PlayerAppClient;
use Illuminate\Http\Request;
use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\CriteriaAwareRepository;
use WhiteLabelAdmin\Entities\SystemCurrency;
use WhiteLabelAdmin\Repositories\CurrencyRepositoryInterface;
use WhiteLabelAdmin\Services\CurrencyInfoService;

final class RestCurrencyRepository extends CriteriaAwareRepository implements CurrencyRepositoryInterface
{
    private PlayerAppClient $client;
    private CurrencyInfoService $currencyService;
    private Request $request;

    public function __construct(PlayerAppClient $client, CurrencyInfoService $currencyService, Request $request)
    {
        $this->client = $client;
        $this->currencyService = $currencyService;
        $this->request = $request;
    }

    /**
     * @param string $iso
     * @param int $clientId
     * @return SystemCurrency
     */
    public function findByIsoCodeAndClientId(string $iso, int $clientId): SystemCurrency
    {
        return $this->findOneBy([
            'filter' => [
                'iso' => [['value' => $iso]],
            ],
        ]);
    }

    /**
     * @param Entity|SystemCurrency $entity
     * @return bool
     */
    public function save(Entity $entity): bool
    {
        if (false === $entity->isAvailableForRegister()) {
            return $this->delete($entity);
        }
        $settings = $this->client->getSettings()['currencies'];
        $settings[$entity->getIsoCode()] = [
            'min_bet' => $entity->getMinBet()->getAmount(),
            'max_win' => $entity->getMaxWin()->getAmount(),
        ];

        $this->client->updateSettings([
            'currencies' => $settings,
        ]);

        return true;
    }

    /**
     * @param Entity|SystemCurrency $entity
     * @return bool
     */
    public function delete(Entity $entity): bool
    {
        $settings = $this->client->getSettings()['currencies'];
        if (false === array_key_exists($entity->getIsoCode(), $settings)) {
            return true;
        }

        unset($settings[$entity->getIsoCode()]);
        $this->client->updateSettings([
            'currencies' => $settings,
        ]);

        return true;
    }

    /**
     * @param array $criteria
     * @return Entity[]
     */
    public function findAll(array $criteria): array
    {
        foreach ($this->getFiltersFromCriteria($criteria) as $filter) {
            if ($filter->getColumn() === 'iso') {
                $isoCode = $filter->getValue();
            }
        }

        $settings = $this->client->getSettings()['currencies'];
        foreach ($settings as $iso => $setting) {
             $tmp = $this->fromArrayToEntity($iso, $setting);
             if ($isoCode ?? false) {
                 return $tmp->getIsoCode() === $isoCode ? [$tmp] : [];
             }
            $entities[] = $tmp;
        }

        return $entities ?? [];
    }

    private function fromArrayToEntity(string $iso, array $settings): SystemCurrency
    {
        $e = new SystemCurrency($iso,  $this->request->attributes->get('token')->client->real_client_id);
        $e->setIsAvailableForRegister(true)
            ->setMaxWin($settings['max_win'])
            ->setMinBet($settings['min_bet'])
            ->setName($this->currencyService->getName($e->getCurrency()));

        return $e;
    }

    /**
     * @param array $criteria
     * @return int
     */
    public function count(array $criteria): int
    {
        return count($this->findAll($criteria));
    }
}
