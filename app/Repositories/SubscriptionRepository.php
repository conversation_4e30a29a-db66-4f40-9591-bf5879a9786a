<?php

namespace App\Repositories;

use App\Models\DataSubscription;
use Illuminate\Support\Collection;

class SubscriptionRepository
{
    public function getSubscriptionsByNames(array $subscriptionNames): Collection
    {
        return DataSubscription::query()->whereIn('provider', $subscriptionNames)->get();
    }

    public function getSubscriptionNameById(int $subscriptionId): string
    {
        $subscription = DataSubscription::query()->where('id', $subscriptionId)->firstOrFail();

        return $subscription->provider;
    }
}
