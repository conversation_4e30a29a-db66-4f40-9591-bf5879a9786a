<?php

namespace App\Repositories\FreeSpins;

use App\Models\Slots;

/**
 * Class SlotsRepository
 * @package App\Repositories\FreeSpins
 */
class SlotsRepository
{
    /**
     * @var Slots
     */
    private Slots $model;

    /**
     * SlotsRepository constructor.
     * @param Slots $model
     */
    public function __construct(Slots $model)
    {
        $this->model = $model;

    }

    /**
     * @param $provider
     * @param $external_id
     * @param $slots_id
     * @return mixed
     */
    public function getBySlotsID($provider, $external_id, $slots_id)
    {
        return $this->model::select($provider, $external_id)->where('id', $slots_id)->first();
    }


    /**
     * @param $provider_id
     * @return mixed
     */
    public function getGames($provider_id)
    {
        return $this->model::select('id', 'name', 'external_id')->where(['external_provider_id' => $provider_id, 'suspended' => 0, 'enabled' => 1, 'has_freespins' => 1])->get();
    }
}
