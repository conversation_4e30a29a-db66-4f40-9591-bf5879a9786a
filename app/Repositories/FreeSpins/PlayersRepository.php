<?php

namespace App\Repositories\FreeSpins;

use App\Models\Players;

/**
 * Class PlayersRepository
 * @package App\Repositories\FreeSpins
 */
class PlayersRepository
{
    /**
     * @var Players
     */
    private Players $model;

    /**
     * PlayersRepository constructor.
     * @param Players $model
     */
    public function __construct(Players $model)
    {
        $this->model = $model;
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getByUserID($id)
    {
        return $this->model->select('username')->where('id', $id)->first();
    }

    /**
     * @param $uuid
     * @return mixed
     */
    public function getUserByUUID($uuid)
    {
        return $this->model->query()->where('uuid', $uuid)->first();
    }

}
