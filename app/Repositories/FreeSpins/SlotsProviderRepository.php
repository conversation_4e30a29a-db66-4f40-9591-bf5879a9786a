<?php

namespace App\Repositories\FreeSpins;

use App\Dto\FreeSpinsAvailableAggregatorDto;
use App\Dto\FreeSpinsAvailableProvidersDto;
use App\Enums\AggregatorEnum;
use App\Models\SlotsProvider;

/**
 * Class SlotsProviderRepository
 * @package App\Repositories\FreeSpins
 */
class SlotsProviderRepository
{
    /**
     * @var SlotsProvider
     */
    private SlotsProvider $model;

    /**
     * SlotsProviderRepository constructor.
     * @param SlotsProvider $model
     */
    public function __construct(SlotsProvider $model)
    {
        $this->model = $model;

    }

    /**
     * @return mixed
     */
    public function availableProviders(FreeSpinsAvailableProvidersDto $availableProvidersDto)
    {
        return $this->model->query()
            ->whereHas('getProviders', function ($query) {
                $query->where(['suspended' => 0, 'enabled' => 1, 'has_freespins' => 1]);
            })
            ->when($availableProvidersDto->getSupplierId() !== null, function ($query) use ($availableProvidersDto) {
                $query->where(['subscription_id' => $availableProvidersDto->getSupplierId()]);
            })
            ->when($availableProvidersDto->getAggregator() !== null, function ($query) use ($availableProvidersDto) {
                $query->where(['aggregator' => $availableProvidersDto->getAggregator()]);
                if ($availableProvidersDto->getAggregator() === AggregatorEnum::SOFTSWISS) {
                    $query->whereIn('name', config('free-spins.softswiss-aggregator-available-providers'));
                }
            })
            ->where(['suspended' => 0])
            ->get()
            ->unique('name');
    }

    public function getAvailableAggregators(?FreeSpinsAvailableAggregatorDto $freeSpinsAvailableAggregatorDto = null): array
    {
        return $this->model->query()
            ->whereHas('getProviders', function ($query) {
                $query->where(['suspended' => 0, 'enabled' => 1, 'has_freespins' => 1]);
            })
            ->when($freeSpinsAvailableAggregatorDto !== null, function ($query) use ($freeSpinsAvailableAggregatorDto) {
                if ($freeSpinsAvailableAggregatorDto ->getSupplierId() !== null) {
                    $query->where(['subscription_id' => $freeSpinsAvailableAggregatorDto->getSupplierId()]);
                }
            })
            ->whereNotNull('aggregator')
            ->where(['suspended' => 0])
            ->groupBy('aggregator')
            ->pluck('aggregator')
            ->toArray();
    }
}
