<?php

namespace App\Repositories\FreeSpins;

use App\Models\DataSubscriptions;

/**
 * Class DataSubscriptionRepository
 * @package App\Repositories\FreeSpins
 */
class DataSubscriptionRepository
{
    /**
     * @var DataSubscriptions
     */
    private DataSubscriptions $model;

    /**
     * DataSubscriptionRepository constructor.
     * @param DataSubscriptions $model
     */
    public function __construct(DataSubscriptions $model)
    {
        $this->model = $model;
    }

    /**
     * @param $provider
     *
     * @return mixed
     */
    public function getByProvider($provider)
    {
        return $this->model->where('provider', $provider)->first();
    }

}
