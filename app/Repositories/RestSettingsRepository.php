<?php
namespace App\Repositories;

use App\Services\PlayerAppClient;
use Illuminate\Http\Request;
use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\CriteriaAwareRepository;
use Skipper\Repository\Exceptions\RepositoryException;
use WhiteLabelAdmin\Entities\ClientSetting;
use WhiteLabelAdmin\Repositories\ClientSettingsRepositoryInterface;

final class RestSettingsRepository extends CriteriaAwareRepository implements ClientSettingsRepositoryInterface
{
    private PlayerAppClient $client;
    private Request $request;

    public function __construct(PlayerAppClient $client, Request $request)
    {
        $this->client = $client;
        $this->request = $request;
    }

    /**
     * @param Entity|ClientSetting $entity
     * @return bool
     */
    public function save(Entity $entity): bool
    {
        $this->client->updateSettings([
            'name' => $entity->getClientName(),
            'domain' => $entity->getDomain(),
            'links' => $entity->getSocialLinks(),
        ]);

        return true;
    }

    /**
     * @param Entity $entity
     * @return bool
     */
    public function delete(Entity $entity): bool
    {
        throw new RepositoryException('Settings delete is not allowed', 'clientSetting');
    }

    /**
     * @param array $criteria
     * @return Entity[]
     */
    public function findAll(array $criteria): array
    {
        $settings = $this->client->getSettings();
        $e = new ClientSetting($this->request->user()->token()->client->real_client_id, $settings['name']);
        $e->setDomain($settings['domain'])
            ->setSocialLinks($settings['links']);

        return [$e];
    }

    /**
     * @param array $criteria
     * @return int
     */
    public function count(array $criteria): int
    {
        return count($this->findAll($criteria));
    }
}
