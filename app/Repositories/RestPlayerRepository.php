<?php
namespace App\Repositories;

use App\Services\PlayerAppClient;
use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\CriteriaAwareRepository;
use Skipper\Repository\Exceptions\RepositoryException;
use WhiteLabelAdmin\Entities\Player;
use WhiteLabelAdmin\Repositories\PlayerRepositoryInterface;

final class RestPlayerRepository extends CriteriaAwareRepository implements PlayerRepositoryInterface
{
    /**
     * @var PlayerAppClient
     */
    private PlayerAppClient $client;

    public function __construct(PlayerAppClient $client)
    {
        $this->client = $client;
    }

    /**
     * @param Entity|Player $entity
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws RepositoryException
     */
    public function save(Entity $entity): bool
    {
        if (null === $entity->getId()) {
            throw new RepositoryException('Creating players from admin panel is not allowed', 'player.save');
        }

        $updatedAttributes = $entity->getUpdated();

        $profileOptions = array_intersect_key($updatedAttributes, [
            'first_name' => null,
            'last_name' => null,
            'birth' => null,
            'email' => null,
            'phone' => null,
        ]);
        $metaOptions = array_intersect_key($updatedAttributes, [
            'blocked' => null,
            'casino_access' => null,
            'comment' => null,
            'is_fake' => null,
            'is_suspicious' => null,
        ]);
        $balanceOptions = array_intersect_key($updatedAttributes, [
            'withdraw' => null,
            'wager' => null,
            'comment' => null,
        ]);
        $player = $entity->getData();

        if (!empty($profileOptions)) {
            $player = $this->client->updateProfile($entity->getId(), $profileOptions);
        }

        if (!empty($metaOptions)) {
            $player = $this->client->updatePlayerMeta($entity->getId(), $metaOptions);
        }

        if (!empty($balanceOptions)) {
            $player = $this->client->updatePlayerBalance($entity->getId(), $balanceOptions);
        }

        $entity->setNewData($player)
            ->setUpdated([]);

        return true;
    }

    /**
     * @param Entity $entity
     * @return bool
     */
    public function delete(Entity $entity): bool
    {
        throw new RepositoryException('Deleting players from admin panel is not supported now', 'player.delete');
    }

    /**
     * @param array $criteria
     * @return Entity[]
     */
    public function findAll(array $criteria): array
    {
        $data = $this->searchPlayers($criteria);

        return array_map(fn(array $player) => new Player($player), $data['data']);
    }

    /**
     * @param array $criteria
     * @return array
     */
    public function getAllWithTotalCount(array $criteria): array
    {
        $data = $this->searchPlayers($criteria);

        return [
            'data' => array_map(fn(array $player) => new Player($player), $data['data']),
            'total' => $data['meta']['counts']['total'],
        ];
    }

    public function searchPlayers(array $criteria): array
    {
        $filters = $this->getFiltersFromCriteria($criteria);
        $sorts = $this->getSortsFromCriteria($criteria);
        $pagination = $this->getPaginationFromCriteria($criteria);
        $query = [];

        if (null !== $pagination) {
            $query['pagination'] = [
                'limit' => $pagination->getLimit(),
                'offset' => $pagination->getOffset(),
            ];
        }
        foreach ($sorts as $column => $order) {
            $query[$order] = $column;
        }

        foreach ($filters as $filter) {
            if ($filter->getColumn() === 'id' && $filter->getOperator() === 'in') {
                $query['ids'] = implode(',', $filter->getValue());
                continue;
            }
            if (is_bool($filter->getValue())) {
                $value = $filter->getValue() ? '1' : '0';
            } else {
                $value = $filter->getValue();
            }
            $query[$filter->getColumn()] = $value;
        }


        return $this->client->playerSearch($query);
    }

    /**
     * @param array $criteria
     * @return int
     */
    public function count(array $criteria): int
    {
        $criteria['pagination'] = [
            'limit' => 1,
            'offset' => 0,
        ];
        return $this->getAllWithTotalCount($criteria)['total'];
    }

    public function find(int $id): Entity
    {
        return $this->findOneBy([
            'filter' => [
                'id' => [
                    ['value' => $id],
                ],
                'with' => [['value' => 'balances']],
            ],
        ]);
    }
}
