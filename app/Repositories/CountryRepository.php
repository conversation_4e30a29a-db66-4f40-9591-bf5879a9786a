<?php

namespace App\Repositories;

use App\Models\Country;
use App\Models\SlotProvidersCountry;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Collection;

class CountryRepository
{
    /**
     * @throws ModelNotFoundException
     * @param int $countryId
     * @return Country|null|Model
     */
    public function findCountryById(int $countryId): Country
    {
        return Country::query()->findOrFail($countryId);
    }

    public function insertCountrySlotProviders(array $data): bool
    {
       return SlotProvidersCountry::query()->insert($data);
    }

    public function getCountries():Collection
    {
        $countryIds = SlotProvidersCountry::query()->select('country_id')->distinct()->pluck('country_id');
        return Country::query()->whereIn('id',$countryIds)->get();
    }
}
