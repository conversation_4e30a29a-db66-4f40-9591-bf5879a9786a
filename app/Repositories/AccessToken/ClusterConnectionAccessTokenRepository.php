<?php

namespace App\Repositories\AccessToken;

use Lara<PERSON>\Passport\Bridge\AccessTokenRepository;
use League\OAuth2\Server\Entities\ClientEntityInterface;

class ClusterConnectionAccessTokenRepository extends AccessTokenRepository
{
    public function getNewToken(ClientEntityInterface $clientEntity, array $scopes, $userIdentifier = null)
    {
        return new ClusterConnectionAccessToken($userIdentifier, $scopes, $clientEntity);
    }
}
