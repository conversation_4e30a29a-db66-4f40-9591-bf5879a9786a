<?php

namespace App\Repositories\AccessToken;

use DateTimeImmutable;
use Laravel\Passport\Bridge\AccessToken;
use League\OAuth2\Server\Entities\Traits\AccessTokenTrait;

class ClusterConnectionAccessToken extends AccessToken
{
    use AccessTokenTrait;

    private function convertToJWT()
    {
        $this->initJwtConfiguration();

        return $this->jwtConfiguration->builder()
            ->permittedFor($this->getClient()->getIdentifier())
            ->identifiedBy($this->getIdentifier())
            ->issuedAt(new DateTimeImmutable())
            ->canOnlyBeUsedAfter(new DateTimeImmutable())
            ->expiresAt($this->getExpiryDateTime())
            ->relatedTo((string) $this->getUserIdentifier())
            ->withClaim('scopes', $this->getScopes())
            ->withClaim('cc', \get_cluster_connection_from_request())
            ->getToken($this->jwtConfiguration->signer(), $this->jwtConfiguration->signingKey());
    }
}
