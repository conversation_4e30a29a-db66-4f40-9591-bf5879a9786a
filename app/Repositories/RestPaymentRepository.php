<?php
namespace App\Repositories;

use App\Services\PaymentClient;
use App\Services\PlayerAppClient;
use Illuminate\Http\Request;
use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\CriteriaAwareRepository;
use WhiteLabelAdmin\Entities\PaymentGateway;
use WhiteLabelAdmin\Repositories\PaymentGatewayRepositoryInterface;

final class RestPaymentRepository extends CriteriaAwareRepository implements PaymentGatewayRepositoryInterface
{
    private PaymentClient $payments;
    private PlayerAppClient $app;
    private Request $request;

    public function __construct(PaymentClient $payments, PlayerAppClient $app, Request $request)
    {
        $this->payments = $payments;
        $this->app = $app;
        $this->request = $request;
    }

    /**
     * @param int $clientId
     * @param string $name
     * @return PaymentGateway
     */
    public function getByClientIdAndName(int $clientId, string $name): PaymentGateway
    {
        return $this->findOneBy([
            'filter' => [
                'name' => [['value' => $name]],
            ],
        ]);
    }

    /**
     * @return array
     */
    public function getAvailableGateways(): array
    {
        return $this->payments->getAllMethods();
    }

    /**
     * @param Entity|PaymentGateway $entity
     * @return bool
     */
    public function save(Entity $entity): bool
    {
        if (false === $entity->isDeposit() && false === $entity->isPayout()) {
            return $this->delete($entity);
        }

        $settings = $this->app->getSettings()['payment_gateways'];
        $settings = array_combine(array_column($settings, 'name'), $settings);
        $settings[$entity->getName()] = [
            'name' => $entity->getName(),
            'deposit' => $entity->isDeposit(),
            'payout' => $entity->isPayout(),
        ];
        $this->app->updateSettings([
            'payment_gateways' => array_values($settings),
        ]);

        return true;
    }

    /**
     * @param Entity|PaymentGateway $entity
     * @return bool
     */
    public function delete(Entity $entity): bool
    {
        $settings = $this->app->getSettings()['payment_gateways'];
        $settings = array_combine(array_column($settings, 'name'), $settings);
        unset($settings[$entity->getName()]);
        $this->app->updateSettings([
            'payment_gateways' => array_values($settings),
        ]);

        return true;
    }

    /**
     * @param array $criteria
     * @return Entity[]
     */
    public function findAll(array $criteria): array
    {
        /** @var PaymentGateway[] $gateways */
        $gateways = array_map(\Closure::fromCallable([$this, 'fromArrayToEntity']), $this->app->getSettings()['payment_gateways']);
        $filters = $this->getFiltersFromCriteria($criteria);
        $result = array_map(function (PaymentGateway $gateway) use ($filters) {
            foreach ($filters as $filter) {
                if ($filter->getColumn() === 'name' && $gateway->getName() !== $filter->getValue()) {
                    return null;
                }
                if ($filter->getColumn() === 'deposit' && $gateway->isDeposit() !== (bool)(int)$filter->getValue()) {
                    return null;
                }
                if ($filter->getColumn() === 'payout' && $gateway->isPayout() !== (bool)(int)$filter->getValue()) {
                    return null;
                }
            }

            return $gateway;
        }, $gateways);

        return array_filter($result);
    }

    private function fromArrayToEntity(array $data): PaymentGateway
    {
        $e = new PaymentGateway($this->request->user()->token()->client->real_client_id, $data['name']);
        $e->setIsDeposit($data['deposit'])
            ->setIsPayout($data['payout']);

        return $e;
    }

    /**
     * @param array $criteria
     * @return int
     */
    public function count(array $criteria): int
    {
        return count($this->findAll($criteria));
    }
}
