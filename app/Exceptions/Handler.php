<?php

namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\UnauthorizedException;
use Illuminate\Validation\ValidationException;
use <PERSON><PERSON>\Lumen\Exceptions\Handler as ExceptionHandler;
use Skipper\Exceptions\DomainException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        AuthorizationException::class,
        HttpException::class,
        ModelNotFoundException::class,
        ValidationException::class,
        PaymentResponseException::class,
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sen<PERSON>, Bugsnag, etc.
     *
     * @param \Throwable $exception
     * @return void
     *
     * @throws \Exception
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Throwable $exception
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception)
    {
        if ($exception instanceof UnauthorizedException) {
            $exception = new DomainException($exception->getMessage(), $exception->getFile(), [], null, $exception->getCode());
        }

        if ($exception instanceof DomainException) {
            $data = $exception->render();
            if (!env('APP_DEBUG')) {
                unset(
                    $data['context']['_file'],
                    $data['context']['_line'],
                    $data['context']['_previous'],
                );
            }

            return response()->json($data, $exception->getCode() ?: 500);
        }

        $rendered = parent::render($request, $exception);

        if ($exception instanceof PaymentResponseException) {
            return $rendered;
        }

        return response()->json([
            'message' => $exception->getMessage(),
            'code' => $rendered->getStatusCode(),
        ], $rendered->getStatusCode());
    }
}
