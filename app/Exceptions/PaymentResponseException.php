<?php declare(strict_types=1);

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use IntegratorCore\Services\Dto\ResponseOutputDto;

class PaymentResponseException extends Exception
{
    private ResponseOutputDto $responseOutputDto;

    public function __construct(ResponseOutputDto $responseOutputDto)
    {
        $this->responseOutputDto = $responseOutputDto;

        parent::__construct();
    }
    public function render(): JsonResponse
    {
        $data = isset($this->responseOutputDto->content['message'])
            ? $this->responseOutputDto->content
            : ['message' => 'Unknown error occurred.'];

        $data['code'] = $this->responseOutputDto->responseCode;

        return new JsonResponse($data, $this->responseOutputDto->responseCode);
    }
}
