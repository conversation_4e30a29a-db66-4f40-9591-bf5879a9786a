<?php declare(strict_types=1);


namespace App\Console\Commands;


use App\Models\Payout;
use App\Models\PayoutStatus;
use App\Services\Payout\WithdrawOrderService;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ManageInternalPayoutStatusCommand extends Command
{
    protected $signature = 'manage-internal-payout-status';
    private WithdrawOrderService $withdrawOrderService;

    public function __construct(WithdrawOrderService $withdrawOrderService)
    {
        parent::__construct();
        $this->withdrawOrderService = $withdrawOrderService;
    }

    public function handle(): void
    {
        $columns = [
            'transaction_token',
            'payouts_id',
            'payouts_system_status',
            'status',
            'player_id',
            'message',
            'created_at',
            'updated_at',
        ];

        $payoutStatuses = PayoutStatus::all($columns);
        $corePayoutTokens = $payoutStatuses->pluck('payouts_id')->all();
        $corePayouts = Payout::whereIn('id', $corePayoutTokens)->get();

        $statuses = [];
        foreach ($payoutStatuses as $k => $payoutStatus) {
            $statuses[$k] = $payoutStatus->toArray();
            $statuses[$k]['whitelabel_status'] = $corePayouts->where('id', $payoutStatus->payouts_id)->first()->status;
        }

        $this->table(
            array_merge($columns, ['whitelabel_status']),
            $statuses
        );

        $syncStatuses = $this->ask('Would you like sync statuses?');

        if (Str::lower($syncStatuses) === 'y') {
            $this->withdrawOrderService->syncStatuses();

            return;
        }

        $selectToken = $this->ask('Would you like select a token?');

        if (Str::lower($selectToken) === 'y') {
            $token = $this->choice('Select token', $payoutStatuses->pluck('transaction_token')->all());

            $delete = $this->ask('Delete it?', 'n');

            if (Str::lower($delete) === 'y') {
                PayoutStatus::where('transaction_token', $token)->delete();
                $this->info('Deleted');
            }
        }
    }
}
