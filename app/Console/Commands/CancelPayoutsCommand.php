<?php declare(strict_types=1);


namespace App\Console\Commands;


use App\Models\Payout;
use App\Models\PayoutStatus;
use App\Services\PlayerAppClient;
use Illuminate\Console\Command;

class CancelPayoutsCommand extends Command
{
    protected $signature = 'payouts:cancel';
    protected PlayerAppClient $playerAppClient;

    public function __construct(PlayerAppClient $playerAppClient)
    {
        parent::__construct();
        $this->playerAppClient = $playerAppClient;
    }

    public function handle()
    {
        $tokens = [
            '6ynVTHo8XDw6I66EroKaRYVkUJoK4brc',
        ];

        foreach ($tokens as $token) {
            $payout = Payout::with('detail')->where('transaction_token', $token)->first();

            if ($payout !== null) {
                try {
                    $this->playerAppClient->sendClosedPayoutStatus($payout->id, 1);
                } catch (\Throwable $e) {
                    $this->error("$token - error");
                    continue;
                }

                PayoutStatus::where('transaction_token', $token)->delete();

                $this->info("$token - ok");
            }
        }
    }
}
