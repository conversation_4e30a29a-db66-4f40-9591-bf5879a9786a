<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Lara<PERSON>\Passport\Passport;

final class GrantPlayerAccessCommand extends Command
{
    protected $signature = 'passport:grant {--clientId : Admin clientId} {--grantClientId : WhiteLabel client id}';
    protected $description = 'Grant admin client to access PlayerAPI';

    public function handle()
    {
        $clientId = $this->option('clientId') ?: $this->ask(
            'State the Admin OAuth client id'
        );
        $client = Passport::client()->newQuery()->findOrFail($clientId);
        $realClientId = $this->option('grantClientId') ?: $this->ask(
            'State the App client id'
        );
        $client->real_client_id = $realClientId;
        $client->saveOrFail();
        $this->info('Done');
    }
}
