<?php declare(strict_types=1);


namespace App\Console\Commands;


use App\Models\Payout;
use App\Models\PayoutStatus;
use App\Services\PlayerAppClient;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DeleteOldClientTokenCommand extends Command
{
    protected $signature = 'delete:old-client-token';

    public function handle()
    {
        $token = DB::table('oauth_clients')->where('name', 'WhiteLabelAdmin ClientCredentials Grant Client')->first();

        if (!$token) {
            $this->info('Oauth client not found');
            return;
        }

        if ($this->confirm('Token found, delete it?')) {
            DB::table('oauth_clients')->where('id', $token->id)->delete();

            $this->info('Token deleted successfully');
        } else {
            $this->info('Token deletion cancelled');
        }
    }
}
