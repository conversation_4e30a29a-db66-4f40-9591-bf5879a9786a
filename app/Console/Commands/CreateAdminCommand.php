<?php
namespace App\Console\Commands;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Contracts\Hashing\Hasher;
use Laravel\Passport\Passport;
use WhiteLabelAdmin\Entities\PermissionList;

final class CreateAdminCommand extends Command
{
    protected $signature = 'passport:admin {--clientId : WhiteLabel App Client Id }';
    protected $description = 'creates one admin to rule them all';

    public function handle(Hasher $hasher)
    {
        $clientId = $this->option('clientId') ?: $this->ask(
            'State the WhiteLabel App Client Id'
        );

        Passport::client()->newQuery()->where('real_client_id', $clientId)->firstOrFail();

        $role = new Role;
        $role->client_id = $clientId;
        $role->name = 'king under the mountain';
        $role->saveOrFail();
        $role->permissions()->sync((new Permission)->newQuery()->where('name', PermissionList::ADMIN)->get());

        $admin = new User;
        $admin->client_id = $clientId;
        $admin->email = $this->ask(
            'Your email please'
        );
        $admin->login = $this->ask(
            'Your login please'
        );

        $password = $this->secret(
            'Password'
        );
        if ($this->secret(
            'Repeat password'
        ) !== $password) {
            throw new \RuntimeException('Passwords do not match');
        }

        $admin->password = $hasher->make($password);
        $admin->role()->associate($role);
        $admin->saveOrFail();

        $this->info('Done');
    }
}
