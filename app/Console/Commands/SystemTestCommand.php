<?php declare(strict_types=1);


namespace App\Console\Commands;


use App\Jobs\TestJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;


class SystemTestCommand extends Command
{
    /**
     * @var string
     */
    protected $signature = 'system:test';

    /**
     * @var string
     */
    protected $description = 'Microservice health test';

    public function handle(): int
    {
        $i = 0;
        while ($i < 1000) {
            $i++;
            dispatch(new TestJob());
        }

        $this->checkEnv();

        $this->testDb();

        $this->checkLodging();

        $storagePath = storage_path();
        $this->checkChmod($storagePath);

        $storagePath = storage_path('logs');
        $this->checkChmod($storagePath);

        $storagePath = storage_path('framework/cache');
        $this->checkChmod($storagePath);

        $storagePath = storage_path('framework/cache/data');
        $this->checkChmod($storagePath);

        $storagePath = storage_path('framework/views');
        $this->checkChmod($storagePath);

        return 0;
    }

    protected function checkEnv(): void
    {
        $envExample = new \SplFileObject(".env.example");

        $checkParameters = [];
        while (!$envExample->eof()) {
            $envStr = $envExample->fgets();
            if ($envStr && !Str::startsWith($envStr, '#') && Str::contains($envStr, '=')) {
                $variable = explode('=', $envStr);
                if ($variable !== false) {
                    $checkParameters[$variable[0]] = '';
                }
            }
        }

        $envExample = null;

        $envPath = base_path('.env');

        if (file_exists($envPath)) {
            $this->info("$envPath - ok");
        } else {
            $this->warn("$envPath - Configuration file is missing. Copy file .env.example > .env");
        }

        foreach ($checkParameters as $parameter => $failValue) {
            $value = env($parameter, '');

            if ($value !== $failValue) {
                $this->info(".env | $parameter = $value - ok");
            } else {
                $this->error(".env | $parameter - not configured");
            }
        }
    }

    protected function checkLodging(): void
    {
        $context = [
            [
                'foo' => 'foo value',
                'bar' => 'var value',
                'nested' => [
                    'nestedFoo' => 'nested foo value',
                    'nestedBar' => 'nested bar value',
                ]
            ],
        ];

        try {
            Log::alert('Alert log of ' . env('APP_NAME'), $context);
            Log::critical('Critical log of ' . env('APP_NAME'), $context);
            Log::debug('Debug log of ' . env('APP_NAME'), $context);
            Log::emergency('Emergency log of ' . env('APP_NAME'), $context);
            Log::error('Error log of ' . env('APP_NAME'), $context);
            Log::info('Info log of ' . env('APP_NAME'), $context);
            Log::notice('Notice log of ' . env('APP_NAME'), $context);
            Log::warning('Warning log of ' . env('APP_NAME'), $context);
            $this->info('Logging - ok');
        } catch (\Exception $e) {
            $this->error('Logging - fail');
        }
    }

    /**
     * @param string $path
     */
    protected function checkChmod(string $path): void
    {
        if (is_writable($path)) {
            $this->info("$path - ok");
        } else {
            $this->error("$path - Missing write permissions, trying to fix ...");
            $this->resolveChmod($path);
        }
    }

    /**
     * @param string $path
     */
    protected function resolveChmod(string $path): void
    {
        $cmd = 'chmod 0777 ' . $path;
        popen($cmd, 'r+');

        if (is_writable($path)) {
            $this->info("$path - ok");
        } else {
            $this->error("$path - Could not be fixed. You need to set write permissions.");
        }
    }

    protected function testDb(): void
    {
        $this->info('Db test started');
        try {
            $data = DB::table('oauth_clients')->first();

            if (empty($data)) {
                $this->error('Oauth_clients table is empty');

                return;
            }

            $json = json_encode($data, JSON_THROW_ON_ERROR|JSON_PRETTY_PRINT);

            $this->info($json);
        } catch (\PDOException $e) {
            $this->error('DB error');
            $this->error($e->getMessage());
        }
    }
}
