<?php declare(strict_types=1);


namespace App\Console\Commands;


use App\Models\Permission;
use App\Models\Role;
use App\Models\RolePermission;
use Illuminate\Console\Command;

class PermissionCommand extends Command
{
    protected $signature = 'permission:set-all-for-admin {--role_id=1}';
    public function handle()
    {
        /** @var Role $adminRole */
        $adminRole = Role::query()->where('id', $this->option('role_id'))->firstOrFail();
        $adminPermissions = RolePermission::query()->where('role_id', $adminRole->id)->pluck('permission_id')->toArray();

        $permissionsQuery = Permission::query();
        if ($adminPermissions) {
            $permissionsQuery->whereNotIn('id', $adminPermissions);
        }
        $permissionIds = $permissionsQuery->pluck('id')->toArray();

        foreach ($permissionIds as $permissionId) {
            $rolePermission = new RolePermission();
            $rolePermission->role_id = $adminRole->id;
            $rolePermission->permission_id = $permissionId;
            $rolePermission->save();
        }

        $this->info(sprintf('Added %d permissions', count($permissionIds)));
    }
}
