<?php declare(strict_types=1);

namespace App\Console\Commands;

use App\Jobs\CancelledPayoutChangeStatusJob;
use App\Models\PayKassmaPayout;
use App\Models\Payout;
use App\Models\PayoutStatus;
use Illuminate\Console\Command;

class ClosePayoutsByJobsCommand extends Command
{
    protected $signature = 'payout:close';

    public function handle()
    {
        $withdrawOrderToken = $this->ask('payout transaction token');
        /** @var Payout $payout */
        $payout = Payout::query()->where('transaction_token', $withdrawOrderToken)->firstOrFail();

        /** @var PayKassmaPayout $paykassaPayout */
        $paykassaPayout = PayKassmaPayout::query()->findOrFail($payout->transaction_id);

        $comment = (string)$this->ask('commend');
        $clientId = (int)$this->ask('client id');

        if (!PayoutStatus::where('payouts_id', $payout->id)->exists()) {
            $payoutStatus = new PayoutStatus();
            $payoutStatus->payouts_id = $payout->id;
            $payoutStatus->transaction_token = $withdrawOrderToken;
            $payoutStatus->save();
        }

        dispatch(new CancelledPayoutChangeStatusJob(
            $payout->id,
            $payout->transaction_token,
            $payout->gateway,
            $paykassaPayout->transaction_id,
            $comment,
            $clientId,
        ));

        $this->info('done');
    }
}
