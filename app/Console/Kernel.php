<?php

namespace App\Console;

use App\Console\Commands\CancelPayoutsCommand;
use App\Console\Commands\ClosePayoutsByJobsCommand;
use App\Console\Commands\CreateAdminCommand;
use App\Console\Commands\DeleteOldClientTokenCommand;
use App\Console\Commands\GrantPlayerAccessCommand;
use App\Console\Commands\ManageInternalPayoutStatusCommand;
use App\Console\Commands\PermissionCommand;
use App\Console\Commands\SystemTestCommand;
use Illuminate\Console\Scheduling\Schedule;
use Laravel\Lumen\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        GrantPlayerAccessCommand::class,
        CreateAdminCommand::class,
        SystemTestCommand::class,
        ManageInternalPayoutStatusCommand::class,
        CancelPayoutsCommand::class,
        PermissionCommand::class,
        ClosePayoutsByJobsCommand::class,
        DeleteOldClientTokenCommand::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //
    }
}
