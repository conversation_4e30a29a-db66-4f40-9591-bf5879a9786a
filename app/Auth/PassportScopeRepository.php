<?php
namespace App\Auth;

use <PERSON><PERSON>\Passport\Bridge\Scope;
use <PERSON><PERSON>\Passport\Bridge\ScopeRepository;
use <PERSON><PERSON>\Passport\Passport;
use League\OAuth2\Server\Entities\ClientEntityInterface;

final class PassportScopeRepository extends ScopeRepository
{

    public function getScopeEntityByIdentifier($identifier)
    {
        if (Passport::client()->newQuery()->where('real_client_id', $identifier)->exists()) {
            return new Scope($identifier);
        }

        return parent::getScopeEntityByIdentifier($identifier);
    }

    public function finalizeScopes(
        array $scopes,
        $grantType,
        ClientEntityInterface $clientEntity,
        $userIdentifier = null
    ) {
        if ($clientEntity->getIdentifier() === env('APP_CLIENT_ID') && $grantType === 'client_credentials') {
            return $scopes;
        }

        return parent::finalizeScopes($scopes, $grantType, $clientEntity, $userIdentifier);
    }
}
