<?php

namespace App\Auth;

use League\OAuth2\Client\Provider\GenericProvider;

class CustomGenericProvider extends GenericProvider
{
    private array $defaultHeaders = [];

    public function setDefaultHeaders(array $defaultHeaders): void
    {
        $this->defaultHeaders = $defaultHeaders;
    }

    protected function getDefaultHeaders()
    {
        return array_merge(parent::getDefaultHeaders(), $this->defaultHeaders);
    }
}
