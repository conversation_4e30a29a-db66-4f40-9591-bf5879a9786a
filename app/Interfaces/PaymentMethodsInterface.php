<?php


namespace App\Interfaces;



/**
 * Interface PaymentMethodsInterface
 * @package App\Interfaces
 */
interface PaymentMethodsInterface
{

    /**
     * @param $request
     * @param string $iso
     * @return array
     */
    public function show($request, string $iso): array;

    /**
     * @param $request
     * @param string $iso
     * @return array
     */
    public function showAggregatorsPayments($request, string $iso): array;

}
