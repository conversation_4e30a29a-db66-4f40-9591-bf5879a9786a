<?php

namespace App\Interfaces;

/**
 * Interface PresetInterface
 * @package App\Interfaces
 */
interface PresetInterface
{
    /**
     * @param string $token
     * @return array
     */
    public function show(string $token): array;

    /**
     * @param $request
     * @param string $token
     * @return array
     */
    public function store($request, string $token): array;

    /**
     * @param $request
     * @param string $token
     * @return array
     */
    public function destroy($request, string $token): array;
}
