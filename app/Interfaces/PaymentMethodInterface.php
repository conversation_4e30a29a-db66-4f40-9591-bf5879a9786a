<?php


namespace App\Interfaces;



/**
 * Interface PaymentMethodInterface
 * @package App\Interfaces
 */
interface PaymentMethodInterface
{

    /**
     * @param string $token
     * @return array
     */
    public function show(string $token): array;


    /**
     * @param $request
     * @param string $token
     * @return array
     */
    public function update($request, string $token): array;
}
