<?php

namespace App\Interfaces;

/**
 * Interface CurrencyInterface
 * @package App\Interfaces
 */
interface WithdrawOrderInterface
{
    /**
     * @param array $orderIds
     * @return array
     */
    public function getMethodsToChange(array $orderIds): array;

    /**
     * @param array $orderIds
     * @param string $methodToken
     * @return array
     */
    public function getMethodsToChangeUpdate(array $orderIds, string $methodToken): array;
}
