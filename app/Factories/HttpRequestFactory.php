<?php
namespace App\Factories;

use Illuminate\Http\Request;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Requests\ClientSettingsRequest;
use White<PERSON>abelAdmin\Requests\CountryRequest;
use White<PERSON>abelAdmin\Requests\CurrencyRequest;
use WhiteLabelAdmin\Requests\GameCategoryRequest;
use WhiteLabelAdmin\Requests\PageRequest;
use WhiteLabelAdmin\Requests\PaymentGatewayRequest;
use WhiteLabelAdmin\Requests\PlayerCommentRequest;
use WhiteLabelAdmin\Requests\PlayerRequest;
use WhiteLabelAdmin\Requests\ProxyRequest;
use WhiteLabelAdmin\Requests\RoleRequest;
use WhiteLabelAdmin\Requests\SearchRequest;
use WhiteLabelAdmin\Requests\UserRequest;

class HttpRequestFactory implements RequestFactoryInterface
{
    /**
     * @var Request
     */
    private Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * @param int $playerId
     * @return PlayerRequest
     */
    public function createPlayerRequest(int $playerId): PlayerRequest
    {
        return new PlayerRequest(
            $playerId,
            $this->request->attributes->get('token')->client->real_client_id,
            $this->request->json('fake'),
            $this->request->json('suspect'),
            $this->request->json('first_name'),
            $this->request->json('last_name'),
            $this->request->json('birth'),
            $this->request->json('email'),
            $this->request->json('phone'),
            $this->request->json('blocked'),
            $this->request->json('casino'),
            $this->request->json('withdraw'),
            $this->request->json('wager'),
            $this->request->json('comment'),
            $this->request->json('phone_code')
        );
    }

    /**
     * @param int|null $userId
     * @return UserRequest
     */
    public function createUserRequest(?int $userId): UserRequest
    {
        return new UserRequest(
            $userId,
            $this->request->attributes->get('token')->client->real_client_id,
            $this->request->json('login'),
            $this->request->json('password'),
            $this->request->json('email'),
            $this->request->json('repeat_password'),
//            $this->request->json('old_password'),
            $this->request->json('role_id')
        );
    }

    /**
     * @param int|null $roleId
     * @return RoleRequest
     */
    public function createRoleRequest(?int $roleId): RoleRequest
    {
        return new RoleRequest(
            $roleId,
            $this->request->attributes->get('token')->client->real_client_id,
            $this->request->json('name'),
            $this->request->json('permissions')
        );
    }

    /**
     * @return SearchRequest
     */
    public function createSearchRequest(): SearchRequest
    {
        return new SearchRequest(
            $this->request->query->all(),
            $this->request->attributes->get('token')->client->real_client_id
        );
    }

    /**
     * @param string $iso
     * @return CurrencyRequest
     */
    public function createCurrencyRequest(string $iso): CurrencyRequest
    {
        return new CurrencyRequest(
            $iso,
            $this->request->attributes->get('token')->client->real_client_id,
            $this->request->json('is_available'),
            $this->request->json('min_bet'),
            $this->request->json('max_win')
        );
    }

    /**
     * @param string $iso
     * @return CountryRequest
     */
    public function createCountryRequest(string $iso): CountryRequest
    {
        return new CountryRequest(
            $this->request->attributes->get('token')->client->real_client_id,
            $iso,
            $this->request->json('is_accessible')
        );
    }

    public function createPageRequest(string $slug): PageRequest
    {
        return new PageRequest(
            $slug,
            $this->request->json('content'),
            $this->request->json('title'),
            $this->request->json('meta'),
            $this->request->attributes->get('token')->client->real_client_id
        );
    }

    public function createGatewayRequest(string $name): PaymentGatewayRequest
    {
        return new PaymentGatewayRequest(
            $this->request->attributes->get('token')->client->real_client_id,
            $name,
            $this->request->json('deposit'),
            $this->request->json('payout')
        );
    }

    /**
     * @return ClientSettingsRequest
     */
    public function createSettingsRequest(): ClientSettingsRequest
    {
        return new ClientSettingsRequest(
            $this->request->attributes->get('token')->client->real_client_id,
            $this->request->json('name'),
            $this->request->json('links'),
        );
    }

    /**
     * @param string $type
     * @param string $slug
     * @return GameCategoryRequest
     */
    public function createCategoryRequest(string $type, string $slug): GameCategoryRequest
    {
        return new GameCategoryRequest(
            $this->request->attributes->get('token')->client->real_client_id,
            $type,
            $slug,
            $this->request->json('name'),
            $this->request->json('enabled'),
            $this->request->json('weight')
        );
    }

    /**
     * @return PlayerCommentRequest
     */
    public function createPlayerCommentRequest(): PlayerCommentRequest
    {
        return new PlayerCommentRequest(
            $this->request->attributes->get('token')->client->real_client_id,
            $this->request->json('emails'),
            $this->request->json('comment')
        );
    }

    /**
     * @return ProxyRequest
     */
    public function createProxyRequest(): ProxyRequest
    {
        return new ProxyRequest(
            $this->request->attributes->get('token')->client->real_client_id,
            $this->request->getMethod(),
            str_replace('/api/v2', '', $this->request->getRequestUri()),
            $this->request->query->all(),
            $this->request->request->all()
        );
    }
}
