<?php

namespace App\Http\Clients;

use App\Services\HttpService;
use AppV2\SmartLink\Dto\AlanbaseOfferDto;
use AppV2\SmartLink\Exceptions\OfferNotFoundException;
use GuzzleHttp\Client;
use IntegratorCore\Services\Dto\ResponseOutputDto;
use Illuminate\Support\Facades\Cache;

class AlanBaseHttpClient extends HttpService
{
    private const CACHE_TTL = 180;
    private const CACHE_KEY = 'offers_page';
    private const ITEMS_PER_PAGE = 1000;

    public function __construct(Client $httpClient) {
        parent::__construct($httpClient);
    }

    public function getByPage(int $page, int $perPage): ResponseOutputDto
    {
        $this->setQuery([
            'page' => $page,
            'per_page' => $perPage
        ]);

        return $this->doRequest('GET', 'v1/admin/offers');
    }

    public function getAll(): array
    {
        return Cache::remember(self::CACHE_KEY, self::CACHE_TTL, function () {
            $result = [];
            $page = 1;

            while (true) {
                $response = $this->getByPage($page++, self::ITEMS_PER_PAGE);
                $result = array_merge($result, $response->content['data']);

                if ($page > $response->content['meta']['last_page']) {
                    break;
                }
            }

            return $result;
        });
    }

    public function getOfferById(int $id): AlanbaseOfferDto
    {
        $response = $this->doRequest('GET', 'v1/admin/offers/' . $id);

        if (!$response->isSuccess && $response->responseCode === 404) {
            throw new OfferNotFoundException('Offer not found. Id: ' . $id);
        }

        if (!$response->isSuccess) {
            throw new \RuntimeException('Could not get offer by id: ' . $id);
        }

        if (!isset($response->content['data'])) {
            throw new \RuntimeException('Offer not found by id: ' . $id);
        }

        return new AlanbaseOfferDto($response->content['data']);
    }

    public function updateDomain(AlanbaseOfferDto $offer, string $newTargetLink): AlanbaseOfferDto
    {
        $this->setJson([
            'name' => $offer->name,
            'target_link' => $newTargetLink,
            'tracking_domain_id' => $offer->trackingDomain['id'],
            'privacy_level' => $offer->privacyLevel,
        ]);

        $response = $this->doRequest('PATCH', 'v1/admin/offers/' . $offer->id);

        if (!$response->isSuccess || !isset($response->content['data'])) {
            throw new \RuntimeException('Could not update offer by id: ' . $offer->id . ' with new target link: ' . $newTargetLink);
        }

        Cache::forget(self::CACHE_KEY);

        return new AlanbaseOfferDto($response->content['data']);
    }
}
