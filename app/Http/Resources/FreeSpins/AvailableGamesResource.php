<?php


namespace App\Http\Resources\FreeSpins;

use Illuminate\Http\Resources\Json\JsonResource;


class AvailableGamesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "external_id" => $this->external_id
        ];
    }
}

