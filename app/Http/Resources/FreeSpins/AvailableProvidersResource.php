<?php

namespace App\Http\Resources\FreeSpins;

use Illuminate\Http\Resources\Json\JsonResource;

class AvailableProvidersResource extends JsonResource
{

    public function toArray($request)
    {
        /**
         * Transform the resource into an array.
         *
         * @param \Illuminate\Http\Request $request
         * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
         */
        return [
            "id" => $this->id,
            "client_id" => $this->client_id,
            "name" => $this->name,
            "image" => $this->image,
            "section_id" => $this->section_id,
            "suspended" => $this->suspended,
            "created_at" => $this->created_at,
            "updated_at" => $this->updated_at
        ];
    }
}
