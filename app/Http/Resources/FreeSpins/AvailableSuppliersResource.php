<?php

namespace App\Http\Resources\FreeSpins;

use App\Models\DataSubscription;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin DataSubscription
 */
class AvailableSuppliersResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'provider' => $this->provider,
            'provider_company' => $this->provider_company,
            'is_approved' => $this->is_approved,
            'is_slot' => $this->is_slot,
        ];
    }
}
