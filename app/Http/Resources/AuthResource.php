<?php declare(strict_types=1);

namespace App\Http\Resources;

use App\Http\Controllers\Dto\AuthResponseDto;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin AuthResponseDto
 */
class AuthResource extends JsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'access_token' => $this->accessToken,
            'refresh_token' => $this->refreshToken,
            'token_type' => $this->tokenType,
            'expires_in' => $this->expiresIn
        ];
    }
}
