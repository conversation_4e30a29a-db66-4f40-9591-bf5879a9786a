<?php declare(strict_types=1);


namespace App\Http\Resources\Payouts;


use App\Models\PayoutStatus;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin PayoutStatus
 */
class PayoutStatusResource extends JsonResource
{
    /**
     * @inheritDoc
     * @noinspection PhpMissingParentCallCommonInspection
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->payouts_id,
            'status' => $this->status,
            'message' => $this->message,
        ];
    }
}
