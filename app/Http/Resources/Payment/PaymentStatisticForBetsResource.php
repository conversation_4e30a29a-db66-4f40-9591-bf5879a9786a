<?php

declare(strict_types=1);

namespace App\Http\Resources\Payment;

use App\Services\Statistics\Dto\Bets\PaymentsStatisticForBetsDto;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin PaymentsStatisticForBetsDto
 */
class PaymentStatisticForBetsResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->getId(),
            'source_id' => $this->getSourceId(),
            'source_external_id' => $this->getSourceExternalId(),
            'market' => $this->getMarket(),
            'outcome' => $this->getOutcome(),
            'source_id_hash' => $this->getSourceIdHash(),
            'source_events' => $this->getSourceEvents(),
            'source_possible_win' => $this->getSourcePossibleWin() / 100,
            'source_amount' => $this->getSourceAmount() / 100,
            'status' => $this->getStatus(),
            'profit' => $this->getProfit() / 100,
            'currency' => $this->getCurrency(),
            'source_factor' => $this->getSourceFactor(),
            'created_at' => $this->getCreatedAt()->timestamp,
            'updated_at' => $this->getUpdatedAt()->timestamp,
            'is_bonus_balance' => $this->getBonusId() !== null,
        ];
    }
}
