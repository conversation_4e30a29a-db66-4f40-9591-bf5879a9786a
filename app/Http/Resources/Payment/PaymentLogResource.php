<?php

declare(strict_types=1);

namespace App\Http\Resources\Payment;

use App\Services\Statistics\Dto\Logs\PaymentLogDto;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin PaymentLogDto
 */
class PaymentLogResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->getId(),
            'source_id' => $this->getSourceId(),
            'source_type' => $this->getSourceType(),
            'source_fee' => is_null($this->getSourceFee()) ? null : $this->getSourceFee() / 100,
            'currency' => $this->getCurrency(),
            'balance_history' => is_null($this->getBalanceHistory()) ? null : $this->getBalanceHistory() / 100,
            'status' => $this->getStatus(),
            'is_bonus_balance' => $this->getBonusId() !== null,
            'bonus_id' => $this->getBonusId(),
            'amount' => $this->getAmount() / 100,
            'created_at' => $this->getCreatedAt()->timestamp,
            'updated_at' => $this->getUpdatedAt()->timestamp,
        ];
    }
}
