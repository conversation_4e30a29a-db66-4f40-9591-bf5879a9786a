<?php declare(strict_types=1);

namespace App\Http\Resources\Segment;

use App\Models\SegmentStatistics;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin SegmentStatistics
 */
class SegmentsResource extends JsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'external_id' => $this->external_id,
            'name' => $this->name,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
