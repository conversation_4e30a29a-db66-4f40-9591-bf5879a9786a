<?php declare(strict_types=1);


namespace App\Http\Resources\RequisiteBlacklist;


use App\Models\RequisitesBlacklist;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * @mixin RequisitesBlacklist
 */
class RequisiteBlacklistResource extends ResourceCollection
{

    public function toArray($request): array
    {
        $result = [];
        $resources = $this->resource;
        foreach ($resources as $resource) {
            $adminInfo = $resource->getRelation('adminInfo');

            $result[] = [
                'id' => $resource['id'],
                'requisite' => $resource['requisite'],
                'comment' => $resource['comment'],
                'admin_email' => !is_null($adminInfo) ? $adminInfo->email : null,
                'created_at' => $resource['created_at'],
            ];
        }

        return $result;
    }
}
