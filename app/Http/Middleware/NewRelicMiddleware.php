<?php declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Intouch\Newrelic\Newrelic;

class NewRelicMiddleware
{
    /**
     * @var Newrelic
     */
    protected Newrelic $newRelic;

    /**
     * NewRelicMiddleware constructor.
     *
     * @param Newrelic $newRelic
     */
    public function __construct(Newrelic $newRelic)
    {
        $this->newRelic = $newRelic;
    }

    /**
     * Handles the request by naming the transaction for New Relic
     *
     * @param Request $request
     * @param Closure $next
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        $this->newRelic->addCustomParameter("memoryUsed", memory_get_usage() / 1024);
        $this->newRelic->nameTransaction($this->getTransactionName($request));

        return $response;
    }


    /**
     * Builds the transaction name. It will return the assigned controller action first, then the route name before
     * falling back to just "index.php"
     *
     * @param Request $request
     *
     * @return string
     */
    public function getTransactionName(Request $request): string
    {
        $route = $request->route();

        return $route[1]['uses'] ?? $route[1]['as'] ?? 'index.php';
    }

    public function start(): void
    {
        newrelic_start_transaction(config('newrelic.app'));
    }

    public function end(): void
    {
        newrelic_end_transaction();
    }
}
