<?php
namespace App\Http\Middleware;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;

final class HasUltimateToken
{
    use CheckTokenId;

    public function handle(Request $request, \Closure $next)
    {
        if ($request->user() && $this->hasTokenId($request, (int)env('ULTIMATE_CLIENT'))) {
            return $next($request);
        }

        throw new UnauthorizedException(new AuthenticationException('Go away!'));
    }
}
