<?php declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

class AdminCredentialMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        /** @var User $admin */
        if ($admin = Auth::user()) {
            $request->merge([
                'admin_name' => $admin->login,
                'admin_email' => $admin->email,
            ]);
        }

        return $next($request);
    }
}
