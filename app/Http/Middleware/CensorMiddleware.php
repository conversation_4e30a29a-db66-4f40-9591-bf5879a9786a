<?php
namespace App\Http\Middleware;

use App\Models\Permission;
use App\Models\RolePermission;
use Illuminate\Contracts\Hashing\Hasher;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use WhiteLabelAdmin\Entities\PermissionList;
use WhiteLabelAdmin\Repositories\RoleRepositoryInterface;
use WhiteLabelAdmin\Repositories\UserRepositoryInterface;

final class CensorMiddleware
{
    private UserRepositoryInterface $users;

    public function __construct(UserRepositoryInterface $users)
    {
        $this->users = $users;
    }
    public function handle(Request $request, \Closure $next)
    {
        $response = $next($request);
        if ($response instanceof JsonResponse) {
            $data = $response->getData(true);
        } elseif ($response instanceof Response && $response->headers->get('Content-Type') === 'application/json') {
            $data = json_decode($response->getContent(), true);
        } else {
            return $response;
        }

        if (!is_array($data)) {
            return $response;
        }
//        $this->censor($request, $data);
        $response->setContent(json_encode($data));

        return $response;
    }

    private function columnReplacer($groupName, $columnName)
    {
        $name = $columnName;
//        $columnName is the name in the database
        if ($groupName == 'banners') {
            if ($columnName == 'start_at')    { $name = 'start_date'; }
            if ($columnName == 'end_at')      { $name = 'end_date'; }
            if ($columnName == 'enabled')     { $name = 'status'; }
            if ($columnName == 'location')    { $name = 'url'; }
            if ($columnName == 'disposition') { $name = 'pages'; }
        }
        if ($groupName == 'seo') {
            if ($columnName == 'slug')        { $name = 'url'; }
        }
        if ($groupName == 'languages') {
            if ($columnName == 'enabled')     { $name = 'status'; }
        }
        if ($groupName == 'deposits') {
            if ($columnName == 'action_at')   { $name = 'date'; }
        }
        if ($groupName == 'users') {
            if ($columnName == 'last_seen_ip')  { $name = 'ip'; }
            if ($columnName == 'registered_at') { $name = 'registration_date'; }
            if ($columnName == 'last_seen_at')  { $name = 'last_activity'; }
            if ($columnName == 'total_deposit') { $name = 'deposit'; }
        }
        if ($groupName == 'payouts') {
            if ($columnName == 'payout_comment') { $name = 'comment'; }
            if ($columnName == 'account_number') { $name = 'requisites'; }
        }
        return $name;
    }

    /**
     * @param array $response
     */
    private function censor($request, array &$response): void
    {
        if ($request->path() == 'api/v2/api/v1/slots/sections' ||
            $request->path() == 'api/v1/roles' ||
            $request->path() == 'api/v2/api/v1/settings/currencies' ||
            $request->path() == 'api/v2/api/v1/settings/countries' ||
            $request->path() == 'api/v2/api/v1/subscriptions') {
            return;
        }

        if ($request->path() == 'api/v1/roles') { $groupName = 'roles'; }
        elseif ($request->path() == 'api/v2/api/v1/settings/banners') { $groupName = 'banners'; }
        elseif ($request->path() == 'api/v2/api/v1/settings/pages') { $groupName = 'seo'; }
        elseif ($request->path() == 'api/v2/api/v1/settings/languages') { $groupName = 'languages'; }
        elseif ($request->path() == 'api/v1/users') { $groupName = 'moderators'; }
        elseif ($request->path() == 'api/v2/api/v1/deposits') { $groupName = 'deposits'; }
        elseif ($request->path() == 'api/v2/api/v1/payouts') { $groupName = 'payouts'; }
        elseif ($request->path() == 'api/v1/payments') { $groupName = 'payments'; }
        elseif ($request->path() == 'api/v2/api/v1/slots/providers') { $groupName = 'providers'; }
        elseif ($request->path() == 'api/v2/api/v1/slots') { $groupName = 'slots'; }
        else { $groupName = 'users'; }

        if (isset($response['data'])) {

            $permissionsArray = RolePermission::getPermissionsArray($request->user()->role_id);

            foreach ($response['data'] as $key => $row) {
                foreach ($row as $index => $item) {
                    if (!in_array($groupName . '-col-' . $this->columnReplacer($groupName, $index), $permissionsArray)) {
                        unset($response['data'][$key][$index]);
                        continue;
                    }
                    if (is_array($item)) {
                        foreach ($item as $subIndex => $subItem) {
                            if (!is_array($subItem)) {
                                if (!in_array($groupName . '-col-' . $this->columnReplacer($groupName, $subIndex), $permissionsArray)) {
                                    unset($response['data'][$key][$index][$subIndex]);
                                    continue;
                                }
                            }
                            foreach ($subItem as $subItemIndex => $subItemItem) {
                                if (!is_array($subItemItem)) {
                                    if (!in_array($groupName . '-col-' . $this->columnReplacer($groupName, $subItemIndex), $permissionsArray)) {
                                        unset($response['data'][$key][$index][$subIndex][$subItemIndex]);
                                        continue;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

    }
}
