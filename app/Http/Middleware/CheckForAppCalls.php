<?php
namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Lara<PERSON>\Passport\Token;

final class CheckForAppCalls
{
    use CheckTokenId;

    public function handle(Request $request, \Closure $next)
    {
        if (!$this->hasTokenId($request, env('APP_CLIENT_ID'))) {
            return $next($request);
        }

        /** @var Token $token */
        $token = $request->attributes->get('token');
        if (1 !== count($token->scopes)) {
            return $next($request);
        }
        $scopes = $token->scopes;
        $token->real_client_id = (int)reset($scopes);

        return $next($request);
    }
}
