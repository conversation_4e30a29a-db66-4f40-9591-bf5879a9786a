<?php
namespace App\Http\Middleware;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;

final class CheckPermission
{
    public function handle(Request $request, \Closure $next, string $permission)
    {
        if ($request->user() && $request->user()->can($permission)) {
            return $next($request);
        }

        throw new UnauthorizedException(new AuthenticationException('You shall not pass!'), 403);
    }
}
