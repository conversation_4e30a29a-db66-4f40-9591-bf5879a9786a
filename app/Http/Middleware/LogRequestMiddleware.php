<?php declare(strict_types=1);


namespace App\Http\Middleware;


use Illuminate\Support\Facades\Log;

/**
 * Class LogRequestMiddleware
 * @package App\Core\Http\Middleware
 */
class LogRequestMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle(\Illuminate\Http\Request $request, \Closure $next)
    {
        $requestParams = collect($request->input())->except(['password'])->toArray();
        $requestPath = $request->path();
        $requestMethod = $request->method();
        $headers = $request->header();

        if (array_key_exists('authorization', $headers)) {
            $headers['bearer'] = 'Logged in user';
        }

        if (array_key_exists('access-token', $headers)) {
            $headers['bearer'] = 'Logged in user';
        }

        foreach ($this->getExcludeHeadersList() as $key) {
            unset($headers[$key]);
        }

        $rawData = file_get_contents("php://input");

        Log::info(
            "Request | $requestMethod: $requestPath",
            [
                'headers' => $headers,
                'request' => $requestParams,
                'raw' => $rawData ? $this->maskPrivateData($rawData, ['password']) : $rawData,
            ]);

        $response = $next($request);

        $responseCode = $response->getStatusCode();
        $responseContent = $response->getContent();

        try {
            $responseContent = json_decode($responseContent, true, 512, JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            if (!is_array($responseContent)) {
                $responseContent = [$responseContent];
            }
        }

        Log::info("Response [$responseCode] | $requestMethod: $requestPath", ['response' => $responseContent]);

        return $response;
    }

    protected function maskPrivateData(string $responseContent, array $keys): string
    {
        foreach ($keys as $key) {
            $responseContent = preg_replace("/\"$key\":\"([^\"\\\]|\\\.)+\"/", "\"$key\":\"*****\"", $responseContent, 1);
        }

        return $responseContent;
    }

    protected function getExcludeHeadersList(): array
    {
        return [
            'authorization',
            'access-token',
        ];
    }
}
