<?php
namespace App\Http\Middleware;

use App\Services\ThrottlerWhitelistIpService;
use Illuminate\Cache\RateLimiter;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use WhiteLabelAdmin\Exceptions\TooManyAttemptsException;


class Throttler
{
    private RateLimiter $limiter;

    private ThrottlerWhitelistIpService $throttlerWhitelistIpService;

    public function __construct(RateLimiter $limiter, ThrottlerWhitelistIpService $throttlerWhitelistIpService)
    {
        $this->limiter = $limiter;
        $this->throttlerWhitelistIpService = $throttlerWhitelistIpService;
    }

    public function handle(Request $request, \Closure $next, int $maxAttempts = 60, int $decaySeconds = 60)
    {
        $ipAddress = $request->headers->has('ip') ? $request->headers->get('ip') : $request->ip();

        if ($this->throttlerWhitelistIpService->isWhitelistedIpAddress($ipAddress)) {
            return $next($request);
        }

        $key = $this->resolveRequestSignature($request);

        if (env('THROTTLER_ENABLED', true) && $this->limiter->tooManyAttempts($key, $maxAttempts)) {
            throw new TooManyAttemptsException($request->getPathInfo(), $maxAttempts, $this->limiter->availableIn($key));
        }

        $this->limiter->hit($key, $decaySeconds);

        $response = $next($request);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts)
        );
    }

    private function resolveRequestSignature(Request $request)
    {
        return sha1(
            $request->method() .
            '|' . $request->getHost() .
            '|' . $request->ip()
        );
    }

    private function addHeaders(Response $response, int $maxAttempts, int $remainingAttempts, int $retryAfter = null)
    {
        $headers = [
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
        ];

        if (!is_null($retryAfter)) {
            $headers['Retry-After'] = $retryAfter;
        }

        $response->headers->add($headers);

        return $response;
    }

    private function calculateRemainingAttempts(string $key, int $maxAttempts)
    {
        return $maxAttempts - $this->limiter->attempts($key) + 1;
    }
}
