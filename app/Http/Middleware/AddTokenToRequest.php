<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Lara<PERSON>\Passport\TokenRepository;
use League\OAuth2\Server\Exception\OAuthServerException;
use League\OAuth2\Server\ResourceServer;
use <PERSON><PERSON>holm\Psr7\Factory\Psr17Factory;
use Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory;

class AddTokenToRequest
{
    /**
     * The Resource Server instance.
     *
     * @var ResourceServer
     */
    protected ResourceServer $server;

    /**
     * Token Repository.
     *
     * @var TokenRepository
     */
    protected TokenRepository $repository;

    /**
     * Create a new middleware instance.
     *
     * @param ResourceServer $server
     * @param TokenRepository $repository
     * @return void
     */
    public function __construct(ResourceServer $server, TokenRepository $repository)
    {
        $this->server = $server;
        $this->repository = $repository;
    }

    public function handle(Request $request, Closure $next)
    {
        if (null === $request->bearerToken()) {
            return $next($request);
        }

        if ($request->user()) {
            $token = $request->user()->token();
        } else {
            $psr = (new PsrHttpFactory(
                new Psr17Factory,
                new Psr17Factory,
                new Psr17Factory,
                new Psr17Factory
            ))->createRequest($request);

            try {
                $psr = $this->server->validateAuthenticatedRequest($psr);
            } catch (OAuthServerException $e) {
                return $next($request);
            }
            $token = $this->repository->find($psr->getAttribute('oauth_access_token_id'));
        }

        $request->attributes->set('token', $token);

        return $next($request);
    }
}
