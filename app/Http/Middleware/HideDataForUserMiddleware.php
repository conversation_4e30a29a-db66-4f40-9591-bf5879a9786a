<?php declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use WhiteLabelAdmin\Entities\PermissionList;

class HideDataForUserMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        if ($response instanceof JsonResponse) {
            $data = $response->getData(true);
        } elseif ($response instanceof Response && $response->headers->get('Content-Type') === 'application/json') {
            $data = json_decode($response->getContent(), true);
        } else {
            return $response;
        }

        $data = $this->hideDataForUser($request, $data);
        $response->setContent(json_encode($data));

        return $response;
    }

    private function hideDataForUser(Request $request, array $data): array
    {
        $permissionsShowPlayer = [
            PermissionList::USERS_COL_EMAIL => 'email',
            PermissionList::USERS_COL_CITY => 'city',
            PermissionList::USERS_COL_COUNTRY => 'country',
            PermissionList::USERS_COL_BIRTH  => 'birth',
            PermissionList::USERS_COL_FIRSTNAME => 'first_name',
            PermissionList::USERS_COL_LASTNAME => 'last_name',
            PermissionList::USERS_COL_PHONE => 'phone',
        ];

        $permissionsPayouts = [
            PermissionList::USERS_COL_EMAIL => 'email',
            PermissionList::USERS_COL_FIRSTNAME => 'first_name',
            PermissionList::USERS_COL_LASTNAME => 'last_name',
            PermissionList::USERS_COL_PHONE => 'phone_inr',
        ];

        // Handle direct "info" object in root
        if (array_key_exists('info', $data)) {
            $data['info'] = $this->hideFieldsInInfo($request, $data['info'], $permissionsShowPlayer);
        }

        // Handle "data" array structure
        if (array_key_exists('data', $data) && is_array($data['data'])) {
            $dataPartAfterUpdate = [];

            foreach ($data['data'] as $item) {
                // Hide fields in info object for show player permissions
                if (array_key_exists('info', $item)) {
                    $item['info'] = $this->hideFieldsInInfo($request, $item['info'], $permissionsShowPlayer);
                }

                // Hide fields in player.info for payout permissions
                if (array_key_exists('player', $item) && array_key_exists('info', $item['player'])) {
                    $item['player']['info'] = $this->hideFieldsInInfo($request, $item['player']['info'], $permissionsPayouts);
                }

                if (array_key_exists('details', $item)) {
                    $item['details']['details'] = $this->hideFieldsInInfo($request, $item['details']['details'], $permissionsPayouts);
                }

                // Hide email/phone fields in root level of item
                if (!$request->user()->can(PermissionList::USERS_COL_EMAIL)) {
                    if (isset($item['new_email'])) {
                        $item['new_email'] = null;
                    }
                    if (isset($item['old_email'])) {
                        $item['old_email'] = null;
                    }
                }

                if (!$request->user()->can(PermissionList::USERS_COL_PHONE)) {
                    if (isset($item['new_phone'])) {
                        $item['new_phone'] = null;
                    }
                    if (isset($item['old_phone'])) {
                        $item['old_phone'] = null;
                    }
                }

                $dataPartAfterUpdate[] = $item;
            }

            $data['data'] = $dataPartAfterUpdate;
        }

        return $data;
    }

    private function hideFieldsInInfo(Request $request, array $info, array $permissions): array
    {
        foreach ($permissions as $permission => $field) {
            if (array_key_exists($field, $info) && !$request->user()->can($permission)) {
                if ($field === 'phone') {
                    unset($info[$field]['phone']);
                    unset($info[$field]['phone_without_code']);
                }
                else {
                    $info[$field] = null;
                }
            }
        }

        return $info;
    }
}
