<?php declare(strict_types=1);


namespace App\Http\Entities;

use Illuminate\Support\Str;

/**
 * Class EntityTree
 * @package App\Event\Entity\EntityTree
 */
class EntityTree
{
    protected const MAP = [];

    protected const TYPE_INTEGER = 'integer';
    protected const TYPE_STRING = 'string';
    protected const TYPE_BOOLEAN = 'boolean';
    protected const TYPE_ARRAY = 'array';
    protected const TYPE_OBJECT = 'object';

    /**
     * EntityTree constructor.
     *
     * @param                    $data
     * @param string             $path
     */
    public function __construct(
        $data,
        string $path = ''
    ) {
        foreach ($data as $field => $value) {
            $className = static::MAP[$field]['class'] ?? null;
            $type = static::MAP[$field]['type'] ?? null;

            $dataToSet = null;

            if ($type === self::TYPE_ARRAY) {
                $dataToSet = [];
                foreach ($value as $key => $item) {
                    if ($className) {
                        /** @var EntityTree $entity */
                        $entity = new $className($item, "$path/$field/$key");
                        $dataToSet[$key] = $entity;
                    } else {
                        $dataToSet[$key] = $item;
                    }
                }
            } else if ($type === self::TYPE_OBJECT) {
                $dataToSet = new $className($value, "$path/$field");
            } else {
                $dataToSet = $value;
            }

            $setterName = sprintf("set%s", Str::ucfirst(Str::camel($field)));

            if ($dataToSet instanceof self) {
                $this->setData($setterName, $dataToSet);
            } else {
                $this->setData($setterName, $dataToSet);
            }
        }
    }

    /** {@inheritdoc} */
    public function offsetExists($offset): bool
    {
        return property_exists($this, $offset);
    }

    /** {@inheritdoc} */
    public function offsetGet($offset)
    {
        return $this->{$offset};
    }

    /** {@inheritdoc} */
    public function offsetSet($offset, $value): void
    {
        $this->{$offset} = $value;
    }

    /** {@inheritdoc} */
    public function offsetUnset($offset): void
    {
        $this->{$offset} = null;
    }

    /**
     * @return \ArrayIterator|\Traversable
     */
    public function getIterator()
    {
        return new \ArrayIterator((array)$this);
    }

    /**
     * @return array
     * @throws \JsonException
     */
    public function toArray(): array
    {
        return json_decode(json_encode($this, JSON_THROW_ON_ERROR), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @param string $setterName
     * @param mixed  $dataToSet
     */
    protected function setData(string $setterName, $dataToSet): void
    {
        $this->$setterName($dataToSet);
    }

    /**
     * @param $name
     * @param $arguments
     */
    public function __call($name, $arguments)
    {
        $this->methodDoesNotExistCallback($name, $arguments);
    }

    /**
     * @param string     $name
     * @param array|null $arguments
     */
    protected function methodDoesNotExistCallback(string $name, ?array $arguments = null): void
    {
        // Call handling of non-existent methods should be here. For example, writing to a log file.
    }
}
