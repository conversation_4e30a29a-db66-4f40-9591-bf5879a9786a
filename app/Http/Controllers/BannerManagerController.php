<?php

namespace App\Http\Controllers;

use App\Services\BannerManagementServiceHttpClient;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Class BannerManagerController
 * @package App\Http\Controllers
 */
class BannerManagerController extends Controller
{
    private BannerManagementServiceHttpClient $bannerClient;

    public function __construct(BannerManagementServiceHttpClient $bannerClient)
    {
        $this->bannerClient = $bannerClient;
    }

    public function BannerCreate(Request $request): JsonResponse
    {
        $response = $this->bannerClient->setJson($request->all())->doPostRequest('api/banners');
        return response()->json($response->content, $response->responseCode);
    }

    public function BannerUpdate(Request $request, $id): JsonResponse
    {
        $response = $this->bannerClient->setJson($request->all())->doPutRequest('api/banners/' . $id);
        return response()->json($response->content, $response->responseCode);
    }

    public function BannerChangeStatus(Request $request, $id): JsonResponse
    {
        $response = $this->bannerClient->setJson($request->all())
            ->doPutRequest('api/banners/' . $id . '/change-status');
        return response()->json($response->content, $response->responseCode);
    }

    public function BannerChangeStatusMany(Request $request): JsonResponse
    {
        $response = $this->bannerClient->setJson($request->all())
            ->doPutRequest('api/banners/change-status/many');
        return response()->json($response->content, $response->responseCode);
    }

    public function GetBanners(Request $request): JsonResponse
    {
        $response = $this->bannerClient->setJson($request->all())->doGetRequest('api/banners');
        return response()->json($response->content, $response->responseCode);
    }

    public function DeleteBanners(Request $request, $id): JsonResponse
    {
        $response = $this->bannerClient->doDeleteRequest('api/banners/' . $id);
        return response()->json($response->content, $response->responseCode);
    }
}
