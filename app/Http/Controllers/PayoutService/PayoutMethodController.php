<?php


namespace App\Http\Controllers\PayoutService;


use App\Http\Controllers\Controller;
use App\Http\Resources\Payouts\PayoutStatusResource;
use App\Services\Payout\PayoutMethodService;
use App\Services\Payout\WithdrawOrderService;
use Illuminate\Http\Request;

/**
 * Class PayoutMethodController
 * @package App\Http\Controllers\PayoutService
 */
final class PayoutMethodController extends Controller
{

    /**
     * @var PayoutMethodService
     */
    private PayoutMethodService $payoutMethodService;
    private WithdrawOrderService $withdrawOrderService;

    /**
     * PaymentMethodController constructor.
     *
     * @param PayoutMethodService  $payoutMethodService
     * @param WithdrawOrderService $withdrawOrderService
     */
    public function __construct(PayoutMethodService $payoutMethodService, WithdrawOrderService $withdrawOrderService)
    {
        $this->payoutMethodService = $payoutMethodService;
        $this->withdrawOrderService = $withdrawOrderService;
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function getMethods(Request $request): array
    {
        return $this->payoutMethodService->getMethods($request->all());
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function getAdminAggregators(Request $request): array
    {
        return $this->payoutMethodService->getAdminAggregators($request->all());
    }

    /**
     * @param Request $request
     * @param string $token
     *
     * @return array
     * @throws \Exception
     */
    public function update(Request $request, string $token): array
    {
        return $this->payoutMethodService->update($request->all(), $token);
    }

    public function getAdminPayouts(Request $request): \Illuminate\Http\Resources\Json\AnonymousResourceCollection
    {
        $payouts = $this->withdrawOrderService->getInternalPayoutStatuses($request['payouts_ids']);

        return PayoutStatusResource::collection($payouts);
    }
}
