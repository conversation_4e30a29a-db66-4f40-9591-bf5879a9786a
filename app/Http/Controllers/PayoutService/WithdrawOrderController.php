<?php

namespace App\Http\Controllers\PayoutService;

use App\Http\Controllers\Controller;
use App\Services\Payout\WithdrawOrderService;
use Illuminate\Http\Request;


/**
 * Class CurrencyController
 * @package App\Http\Controllers\PayoutService
 */
final class WithdrawOrderController extends Controller
{

    /**
     * @var WithdrawOrderService
     */
    private WithdrawOrderService $withdrawOrderService;

    /**
     * CurrencyController constructor.
     * @param WithdrawOrderService $withdrawOrderService
     */
    public function __construct(WithdrawOrderService $withdrawOrderService)
    {
        $this->withdrawOrderService = $withdrawOrderService;
    }

    /**
     * @param Request $request
     * @return array|\Illuminate\Http\JsonResponse|null
     * @throws \Exception
     */
    public function methodsToChange(Request $request)
    {
        return $this->withdrawOrderService->getMethodsToChange($request->order_ids);
    }

    /**
     * @param Request $request
     *
     * @return array
     */
    public function orderRetry(Request $request): array
    {
        return $this->withdrawOrderService->orderRetry($request->all());
    }


    /**
     * @param Request $request
     *
     * @return array
     */
    public function orderClose(Request $request): array
    {
        $clientId = $request->attributes->get('token') && $request->attributes->get('token')->client !== null ? $request->attributes->get('token')->client->real_client_id : env('ULTIMATE_CLIENT', 2);
        return $this->withdrawOrderService->orderClose($request->all(), $clientId);
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function methodsToChangeUpdate(Request $request): array
    {
        return $this->withdrawOrderService->getMethodsToChangeUpdate($request->order_ids, $request->withdraw_method_token);
    }
}
