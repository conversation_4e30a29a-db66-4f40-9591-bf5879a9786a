<?php

namespace App\Http\Controllers\PayoutService;

use App\Http\Controllers\Controller;
use App\Services\Payout\CurrencyService;
use Illuminate\Http\Request;


/**
 * Class CurrencyController
 * @package App\Http\Controllers\PayoutService
 */
final class CurrencyController extends Controller
{

    /**
     * @var CurrencyService
     */
    private CurrencyService $currencyService;

    /**
     * CurrencyController constructor.
     * @param CurrencyService $currencyService
     */
    public function __construct(CurrencyService $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function getCurrencies(Request $request): array
    {
        return $this->currencyService->getCurrencies($request->all());
    }
}
