<?php

namespace App\Http\Controllers\FreeSpins;


use App\Dto\FreeSpinsAvailableAggregatorDto;
use App\Dto\FreeSpinsAvailableProvidersDto;
use App\Http\Controllers\Controller;
use App\Http\Resources\FreeSpins\AvailableGamesCollection;
use App\Http\Resources\FreeSpins\AvailableProvidersCollection;
use App\Http\Resources\FreeSpins\AvailableSuppliersResource;
use App\Services\FreeSpins\FreeSpinsService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Validation\ValidationException;
use JsonException;

/**
 * Class FreeSpinsController
 * @package App\Http\Controllers\FreeSpins
 */
class FreeSpinsController extends Controller
{
    /**
     * @var FreeSpinsService
     */
    private FreeSpinsService $freeSpinsService;

    /**
     * FreeSpinsController constructor.
     * @param FreeSpinsService $freeSpinsService
     */
    public function __construct(FreeSpinsService $freeSpinsService)
    {
        $this->freeSpinsService = $freeSpinsService;
    }

    /**
     * @param Request $request
     * @return AvailableGamesCollection
     * @throws ValidationException
     */
    public function availableGames(Request $request): AvailableGamesCollection
    {
        $provider_id = $request->route('provider_id');
        $request->merge(['provider_id' => $provider_id]);
        $this->validate($request, [
            'provider_id' => 'required|numeric',
        ]);
        return new AvailableGamesCollection($this->freeSpinsService->availableGames($provider_id));

    }

    /**
     * @return AvailableProvidersCollection
     * @throws ValidationException
     */
    public function availableProviders(Request $request): AvailableProvidersCollection
    {
        $requestData = $this->validate($request, [
            'supplier' => 'numeric|nullable',
            'aggregator' => 'string|nullable',
        ]);

        $freeSpinsAvailableProvidersDto = new FreeSpinsAvailableProvidersDto(
            $requestData['supplier'] ?? null,
            $requestData['aggregator'] ?? null,
        );

        return new AvailableProvidersCollection(
            $this->freeSpinsService->availableProviders($freeSpinsAvailableProvidersDto)
        );
    }

    public function availableSuppliers(): ResourceCollection
    {
        return AvailableSuppliersResource::collection(
            $this->freeSpinsService->getFreeSpinsAvailableSuppliers()
        );
    }

    public function availableSupplierAggregators(int $supplierId): array
    {
        $freeSpinsAvailableAggregatorDto = new FreeSpinsAvailableAggregatorDto($supplierId);

        return $this->freeSpinsService->getFreeSpinsAvailableSupplierAggregators($freeSpinsAvailableAggregatorDto);
    }

    public function availableAggregators(): array
    {
        return $this->freeSpinsService->getFreeSpinsAvailableAggregators();
    }

    /**
     * @throws JsonException
     */
    public function gamesAdditionalData(Request $request): array
    {
        $slots_id = $request->slots_id;
        $game_uuid = $request->external_id;
        $currency = $request->currency;

        return $this->freeSpinsService->getGamesAdditionalData($slots_id, $game_uuid, $currency);
    }

}
