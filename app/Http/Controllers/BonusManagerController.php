<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\BonusManagementServiceHttpClient;
use Deversin\Signature\SignatureInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpKernel\Exception\HttpException;

/**
 * Class BonusManagerController
 * @package App\Http\Controllers
 */
class BonusManagerController extends Controller
{
    private BonusManagementServiceHttpClient $bonusClient;

    public function __construct(BonusManagementServiceHttpClient $bonusClient)
    {
        $this->bonusClient = $bonusClient;
    }

    public function FreeSpinList(Request $request)
    {
        $author = $request->get('author');
        $users = [];
        if ($author) {
            $user = User::query()->where('login', $author)->first();
            if ($user) {
                $users[$user->id] = $user;
                $request->request->add(['author_id' => $user->id]);
            } else {
                $request->request->add(['author_id' => 0]);
            }
        }
        $updateByAuthor = $request->get('updatedBy');
        if ($updateByAuthor) {
            $updateByUser = User::query()->where('login', $updateByAuthor)->first();
            if ($updateByUser) {
                $users[$updateByUser->id] = $updateByUser;
                $request->request->add(['updated_by_author_id' => $updateByUser->id]);
            } else {
                $request->request->add(['updated_by_author_id' => 0]);
            }
        }

        $responseDTO = $this->bonusClient->setJson($request->all())->doGetRequest('api/free-spins');
        if (!$responseDTO->isSuccess) {
            throw new HttpException(400, 'Bad response from bonus management.');
        }

        $content = $responseDTO->content;
        $userIds = array_keys($users);
        foreach ($content['data'] ?? [] as $freeSpin) {
            if (($freeSpin['author_id'] ?? null) && !in_array($freeSpin['author_id'], $userIds)) {
                $userIds[] = $freeSpin['author_id'];
            }
            if (($freeSpin['updated_by_author_id'] ?? null) && !in_array($freeSpin['updated_by_author_id'], $userIds)) {
                $userIds[] = $freeSpin['updated_by_author_id'];
            }
        }

        $users = User::query()->whereIn('id', $userIds)
            ->select(['id', 'login'])->pluck('login', 'id')->toArray();

        foreach ($content['data'] as $key => $freeSpin) {
            if (isset($freeSpin['author_id']) && isset($users[$freeSpin['author_id']])) {
                $content['data'][$key]['author'] = [
                    'id' => $freeSpin['author_id'],
                    'login' => $users[$freeSpin['author_id']]
                ];
            } else {
                $content['data'][$key]['author'] = null;
            }

            if (isset($freeSpin['updated_by_author_id']) && isset($users[$freeSpin['updated_by_author_id']])) {
                $content['data'][$key]['updatedBy'] = [
                    'id' => $freeSpin['updated_by_author_id'],
                    'login' => $users[$freeSpin['updated_by_author_id']]
                ];
            } else {
                $content['data'][$key]['updatedBy'] = null;
            }
        }
        return $content;
    }

    public function FreeSpinCreate(Request $request)
    {
        /** @var User $admin */
        $admin = Auth::user();
        if ($admin) {
            $request->request->add(['author_email' => $admin->email]);
        }
        $response = $this->bonusClient->setJson($request->all())->doPostRequest('api/free-spins/add');

        return response()->json($response->content, $response->responseCode);
    }

    public function FreeSpinResend(Request $request)
    {
        return $this->bonusClient->setJson($request->all())->doPostRequest('api/free-spins/resend')->content;
    }

    public function FreeSpinAttach(Request $request)
    {
        return $this->bonusClient->setJson($request->all())->doPostRequest('api/free-spins/attach')->content;
    }

    public function FreeSpinPlayerBoundHistory(Request $request, $id)
    {
        return $this->bonusClient->setJson($request->all())->doGetRequest('api/free-spins/player-bound-history/' . $id)->content;
    }

    public function FreeSpinPlayerBoundActive(Request $request, $id)
    {
        return $this->bonusClient->setJson($request->all())->doGetRequest('api/free-spins/player-bound-active/' . $id)->content;
    }

    public function FreeSpinShow(Request $request, $id)
    {
        return $this->bonusClient->setJson($request->all())->doGetRequest('api/free-spins/show/' . $id)->content;
    }

    public function FreeSpinCheckName(Request $request)
    {
        return $this->bonusClient->setJson($request->all())->doPostRequest('api/free-spins/check-free-spin-name')->content;
    }

    public function FreeSpinGetPlayersById(Request $request, $id)
    {
        return $this->bonusClient->setJson($request->all())->doGetRequest('api/free-spins/get-players/' . $id)->content;
    }

    public function FreeSpinUpdate(Request $request, $id)
    {
        /** @var User $admin */
        $admin = Auth::user();
        if ($admin) {
            $request->request->add(['author_email' => $admin->email]);
        }

        $response = $this->bonusClient->setJson($request->all())->doPutRequest('api/free-spins/update/' . $id);

        return response()->json($response->content, $response->responseCode);
    }

    public function FreeSpinHistoryLogs(Request $request, $id): JsonResponse
    {
        $responseDTO = $this->bonusClient->setJson($request->all())
            ->doGetRequest('api/free-spins/get-history-logs/' . $id);
        if (!$responseDTO->isSuccess) {
            throw new HttpException(400, 'Bad response from bonus management.');
        }
        return response()->json($responseDTO->content, $responseDTO->responseCode);
    }

    public function FreeSpinCancel(Request $request, $id)
    {
        /** @var User $admin */
        $admin = Auth::user();
        if ($admin) {
            $request->request->add(['author_email' => $admin->email]);
        }
        return $this->bonusClient->setJson($request->all())->doPutRequest('api/free-spins/delete/' . $id)->content;
    }

    public function GetТemplates(Request $request)
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/get-templates');

        return response()->json($response->content, $response->responseCode);
    }

    public function GetBonusList(Request $request)
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses');

        return response()->json($response->content, $response->responseCode);
    }

    public function BonusCreate(Request $request): JsonResponse
    {
        /** @var User $admin */
        $admin = Auth::user();
        if ($admin) {
            $request->request->add(['author_email' => $admin->email]);
            $request->request->add(['author_id' => $admin->id]);
        }

        $response = $this->bonusClient->setJson($request->all())->doPostRequest('api/bonuses/add');

        return response()->json($response->content, $response->responseCode);
    }

    public function BonusUpdate(Request $request, $id): JsonResponse
    {
        /** @var User $admin */
        $admin = Auth::user();
        if ($admin) {
            $request->request->add(['author_email' => $admin->email]);
            $request->request->add(['author_id' => $admin->id]);
        }

        $response = $this->bonusClient->setJson($request->all())->doPutRequest('api/bonuses/' . $id);

        return response()->json($response->content, $response->responseCode);

    }

    public function BonusHistoryLogs(Request $request, $id): JsonResponse
    {
        $responseDTO = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/get-history-logs/' . $id);
        if (!$responseDTO->isSuccess) {
            throw new HttpException(400, 'Bad response from bonus management.');
        }
        return response()->json($responseDTO->content, $responseDTO->responseCode);
    }

    public function HistoryLogs(Request $request, $id): JsonResponse
    {
        $responseDTO = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/get-history-logs/' . $id);
        if (!$responseDTO->isSuccess) {
            throw new HttpException(400, 'Bad response from bonus management.');
        }

        return response()->json($responseDTO->content, $responseDTO->responseCode);

    }

    public function massApplyBonus(Request $request): JsonResponse
    {
        $file = $request->file('file');
        $params =  [
            'bonus_id' => $request->bonus_id,
            'bonus_type' => $request->bonus_type,
        ];

        $httpRequest = Http::baseUrl(config('external-services.bonus_management.host'));

        $signature = app(SignatureInterface::class);
        $sign = $signature->sign(json_encode($params, JSON_THROW_ON_ERROR));
        $httpRequest->withHeaders([SignatureInterface::SIGNATURE_HEADER => $sign]);

        $response = $httpRequest->attach(
            'file', file_get_contents($file), $file->getClientOriginalName()
        )->post('api/bonuses/mass-apply-admin', $params);

        return response()->json($response->object(), $response->status());
    }

    public function applyBonusForPlayer(Request $request, $bonusType): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doPostRequest('api/bonuses/apply-admin/' . $bonusType);

        return response()->json($response->content, $response->responseCode);
    }

    public function bonusDetails(Request $request, $bonusType, $bonusId): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/' . $bonusType . '/details/' . $bonusId);

        return response()->json($response->content, $response->responseCode);
    }

    public function getBonusBalances(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/balances');

        return response()->json($response->content, $response->responseCode);
    }

    public function createBetbyBonus(Request $request, $id): JsonResponse
    {
        /** @var User $admin */
        $admin = Auth::user();
        if ($admin) {
            $request->request->add(['author_email' => $admin->email]);
        }
        $response = $this->bonusClient->setJson($request->all())->doPostRequest('api/bonuses/add-betby-template-bonus/' . $id);

        return response()->json($response->content, $response->responseCode);
    }

    public function BonusInfoCreate(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doPostRequest('api/bonuses/info');
        return response()->json($response->content, $response->responseCode);
    }

    public function BonusInfoUpdate(Request $request, $id): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doPutRequest('api/bonuses/info/' . $id);
        return response()->json($response->content, $response->responseCode);
    }

    public function GetBonusInfoList(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/info');
        return response()->json($response->content, $response->responseCode);
    }

    public function GetBonusInfoEnabledList(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/info/enabled');
        return response()->json($response->content, $response->responseCode);
    }

    public function GetBonusInfoEnabledListForPlayer(Request $request, $id): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/info/enabled/' . $id);
        return response()->json($response->content, $response->responseCode);
    }

    public function GetBonusInfoEnabledFullListForPlayer(Request $request, $id): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/info/enabled-all/' . $id);
        return response()->json($response->content, $response->responseCode);
    }

    public function BannerCreate(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doPostRequest('api/settings/banners');
        return response()->json($response->content, $response->responseCode);
    }

    public function BannerUpdate(Request $request, $id): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doPutRequest('api/settings/banners/' . $id);
        return response()->json($response->content, $response->responseCode);
    }

    public function BannerChangeStatus(Request $request, $id): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())
            ->doPutRequest('api/settings/banners/' . $id . '/change-status');
        return response()->json($response->content, $response->responseCode);
    }

    public function BannerChangeStatusMany(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())
            ->doPutRequest('api/settings/banners/change-status/many');
        return response()->json($response->content, $response->responseCode);
    }

    public function GetBanners(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/settings/banners');
        return response()->json($response->content, $response->responseCode);
    }

    public function DeleteBanners(Request $request, $id): JsonResponse
    {
        $response = $this->bonusClient->doDeleteRequest('api/settings/banners/' . $id);
        return response()->json($response->content, $response->responseCode);
    }

    public function PromoCreate(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doPostRequest('api/bonuses/promo');
        return response()->json($response->content, $response->responseCode);
    }

    public function PromoUpdate(Request $request, $id): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doPutRequest('api/bonuses/promo/' . $id);
        return response()->json($response->content, $response->responseCode);
    }

    public function GetPromos(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/promo');
        return response()->json($response->content, $response->responseCode);
    }

    public function CheckPromo(Request $request): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doPatchRequest('api/bonuses/promo');
        return response()->json($response->content, $response->responseCode);
    }

    public function getBonusSlots(Request $request, $id): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doGetRequest('api/bonuses/' . $id . '/slots');
        return response()->json($response->content, $response->responseCode);
    }

    public function updateBonusSlots(Request $request, $id): JsonResponse
    {
        $response = $this->bonusClient->setJson($request->all())->doPutRequest('api/bonuses/' . $id . '/slots');
        return response()->json($response->content, $response->responseCode);
    }
}
