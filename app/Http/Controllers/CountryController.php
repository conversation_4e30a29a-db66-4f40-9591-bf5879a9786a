<?php
namespace App\Http\Controllers;

use App\Http\Resources\CountryResource;
use App\Services\CountryService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

final class CountryController
{
    private CountryService $countryService;

    public function __construct(CountryService $countryService)
    {
        $this->countryService = $countryService;
    }

    /**
     * @throws ModelNotFoundException
     */
    public function bindProvidersToCountry(int $id): JsonResponse
    {
        $this->countryService->bindProvidersToCountry($id);

        return response()->json(['status' => 'ok']);
    }

    public function getCountries(): AnonymousResourceCollection
    {
        return CountryResource::collection($this->countryService->getCountries());
    }
}
