<?php

namespace App\Http\Controllers\PaymentService;

use App\Http\Controllers\Controller;
use App\Services\Payment\PaymentMethodsService;
use AppV2\Core\Http\Resources\PaymentSystemNameResource;
use AppV2\Core\Services\PaymentSystemEncryptService;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;


/**
 * Class PaymentMethodsController
 * @package App\Http\Controllers\PaymentService
 */
final class PaymentMethodsController extends Controller
{
    /**
     * @var PaymentMethodsService
     */
    private PaymentMethodsService $paymentMethodsService;

    /**
     * PaymentMethodsController constructor.
     * @param PaymentMethodsService $paymentMethodsService
     */
    public function __construct(PaymentMethodsService $paymentMethodsService)
    {
        $this->paymentMethodsService = $paymentMethodsService;
    }

    /**
     * @param Request $request
     * @param string $iso
     * @return array
     * @throws \Exception
     */
    public function show(Request $request, string $iso): array
    {
        return $this->paymentMethodsService->show($request->all(), $iso);
    }

    /**
     * @param Request $request
     * @param string $iso
     * @return array
     * @throws \Exception
     */
    public function showAggregatorsPayments(Request $request, string $iso): array
    {
        return $this->paymentMethodsService->showAggregatorsPayments($request->all(), $iso);
    }

    /**
     * @throws \Exception
     */
    public function getDeterminingUser(Request $request): array
    {
        return $this->paymentMethodsService->getDeterminingUser($request->all());
    }

    public function decrypt(Request $request, PaymentSystemEncryptService $paymentSystemEncryptService)
    {
        $this->validate($request, [
            'value' => ['required', 'string'],
        ]);

        try {
            $result = $paymentSystemEncryptService->decryptName($request->get('value'));
        } catch (DecryptException $exception) {
            return response()->json(
                [
                    'message' => 'Could not decrypt data',
                    'code' => Response::HTTP_UNPROCESSABLE_ENTITY
                ],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        return new PaymentSystemNameResource($result);
    }
}
