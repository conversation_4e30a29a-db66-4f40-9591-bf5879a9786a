<?php


namespace App\Http\Controllers\PaymentService;


use App\Http\Controllers\Controller;
use App\Services\Payment\PresetService;
use Illuminate\Http\Request;

/**
 * Class PresetController
 * @package App\Http\Controllers\PaymentService
 */
final class PresetController extends Controller
{

    /**
     * @var PresetService
     */
    protected $presetService;

    /**
     * PresetController constructor.
     * @param PresetService $presetService
     */
    public function __construct(PresetService $presetService)
    {
        $this->presetService = $presetService;
    }

    /**
     * @param string $token
     * @return array
     * @throws \Exception
     */
    public function show(Request $request, string $token): array
    {
        return $this->presetService->show($request->all(), $token);
    }

    /**
     * @param Request $request
     * @param string $token
     * @return array
     */
    public function store(Request $request, string $token): array
    {
        return $this->presetService->store($request->all(), $token);
    }

    /**
     * @param Request $request
     * @param string $token
     * @return array
     */
    public function destroy(Request $request, string $token): array
    {
        return $this->presetService->destroy($request->all(), $token);
    }
}
