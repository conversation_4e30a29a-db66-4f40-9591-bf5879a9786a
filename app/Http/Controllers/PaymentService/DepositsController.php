<?php


namespace App\Http\Controllers\PaymentService;


use App\Http\Controllers\Controller;
use App\Services\Payment\DepositsService;
use Illuminate\Http\Request;

/**
 * Class DepositsController
 * @package App\Http\Controllers\PaymentService
 */
final class DepositsController extends Controller
{
    /**
     * @var DepositsService
     */
    private DepositsService $depositsService;

    /**
     * DepositsController constructor.
     * @param DepositsService $depositsService
     */
    public function __construct(DepositsService $depositsService)
    {
        $this->depositsService = $depositsService;
    }

    /**
     * @param Request $request
     * @param $label
     * @return array
     * @throws \Exception
     */
    public function showFailed(Request $request, $label): array
    {
        return $this->depositsService->showFailed($request->all(), $label);
    }
}
