<?php

namespace App\Http\Controllers\PaymentService;

use App\Http\Controllers\Controller;
use App\Services\Payment\PaymentMethodService;
use Illuminate\Http\Request;

/**
 * Class PaymentMethodController
 * @package App\Http\Controllers\PaymentService
 */
final class PaymentMethodController extends Controller
{
    /**
     * @var PaymentMethodService
     */
    private PaymentMethodService $paymentMethodService;

    /**
     * PaymentMethodController constructor.
     * @param PaymentMethodService $paymentMethodService
     */
    public function __construct(PaymentMethodService $paymentMethodService)
    {
        $this->paymentMethodService = $paymentMethodService;
    }

    /**
     * @param string $token
     * @return array
     */
    public function show(Request $request, string $token): array
    {
        return $this->paymentMethodService->show($request->all(), $token);
    }

    /**
     * @param Request $request
     * @param string $token
     * @return array
     * @throws \Exception
     */
    public function update(Request $request, string $token): array
    {

        return $this->paymentMethodService->update($request->all(), $token);
    }
}
