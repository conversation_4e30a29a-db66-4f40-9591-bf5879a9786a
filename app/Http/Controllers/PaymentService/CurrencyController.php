<?php

namespace App\Http\Controllers\PaymentService;

use App\Http\Controllers\Controller;
use App\Services\Payment\CurrencyService;
use Illuminate\Http\Request;
use Skipper\Exceptions\DomainException;


/**
 * Class CurrencyController
 * @package App\Http\Controllers\PaymentService
 */
final class CurrencyController extends Controller
{

    /**
     * @var CurrencyService
     */
    private CurrencyService $currencyService;

    /**
     * CurrencyController constructor.
     * @param CurrencyService $currencyService
     */
    public function __construct(CurrencyService $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    /**
     * @param Request $request
     * @return array
     * @throws DomainException
     * @throws \Exception
     */
    public function getCurrencies(Request $request): array
    {
        return $this->currencyService->getCurrencies($request->all());
    }
}
