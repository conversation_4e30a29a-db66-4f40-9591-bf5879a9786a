<?php

namespace App\Http\Controllers;

use App\Services\ThrottlerWhitelistIpService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class InitializeController extends Controller
{
    private ThrottlerWhitelistIpService $throttlerWhitelistIpService;

    public function __construct(ThrottlerWhitelistIpService $throttlerWhitelistIpService)
    {
        $this->throttlerWhitelistIpService = $throttlerWhitelistIpService;
    }

    public function initialize(Request $request): JsonResponse
    {
        $ipAddress = $request->has('ip') ? $request->input('ip') : $request->ip();

        $this->throttlerWhitelistIpService->updateWhitelistIpAddress($ipAddress);

        return response()->json(['message' => 'IP address successfully whitelisted']);
    }

    /**
     * @return JsonResponse
     */
    public function getWhitelistedIpAddress(): JsonResponse
    {
        return response()->json(['ipAddress' => $this->throttlerWhitelistIpService->getWhitelistedIpAddress()]);
    }
}
