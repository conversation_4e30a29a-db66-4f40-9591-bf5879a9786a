<?php

declare(strict_types=1);

namespace App\Http\Controllers\Payment;

use App\Core\Validator\ValidationService;
use App\Enums\Payments\PaymentBalanceTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Resources\Payment\PaymentLogResource;
use App\Http\Resources\Payment\PaymentStatisticForBetsResource;
use App\Services\Cache\SingleFlight;
use App\Services\Payment\PaymentService;
use App\Services\Statistics\Dto\Bets\SearchPaymentsStatisticForBetsDto;
use App\Services\Statistics\Dto\Logs\SearchPaymentLogsDto;
use App\Validators\Payment\SearchPaymentsLogsValidatorConfig;
use App\Validators\Payment\SearchPaymentsStatisticForBetsValidatorConfig;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Validation\ValidationException;
use Throwable;

class PaymentController extends Controller
{
    private PaymentService $paymentService;
    private ValidationService $validationService;
    private SingleFlight $singleFlight;

    public function __construct(
        PaymentService $paymentService,
        ValidationService $validationService,
        SingleFlight $singleFlight
    ) {
        $this->paymentService = $paymentService;
        $this->validationService = $validationService;
        $this->singleFlight = $singleFlight;
    }

    /**
     * @throws ValidationException
     * @throws GuzzleException
     * @throws Exception
     * @throws Throwable
     */
    public function searchPaymentsLogs(Request $request): ResourceCollection
    {
        $validatedData = $this->validationService->createValidator(
            $request->all(),
            SearchPaymentsLogsValidatorConfig::class
        )
            ->validate();
        ksort($validatedData);

        $searchDto = new SearchPaymentLogsDto(
            [
                'playerId' => (int)$validatedData['player_id'],
                'startDate' => array_key_exists('start_date', $validatedData) ? Carbon::parse(
                    $validatedData['start_date']
                ) : null,
                'endDate' => array_key_exists('end_date', $validatedData) ? Carbon::parse(
                    $validatedData['end_date']
                ) : null,
                'limit' => (int)$validatedData['pagination']['limit'],
                'offset' => array_key_exists('offset', $validatedData['pagination']) ?
                    (int)$validatedData['pagination']['offset'] : null,
                'transactionType' => $validatedData['transaction_type'] ?? null,
                'bonusType' => $validatedData['bonus_type'] ?? null,
                'playerUuid' => $validatedData['player_uuid'] ?? null,
            ]
        );

        $cacheGroup = "admin:statistics:payment-logs";
        $cacheKey = md5(json_encode($validatedData, JSON_THROW_ON_ERROR));
        $paymentsLogs = $this->singleFlight->call(
            $cacheGroup,
            $cacheKey,
            function () use ($searchDto) {
                return $this->paymentService->searchPaymentsLogs($searchDto);
            },
            config('singleflight.statistics.default'),
        );

        $resultArray = $paymentsLogs === null ? [] : $paymentsLogs->getIterator()
            ->getArrayCopy();

        return PaymentLogResource::collection($resultArray)
            ->additional(
                [
                    'meta' => [
                        'limit' => (int)$validatedData['pagination']['limit'],
                        'offset' => (int)$validatedData['pagination']['offset'],
                    ]
                ]
            );
    }

    /**
     * @throws ValidationException
     * @throws GuzzleException
     * @throws Exception
     * @throws Throwable
     */
    public function searchPaymentsStatisticForBets(Request $request): ResourceCollection
    {
        $validatedData = $this->validationService->createValidator(
            $request->all(),
            SearchPaymentsStatisticForBetsValidatorConfig::class
        )
            ->validate();
        ksort($validatedData);

        $searchDto = new SearchPaymentsStatisticForBetsDto(
            [
                'playerId' => (int)$validatedData['player_id'],
                'balanceType' => new PaymentBalanceTypeEnum($validatedData['balance_type']),
                'limit' => (int)$validatedData['pagination']['limit'],
                'offset' => (int)$validatedData['pagination']['offset'],
                'startDate' => $validatedData['start_date'] ?? null,
                'endDate' => $validatedData['end_date'] ?? null,
            ]
        );

        $cacheGroup = "admin:statistics:bets-statistics";
        $cacheKey = md5(json_encode($validatedData, JSON_THROW_ON_ERROR));

        $paymentsStatistics = $this->singleFlight->call(
            $cacheGroup,
            $cacheKey,
            function () use ($searchDto) {
                return $this->paymentService->searchPaymentsStatisticsForBets($searchDto);
            },
            config('singleflight.statistics.default'),
        );

        $resultArray = $paymentsStatistics === null ? [] : $paymentsStatistics->getIterator()
            ->getArrayCopy();

        return PaymentStatisticForBetsResource::collection($resultArray)
            ->additional(
                [
                    'meta' => [
                        'limit' => (int)$validatedData['pagination']['limit'],
                        'offset' => (int)$validatedData['pagination']['offset'],
                    ]
                ]
            );
    }
}
