<?php
namespace App\Http\Controllers;

use App\Http\Resources\Segment\SegmentsResource;
use App\Services\SegmentService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

final class SegmentController
{
    /**
     * @var SegmentService
     */
    private SegmentService $segment;

    public function __construct(SegmentService $segment)
    {
        $this->segment = $segment;
    }

    /**
     * @return AnonymousResourceCollection
     */
    public function getAll(): AnonymousResourceCollection
    {
        return SegmentsResource::collection($this->segment->getAll());
    }
}
