<?php declare(strict_types=1);

namespace App\Http\Controllers\PaymentManagement;

use App\Http\Controllers\Controller;
use App\Services\PaymentManagement\PaymentManagementService;
use Illuminate\Http\Request;

final class PaymentManagementController extends Controller
{

    /**
     * @var PaymentManagementService
     */
    private PaymentManagementService $paymentManagementService;

    /**
     * CurrencyController constructor.
     * @param PaymentManagementService $paymentManagementService
     */
    public function __construct(PaymentManagementService $paymentManagementService)
    {
        $this->paymentManagementService = $paymentManagementService;
    }


    /**
     * @throws \Exception
     */
    public function getAvailablePaymentMethods(Request $request): array
    {
        return $this->paymentManagementService->getAvailablePaymentMethods($request->all());
    }

    /**
     * @throws \Exception
     */
    public function getAvailablePaymentAggregators(Request $request): array
    {
        return $this->paymentManagementService->getAvailablePaymentAggregators($request->all());
    }

    /**
     * @throws \Exception
     */
    public function createPaymentSystem(Request $request): array
    {
        return $this->paymentManagementService->createPaymentSystem($request->post(), $request->allFiles());
    }

    /**
     * @throws \Exception
     */
    public function createDuplicatePaymentSystem($id): array
    {
        return $this->paymentManagementService->createDuplicatePaymentSystem($id);
    }

    /**
     * @throws \Exception
     */
    public function updatePaymentSystem(Request $request, $id): array
    {
        return $this->paymentManagementService->updatePaymentSystem($request->post(), $id, $request->allFiles());
    }

    /**
     * @throws \Exception
     */
    public function getPaymentPresets(Request $request, $id): array
    {
        return $this->paymentManagementService->getPaymentPresets($request->all(), $id);
    }

    /**
     * @throws \Exception
     */
    public function savePaymentPresets(Request $request, $id): array
    {
        return $this->paymentManagementService->savePaymentPresets($request->all(), $id);
    }

    /**
     * @throws \Exception
     */
    public function listPaymentMethod(Request $request): array
    {
        return $this->paymentManagementService->listPaymentMethod($request->all());
    }

    /**
     * @throws \Exception
     */
    public function getPaymentMethod($id): array
    {
        return $this->paymentManagementService->getPaymentMethod($id);
    }

    /**
     * @throws \Exception
     */
    public function deletePaymentMethod($id, Request $request): array
    {
        return $this->paymentManagementService->deletePaymentMethod($id, (array)$request->query());
    }
}
