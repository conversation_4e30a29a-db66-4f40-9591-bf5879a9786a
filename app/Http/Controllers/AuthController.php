<?php declare(strict_types=1);

namespace App\Http\Controllers;


use App\Http\Resources\AuthResource;
use App\Http\Services\AuthService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class AuthController extends Controller
{
    private AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * @param Request $request
     *
     * @return AuthResource
     * @throws \Exception
     */
    public function refresh(Request $request): AuthResource
    {
        $this->validate($request, [
            'refresh_token' => ['required', 'string'],
            'cluster_connection' => ['string', 'nullable'],
        ]);

        $authResponseDto = $this->authService->refresh(
            $request->input('refresh_token'),
            $request->input('cluster_connection'),
        );

        return new AuthResource($authResponseDto);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function logout(Request $request): JsonResponse
    {
        $this->authService->logout();

        return response()->json([
            'status' => 'ok',
        ]);
    }

    /**
     * @param Request $request
     *
     * @return AuthResource
     * @throws \Exception
     */
    public function login(Request $request): AuthResource
    {
        $this->validate($request, [
            'username' => ['required', 'string'],
            'password' => ['required', 'string'],
        ]);

        $loginResponseDto = $this->authService->login($request->input('username'), $request->input('password'));

        return new AuthResource($loginResponseDto);
    }
}
