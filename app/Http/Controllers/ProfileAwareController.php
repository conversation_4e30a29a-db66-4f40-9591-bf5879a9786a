<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use WhiteLabelAdmin\Controllers\UserController;
use WhiteLabelAdmin\Entities\PermissionList;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;

final class ProfileAwareController
{
    private const ME_PART = 'me';

    /**
     * @var UserController
     */
    private UserController $users;

    public function __construct(UserController $users)
    {
        $this->users = $users;
    }

    /**
     * @param Request $request
     * @param string $userId
     * @return Response
     * @throws UnauthorizedException
     * @throws \Skipper\Exceptions\InvalidArgumentException
     * @throws \Skipper\Exceptions\SymfonyValidationException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function update(Request $request, string $userId): Response
    {
        return response()->json($this->users->updateUser($this->getRequesterId($userId, $request, PermissionList::MODERATORS_EDIT)));
    }

    /**
     * @param Request $request
     * @param string $userId
     * @return Response
     * @throws UnauthorizedException
     * @throws \Skipper\Exceptions\SymfonyValidationException
     */
    public function get(Request $request, string $userId): Response
    {
        return response()->json($this->users->getUserProfile($this->getRequesterId($userId, $request, PermissionList::MODERATORS_LIST)));
    }

    /**
     * @param Request $request
     * @param string $userId
     * @return Response
     * @throws UnauthorizedException
     * @throws \Skipper\Exceptions\SymfonyValidationException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function delete(Request $request, string $userId): Response
    {
        return response()->json($this->users->deleteUser($this->getRequesterId($userId, $request, PermissionList::MODERATORS_DELETE)));
    }

    /**
     * @param Request $request
     * @param string $userId
     * @return Response
     * @throws UnauthorizedException
     * @throws \Skipper\Exceptions\SymfonyValidationException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function password(Request $request, string $userId): Response
    {
        return response()->json($this->users->changePassword($this->getRequesterId($userId, $request, PermissionList::MODERATORS_EDIT)));
    }

    /**
     * @param string $userId
     * @param Request $request
     * @param string $permission
     * @return int
     * @throws UnauthorizedException
     */
    private function getRequesterId(string $userId, Request $request, string $permission): int
    {
        $loggedUser = optional($request->user())->id;
        if ($loggedUser !== null && (self::ME_PART === $userId || $userId == $loggedUser)) {
            $userId = $loggedUser;
        } elseif ($request->user()->can($permission)) {
            $userId = (int)$userId;
        } else {
            throw new UnauthorizedException(new AccessDeniedHttpException('This require ' . $permission . ' privilege'));
        }

        return $userId;
    }
}
