<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Core\Validator\ValidationService;
use App\Services\BonusBalanceService;
use App\Services\Statistics\Dto\BonusBalanceDto;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Throwable;

class BonusBalanceController extends Controller
{
    private ValidationService $validationService;
    private BonusBalanceService $bonusBalanceService;

    public function __construct(ValidationService $validationService, BonusBalanceService $bonusBalanceService)
    {
        $this->validationService = $validationService;
        $this->bonusBalanceService = $bonusBalanceService;
    }

    /**
     * @throws ValidationException
     * @throws GuzzleException
     * @throws Exception
     * @throws Throwable
     */
    public function getBonusBalances(Request $request): array
    {
        $validatedData = $this->validationService->createValidator(
            $request->all(),
            [
                'bonus_external_id' => [
                    'required',
                    'integer',
                ],
                'pagination' => [
                    'required',
                    'array',
                ],
                'pagination.limit' => [
                    'required',
                    'integer',
                    'min:1',
                    'max:50',
                ],
                'pagination.page' => [
                    'filled',
                    'integer',
                    'min:0',
                    'max:50000',
                ],
                'with' => [
                    'nullable',
                    'string'
                ]
            ]
        )->validate();

        $bonusBalanceDto = new BonusBalanceDto($validatedData);

        return $this->bonusBalanceService->getBonusBalanceFromStatistic($bonusBalanceDto);
    }
}
