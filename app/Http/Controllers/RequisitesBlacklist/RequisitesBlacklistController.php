<?php declare(strict_types=1);


namespace App\Http\Controllers\RequisitesBlacklist;


use App\Http\Controllers\Controller;
use App\Http\Resources\RequisiteBlacklist\RequisiteBlacklistResource;
use App\Services\RequisitesBlacklist\RequisitesBlacklistService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

/**
 * Class RequisitesBlacklistController
 * @package App\Http\Controllers\RequisitesBlacklist
 */
class RequisitesBlacklistController extends Controller
{
    /**
     * @var RequisitesBlacklistService
     */
    private RequisitesBlacklistService $requisitesBlacklistService;

    /**
     * RequisitesBlacklistController constructor.
     * @param RequisitesBlacklistService $requisitesBlacklistService
     */
    public function __construct(RequisitesBlacklistService $requisitesBlacklistService)
    {
        $this->requisitesBlacklistService = $requisitesBlacklistService;
    }


    public function get(Request $request): RequisiteBlacklistResource
    {
        $pagination = $request->get('pagination');
        $requisite = $request->get('requisite');
        $result = $this->requisitesBlacklistService->get($requisite, $pagination);

        return  new RequisiteBlacklistResource($result);
    }

    /**
     * @throws ValidationException
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'requisite' => 'required|string',
            'comment' => 'nullable|string'
        ]);

        $adminId = Auth::id();
        $requisite = $request->get('requisite');
        $comment = $request->get('comment');

        $this->requisitesBlacklistService->create($adminId, $requisite, $comment);

        return response()->json()->setStatusCode(200);
    }

    public function destroy(Request $request, $id)
    {
        $deleteAdminId = Auth::id();

        $this->requisitesBlacklistService->destroy($deleteAdminId, $id);

        return response()->json()->setStatusCode(200);

    }

}
