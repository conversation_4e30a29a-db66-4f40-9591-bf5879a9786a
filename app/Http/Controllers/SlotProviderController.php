<?php

namespace App\Http\Controllers;

use App\Jobs\SlotsCategoryCacheResetJob;
use App\Services\SlotProviderService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class SlotProviderController extends Controller
{
    private SlotProviderService $slotProviderService;

    public function __construct(SlotProviderService $slotProviderService)
    {
        $this->slotProviderService = $slotProviderService;
    }

    /**
     * @throws ValidationException | ModelNotFoundException
     */
    public function changeEnable(Request $request): JsonResponse
    {
        $params = $this->validate($request, [
            'slot_provider_id' => 'required|integer',
            'country_id' => 'required|integer',
            'is_enabled' => 'required|boolean'
        ]);

        $this->slotProviderService
            ->updateIsEnabledParam($params['slot_provider_id'], $params['country_id'], $params['is_enabled']);

        $this->dispatch(new SlotsCategoryCacheResetJob($params));

        return response()->json(['status' => 'ok']);
    }

    /**
     * @throws ModelNotFoundException
     */
    public function deleteProviderLocation(int $id): JsonResponse
    {
        $this->slotProviderService->detachProviderFromTheCountry($id);

        $this->dispatch(new SlotsCategoryCacheResetJob(['country_id' => $id]));

        return response()->json(['status' => 'ok']);
    }
}
