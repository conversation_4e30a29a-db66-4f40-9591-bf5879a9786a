<?php

namespace App\Http\Services;

use App\Http\Clients\AlanBaseHttpClient;
use App\Repositories\SmartLinkChangeLogRepository;
use App\Services\Utilities\UrlService;
use AppV2\SmartLink\Dto\AlanbaseOfferDto;
use AppV2\SmartLink\Dto\SmartLinkHistoryDto;
use AppV2\SmartLink\Dto\SmartLinkHistoryResultDto;
use AppV2\SmartLink\Dto\SmartLinkItemDto;
use AppV2\SmartLink\Dto\SmartLinksPageDto;
use AppV2\SmartLink\Exceptions\SameDomainException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Collection;

class SmartLinkService
{
    protected SmartLinkChangeLogRepository $changeLogRepository;
    protected AlanBaseHttpClient $alanbaseClient;
    private UrlService $urlService;

    public function __construct(
        SmartLinkChangeLogRepository $changeLogRepository,
        AlanBaseHttpClient $httpClient,
        UrlService $urlService
    ) {
        $this->changeLogRepository = $changeLogRepository;
        $this->alanbaseClient = $httpClient;
        $this->urlService = $urlService;
    }

    public function getPage(int $page, int $perPage): SmartLinksPageDto
    {
        $data = $this->alanbaseClient->getByPage($page, $perPage)->content;

        return $this->convertToDto($data);
    }

    /**
     * @throws GuzzleException
     */
    public function searchUsingCache(?string $search, int $page, int $perPage): SmartLinksPageDto
    {
        $search = strtolower($search);
        $data = $this->alanbaseClient->getAll();
        $smartLink = $this->makeSmartLinkItemsCollection($data);

        $filtered = $smartLink->filter(function ($item) use ($search) {
            $name = strtolower($item->getName());
            $targetLink = strtolower($item->getTargetlink());

            return str_contains($targetLink, $search) || str_contains($name, $search);
        });

        return $this->createDtoWithPagination($filtered, $page, $perPage);
    }

    public function history(SmartLinkHistoryDto $historyDto): SmartLinkHistoryResultDto
    {
        return $this->changeLogRepository->getByDto($historyDto);
    }

    public function updateDomain(int $id, string $newDomain, string $adminEmail): AlanbaseOfferDto
    {
        $offer = $this->alanbaseClient->getOfferById($id);

        $oldDomain = $this->urlService->getDomainFromUrl($offer->targetLink);

        if ($oldDomain === $newDomain) {
            throw new SameDomainException('New domain is the same as the old one');
        }

        $newTargetLink = $this->urlService->replaceDomain($offer->targetLink, $newDomain);

        $updatedOffer = $this->alanbaseClient->updateDomain($offer, $newTargetLink);

        $this->changeLogRepository->add($offer, $oldDomain, $newDomain, $newTargetLink, $adminEmail);

        return $updatedOffer;
    }

    private function convertToDto(array $data): SmartLinksPageDto
    {
        $smartLink = new Collection();

        foreach ($data['data'] as $item) {
            $smartLink->add(new SmartLinkItemDto(
                $item['id'],
                $item['name'],
                $item['target_link'],
                $this->urlService->getDomainFromUrl($item['target_link']),
                $item['tracking_domain']['id'],
                $item['privacy_level'],
            ));
        }

        return new SmartLinksPageDto(
            $data['meta']['page'],
            $data['meta']['per_page'],
            $data['meta']['total_count'],
            $data['meta']['last_page'],
            $smartLink
        );
    }

    /**
     * @param Collection<SmartLinkItemDto> $items
     */
    private function createDtoWithPagination(Collection $items, int $page, int $perPage): SmartLinksPageDto
    {
        $total = $items->count();
        $lastPage = (int)ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;
        $paginatedItems = $items->slice($offset, $perPage)->values();

        return new SmartLinksPageDto(
            $page,
            $perPage,
            $total,
            $lastPage,
            $paginatedItems
        );
    }

    /**
     * @return Collection<SmartLinkItemDto>
     */
    private function makeSmartLinkItemsCollection(array $data): Collection
    {
        $collection = new Collection();

        foreach ($data as $item) {
            $collection->add(new SmartLinkItemDto(
                $item['id'],
                $item['name'],
                $item['target_link'],
                $this->urlService->getDomainFromUrl($item['target_link']),
                $item['tracking_domain']['id'],
                $item['privacy_level'],
            ));
        }

        return $collection;
    }
}
