<?php declare(strict_types=1);

namespace App\Http\Services;

use App\Http\Controllers\Dto\AuthResponseDto;
use App\Services\AuthHttpService;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Passport\RefreshTokenRepository;
use League\OAuth2\Client\Token\AccessToken;

class AuthService
{
    private AuthHttpService $authHttpService;

    public function __construct(AuthHttpService $authHttpService)
    {
        $this->authHttpService = $authHttpService;
    }

    /**
     * @param string $username
     * @param string $password
     * @return AuthResponseDto
     * @throws \Exception
     */
    public function login(string $username, string $password): AuthResponseDto
    {
        $uri = "/api/v1/oauth/token";

        $form = [
            'grant_type' => 'password',
            'client_id' => config('common.client_id'),
            'client_secret' => config('common.client_secret'),
            'username' => $username,
            'password' => $password,
            'scope' => '',
        ];

        $response = $this->authHttpService->setForm($form)->doPostRequest($uri);

        if (!$response->isSuccess) {
            throw new \Exception($response->content['message'] ?? $response->content['errors'][0]);
        }

        $token = new AccessToken($response->content);

        $authResponseDto = new AuthResponseDto();
        $authResponseDto->accessToken = $token->getToken();
        $authResponseDto->refreshToken = $token->getRefreshToken();
        $authResponseDto->tokenType = $token->getValues()['token_type'];
        $authResponseDto->expiresIn = $token->getExpires();

        return $authResponseDto;
    }

    public function logout(): void
    {
        if (Auth::check()) {
            $userToken = Auth::user()->token();
            $refreshTokenRepository = app(RefreshTokenRepository::class);

            if ($userToken) {
                $userToken->revoke();
                $refreshTokenRepository->revokeRefreshTokensByAccessTokenId($userToken->id);
            }
        }
    }

    /**
     * @param string $refreshToken
     * @return AuthResponseDto
     * @throws \Exception
     */
    public function refresh(string $refreshToken, ?string $cluster): AuthResponseDto
    {
        $uri = "/api/v1/oauth/token";

        $form = [
            'grant_type' => 'refresh_token',
            'client_id' => config('common.client_id'),
            'client_secret' => config('common.client_secret'),
            'refresh_token' => $refreshToken,
            'cluster_connection' => $cluster,
        ];

        $response = $this->authHttpService->setForm($form)->doPostRequest($uri);

        if (!$response->isSuccess) {
            throw new \Exception($response->content['message'] ?? $response->content['errors'][0]);
        }

        $token = new AccessToken($response->content);

        $authResponseDto = new AuthResponseDto();
        $authResponseDto->accessToken = $token->getToken();
        $authResponseDto->refreshToken = $token->getRefreshToken();
        $authResponseDto->tokenType = $token->getValues()['token_type'];
        $authResponseDto->expiresIn = $token->getExpires();

        return $authResponseDto;
    }
}
