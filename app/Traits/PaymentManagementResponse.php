<?php

namespace App\Traits;

use App\Exceptions\PaymentResponseException;
use IntegratorCore\Services\Dto\ResponseOutputDto;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

trait PaymentManagementResponse
{

    /**
     * @param ResponseOutputDto $responseOutputDto
     * @return ResponseOutputDto
     * @throws PaymentResponseException
     */
    private function handlePaymentManagementResponse(ResponseOutputDto $responseOutputDto): ResponseOutputDto
    {
        if (!$responseOutputDto->isSuccess && $responseOutputDto->responseCode !== ResponseAlias::HTTP_CREATED) {
            throw new PaymentResponseException($responseOutputDto);
        }

        return $responseOutputDto;
    }
}
