<?php

namespace App\Traits;

use App\Exceptions\PaymentResponseException;
use IntegratorCore\Services\Dto\ResponseOutputDto;
use Skipper\Exceptions\DomainException;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

/**
 * Trait PaymentResponse
 * @package App\Traits
 */
trait PaymentResponse
{
    /**
     * @param $response
     * @return array
     * @throws DomainException
     */
    private function guzzleProcess($response): array
    {
        $statusCode = $response->getStatusCode();
        $responseBody = json_decode($response->getBody(), true, 512, JSON_THROW_ON_ERROR);

        if ($statusCode !== 200) {
            if ($statusCode === 404) {
                throw new DomainException('Not found', '', [], null, $statusCode);
            }

            if (array_key_exists('errors', $responseBody) && is_array($responseBody['errors'])) {
                $msg = $responseBody['errors'][0];
            } else {
                $msg = reset($responseBody);
            }

            $msgStr = is_array($msg) ? json_encode($msg) : $msg;

            throw new DomainException($msgStr, '', [], null, $statusCode);
        }

        return $responseBody;
    }

    /**
     * @param ResponseOutputDto $responseOutputDto
     * @return ResponseOutputDto
     * @throws PaymentResponseException
     */
    private function handlePaymentResponse(ResponseOutputDto $responseOutputDto): ResponseOutputDto
    {
        if (isset($responseOutputDto->content['status']) && $responseOutputDto->content['status'] === 'fail') {
            throw new PaymentResponseException($responseOutputDto);
        }

        return $responseOutputDto;
    }
}
