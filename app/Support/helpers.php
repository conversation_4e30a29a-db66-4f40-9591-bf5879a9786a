<?php

if (! function_exists('get_cluster_connection_from_request')) {
    function get_cluster_connection_from_request(): string
    {
        $cluster = (string)request()->attributes->get('cluster_connection', '');
        if (!$cluster) {
            $cluster = (string)request()->get('cluster_connection', '');
        }

        return $cluster;
    }
}

function app_encode($value) {
    if (!empty($value)) {
        return strtr(base64_encode($value), config('common.encrypt.default'), config('common.encrypt.custom'));
    }

    return $value;
}

function app_decode($value) {
    if ($value && is_string($value)) {
        return base64_decode(strtr($value, config('common.encrypt.custom'), config('common.encrypt.default')));
    }

    return $value;
}

function throttler(string $configKey): string
{
    $attempts = config("common.throttler.$configKey.attempts");
    $timeout = config("common.throttler.$configKey.timeout");

    return "throttler:$attempts,$timeout";
}

function isProduction(): bool
{
    return app()->environment(['production', 'prod']);
}
