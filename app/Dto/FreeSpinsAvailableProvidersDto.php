<?php

namespace App\Dto;

class FreeSpinsAvailableProvidersDto
{
    private ?int $supplierId;
    private ?string $aggregator;

    public function __construct(?int $supplierId = null, ?string $aggregator = null)
    {
        $this->supplierId = $supplierId;
        $this->aggregator = $aggregator;
    }

    public function getSupplierId(): ?int
    {
        return $this->supplierId;
    }

    public function getAggregator(): ?string
    {
        return $this->aggregator;
    }
}
