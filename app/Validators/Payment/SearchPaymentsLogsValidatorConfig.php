<?php

declare(strict_types=1);

namespace App\Validators\Payment;

use App\Core\Validator\ValidatorConfigInterface;

class SearchPaymentsLogsValidatorConfig implements ValidatorConfigInterface
{
    public function __invoke(): array
    {
        return [
            'player_id' => [
                'required',
                'integer',
            ],
            'pagination' => [
                'required',
                'array',
            ],
            'pagination.limit' => [
                'required',
                'integer',
                'min:1',
                'max:50',
            ],
            'pagination.offset' => [
                'filled',
                'integer',
                'min:0',
                'max:50000',
            ],
            'start_date' => [
                'required_with:end_date',
                'filled',
                'date',
                'before_or_equal:end_date',
                'before:tomorrow',
            ],
            'end_date' => [
                'required_with:start_date',
                'filled',
                'date',
                'after_or_equal:start_date',
            ],
            'transaction_type' => [
                'nullable',
                'string',
                'max:20',
            ],
            'bonus_type' => [
                'nullable',
                'string',
                'max:40',
            ],
            'player_uuid' => [
                'nullable',
                'string',
                'max:40',
            ],
        ];
    }
}
