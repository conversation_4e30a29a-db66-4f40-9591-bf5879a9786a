<?php

declare(strict_types=1);

namespace App\Validators\Payment;

use App\Core\Validator\ValidatorConfigInterface;
use App\Enums\Payments\PaymentBalanceTypeEnum;
use Illuminate\Validation\Rule;

class SearchPaymentsStatisticForBetsValidatorConfig implements ValidatorConfigInterface
{
    public function __invoke(): array
    {
        return [
            'player_id' => [
                'required',
                'integer',
            ],
            'balance_type' => [
                'required',
                'string',
                Rule::in(PaymentBalanceTypeEnum::cases())
            ],
            'start_date' => [
                'required_with:end_date',
                'filled',
                'date',
                'before_or_equal:end_date',
                'before:tomorrow',
            ],
            'end_date' => [
                'filled',
                'date',
                'after_or_equal:start_date',
                'required_with:start_date',
                'before:tomorrow'
            ],
            'pagination' => [
                'array',
                'required'
            ],
            'pagination.limit' => [
                'integer',
                'min:1',
                'max:50',
                'required'
            ],
            'pagination.offset' => [
                'integer',
                'min:0',
                'max:50000',
                'filled',
            ],
        ];
    }
}
