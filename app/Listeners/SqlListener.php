<?php

namespace App\Listeners;

use Illuminate\Database\Events\QueryExecuted;
use App\Services\Utilities\DatabaseSyncManager;

class SqlListener
{
    private DatabaseSyncManager $databaseSyncManager;

    public function __construct(
        DatabaseSyncManager $databaseSyncManager
    ) {
        $this->databaseSyncManager = $databaseSyncManager;
    }

    public function __invoke(QueryExecuted $executed)
    {
        $this->databaseSyncManager->syncQuery($executed);
    }
}
