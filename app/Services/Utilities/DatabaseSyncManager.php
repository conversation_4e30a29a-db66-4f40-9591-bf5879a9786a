<?php

namespace App\Services\Utilities;

use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DatabaseSyncManager
{
    private bool $enabled;
    private string $defaultConnection;
    private array $syncConnections;
    private array $syncTables;

    public function __construct(
        bool $enabled,
        string $defaultConnection,
        array $syncConnections,
        array $syncTables
    ) {
        $this->enabled = $enabled;
        $this->defaultConnection = $defaultConnection;
        $this->syncConnections = $syncConnections;
        $this->syncTables = $syncTables;
    }

    public function syncQuery(QueryExecuted $query): bool
    {
        if (!$this->enabled) {
            return false;
        }

        if (!$this->canSyncQuery($query->connectionName, $query->sql)) {
            return false;
        }

        $targetConnections = $this->getTargetConnections($query->connectionName);
        foreach ($targetConnections as $syncConnection) {
            try {
                DB::connection($syncConnection)->statement($query->sql, $query->bindings);
            } catch (\Throwable $throwable) {
                Log::error('Sync request failed, retry again.', [
                    'error' => $throwable,
                    'query' => $query,
                ]);
                sleep(1);
                DB::connection($syncConnection)->statement($query->sql, $query->bindings);
            }
        }

        return true;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function getDefaultConnection(): string
    {
        return $this->defaultConnection;
    }

    private function canSyncQuery(string $connectionName, string $query): bool
    {
        if ($connectionName !== $this->defaultConnection) {
            return false;
        }

        $query = str_replace(['`', '(', ')', ','], '', $query);
        $parts = explode(' ', $query);
        if (!is_array($parts) || count($parts) < 3) {
            return false;
        }

        $parts = array_map('strtolower', array_combine($parts, $parts));
        $firstWord = reset($parts);
        if (!in_array($firstWord, ['insert', 'update', 'delete'])) {
            return false;
        }

        $tableExists = false;
        foreach ($this->syncTables as $syncTable) {
            if (isset($parts[$syncTable])) {
                $tableExists = true;
                break;
            }
        }

        return $tableExists;
    }

    private function getTargetConnections(string $sourceConnection): array
    {
        if ($sourceConnection === $this->defaultConnection) {
            $duplicatedConnection = $sourceConnection;
            if ($duplicatedConnection === 'mysql-app') {
                $duplicatedConnection = '6Vr';
            }
            $result = $this->syncConnections;
            $key = array_search($duplicatedConnection, $result);
            if ($key !== false) {
                unset($result[$key]);
                return $result;
            }

            return $this->syncConnections;
        }

        $key = array_search($sourceConnection, $this->syncConnections, true);
        if ($key === false) {
            throw new \RuntimeException('Sync connection not found');
        }

        $result = $this->syncConnections;
        unset($result[$key]);

        return $result;
    }
}
