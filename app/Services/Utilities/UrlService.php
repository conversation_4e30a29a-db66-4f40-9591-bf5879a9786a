<?php

declare(strict_types=1);

namespace App\Services\Utilities;

class UrlService
{
    public function replaceDomain(string $url, string $domain): string
    {
        $parsedUrl = parse_url($url);
        $parsedUrl['host'] = $domain;

        $query = '';
        if (isset($parsedUrl['query'])) {
            $query = '?' . $parsedUrl['query'];
        }

        return $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . ($parsedUrl['path'] ?? '') . $query;
    }

    public function getDomainFromUrl(string $targetLink): string
    {
        return parse_url($targetLink, PHP_URL_HOST);
    }
}
