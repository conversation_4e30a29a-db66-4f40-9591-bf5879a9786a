<?php


namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;

/**
 * Class HttpClientService
 * @package App\Services
 */
class HttpClientService
{


    /**
     * @var Client
     */
    protected Client $httpClient;

    /**
     * HttpClientService constructor.
     * @param Client $httpClient
     */
    public function __construct(Client $httpClient)
    {
        $this->httpClient = $httpClient;
    }

    /**
     * @param array $json
     * @param string $accessToken
     * @param string $uri
     * @param string $base_uri
     * @return ResponseInterface
     * @throws GuzzleException
     */
    public function post(array $json, string $accessToken, string $uri, string $base_uri): ResponseInterface
    {
        $params = [
            'base_uri' => $base_uri,
            'timeout' => 10,
            'verify' => false,
            'http_errors' => false,
            'json' => $json,
            'headers' => [
                'Accept' => 'application/json',
                'accessToken' => $accessToken
            ]
        ];
        return $this->httpClient->post($uri, $params);
    }

    public function get(array $json, string $accessToken, string $uri, string $base_uri): ResponseInterface
    {
        $params = [
            'base_uri' => $base_uri,
            'timeout' => 10,
            'verify' => false,
            'http_errors' => false,
            'json' => $json,
            'headers' => [
                'Accept' => 'application/json',
                'accessToken' => $accessToken
            ]
        ];
        return $this->httpClient->get($uri, $params);
    }
}
