<?php
namespace App\Services;

use App\Auth\CustomGenericProvider;
use AppV2\Core\Services\ClusterConnectionService;
use Carbon\Carbon;
use Deversin\Signature\SignatureInterface;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Cache\Repository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Skipper\ApiClient\BasicApiClient;
use Skipper\Exceptions\DomainException;

class PlayerAppClient extends BasicApiClient
{
    private int $clientId;
    private string $clientSecret;
    private Repository $cache;
    private Request $request;
    private SignatureInterface $signature;
    private ClusterConnectionService $clusterConnectionService;

    public function __construct(
        string $baseUrl,
        int $clientId,
        string $clientSecret,
        Repository $cache,
        Request $request,
        SignatureInterface $signature,
        ClusterConnectionService $clusterConnectionService,
        array $options = []
    ) {
        parent::__construct($baseUrl, null, $options);
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->cache = $cache;
        $this->request = $request;
        $this->signature = $signature;
        $this->clusterConnectionService = $clusterConnectionService;
    }

    /**
     * @param array $emails
     * @param string $comment
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function massCommentAssignment(array $emails, string $comment): array
    {
        return $this->request('post', '/api/v1/players/comments', $this->authorizeOptions([
            'json' => [
                'emails' => $emails,
                'admin_id' => Auth::user()->id,
                'admin_name' => Auth::user()->login,
                'comment' => $comment,
            ],
        ]));
    }

    /**
     * @param array $query
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function searchPayments(array $query): array
    {
        return $this->request('get', '/api/v1/payments', $this->authorizeOptions([
            'query' => $query,
        ]));
    }

    /**
     * @param string   $payment_gateway
     * @param string   $transaction_id
     * @param int|null $clientId
     *
     * @return array
     * @throws GuzzleException
     * @throws \JsonException
     */
    public function sendClosedPayoutStatus(int $internalPayoutId, ?int $clientId = null): array
    {
        return $this->request('POST', "/api/v3/payouts/$internalPayoutId/close/", $this->authorizeOptions([
            'json' => [],
        ], $clientId));
    }

    /**
     * @throws GuzzleException
     */
    public function sendPayoutToApprove(int $newPayoutId, string $player_id, ?int $clientId = null): array
    {
        $json = [
            'comment'   => "",
            'player_id' => $player_id,
            '_ultra'    => env('CORE_API_TOKEN'),
        ];

        return $this->request('POST', "/api/v1/payouts/$newPayoutId", $this->authorizeOptions([
            'json' => $json,
        ], $clientId));
    }

    /**
     * @param array $options
     * @return array
     */
    public function playerSearch(array $options): array
    {
        return $this->request('get', '/api/v1/players', $this->authorizeOptions([
            'query' => $options,
        ]));
    }

    /**
     * @param int $playerId
     * @param array $options
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function updateProfile(int $playerId, array $options): array
    {
        return $this->request('put', '/api/v1/players/' . $playerId, $this->authorizeOptions([
            'json' => $options,
        ]));
    }

    /**
     * @param int $playerId
     * @param array $options
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function updatePlayerBalance(int $playerId, array $options): array
    {
        return $this->request('post', '/api/v1/players/' . $playerId . '/balance', $this->authorizeOptions([
            'json' => $options,
        ]));
    }

    /**
     * @param int $playerId
     * @param array $options
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function updatePlayerMeta(int $playerId, array $options): array
    {
        return $this->request('post', sprintf('/api/v1/players/%d/meta', $playerId), $this->authorizeOptions([
            'json' => $options,
        ]));
    }

    /**
     * @param array $options
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function updateSettings(array $options): array
    {
        return $this->request('post', '/api/v1/settings', $this->authorizeOptions([
            'json' => $options,
        ]));
    }

    /**
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getSettings(): array
    {
        return $this->request('get', '/api/v1/settings', $this->authorizeOptions([]));
    }

    /**
     * @throws DomainException
     * @throws GuzzleException
     * @throws \JsonException
     */
    public function updateSlotCategoryCache(array $options): void
    {
        $this->request('post', '/api/v3/slots/categories/reset-cache', $this->authorizeOptions([
            'json' => $options,
        ]));
    }

    /**
     * @param array $options
     * @param int|null $clientId
     * @return array
     */
    private function authorizeOptions(array $options, ?int $clientId = null): array
    {
        $cluster = $this->clusterConnectionService->getUserClusterConnection();

        $key = 'app_access_bearer_token-' . $cluster;
        $token = $this->cache->get($key);

        if (empty($token)) {
            $provider = new CustomGenericProvider([
                'clientId' => $this->clientId,
                'clientSecret' => $this->clientSecret,
                'redirectUri' => 'http://localhost',
                'urlAuthorize' => 'http://admin.com/authorize',
                'urlAccessToken' => $this->baseUrl . '/api/v1/oauth/token',
                'urlResourceOwnerDetails' => 'http://admin.com/resource',
            ]);
            if ($cluster) {
                $provider->setDefaultHeaders([
                    ClusterConnectionService::CLUSTER_CONNECTION_HEADER => $cluster,
                ]);
            }

            try {
                $token = $provider->getAccessToken('client_credentials', [
                    'scope' => ['admin'],
                ]);
            } catch (IdentityProviderException $e) {
                throw new DomainException('Error authenticating on white label app', 'white-label.app', [
                    'original_message' => $e->getMessage(),
                    'response' => $e->getResponseBody(),
                ], $e);
            }

            $ttl = Carbon::createFromTimestamp($token->getExpires());

            $token = $token->getToken();

            $this->cache->put($key, $token, $ttl);
        }

        $options['headers']['Authorization'] = 'Bearer ' . $token;

        $options['headers'][SignatureInterface::SIGNATURE_HEADER] =
            $this->signature->sign(json_encode($options['json'] ?? [], JSON_THROW_ON_ERROR));

        if ($whiteListedIp = request()->headers->get('ip')) {
            $options['headers']['ip'] = $whiteListedIp;
        }

        return $options;
    }

    /**
     * @param string $method
     * @param string $uri
     * @param array $query
     * @param array $body
     * @param int|null $clientId
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function doRequest(string $method, string $uri, array $query, array $body, ?int $clientId = null): array
    {
        return $this->request($method, $uri, $this->authorizeOptions([
            'query' => $query,
            'json' => $body,
        ], $clientId));
    }
}
