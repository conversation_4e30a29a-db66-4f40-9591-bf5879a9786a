<?php declare(strict_types=1);

namespace App\Services;

use App\Traits\BonusManagementResponse;
use IntegratorCore\Services\Dto\ResponseOutputDto;

class BonusManagementServiceHttpClient extends HttpService
{
    use BonusManagementResponse;

    /**
     * @throws BonusManagementResponse
     */
    protected function doRequest(string $requestType, string $url): ResponseOutputDto
    {
        $response = parent::doRequest($requestType, $url);

        return $this->handleBonusManagementResponse($response);
    }
}
