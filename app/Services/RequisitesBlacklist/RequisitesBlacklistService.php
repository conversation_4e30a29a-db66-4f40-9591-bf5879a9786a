<?php declare(strict_types=1);


namespace App\Services\RequisitesBlacklist;


use App\Models\Payout;
use App\Repositories\RequisitesBlackList\RequisitesBlacklistRepository;
use Illuminate\Pagination\LengthAwarePaginator;

class RequisitesBlacklistService
{
    /**
     * @var RequisitesBlacklistRepository
     */
    private RequisitesBlacklistRepository $requisitesBlacklistRepository;

    /**
     * RequisitesBlacklistRepository constructor.
     * @param RequisitesBlacklistRepository $requisitesBlacklistRepository
     */
    public function __construct(RequisitesBlacklistRepository $requisitesBlacklistRepository)
    {
        $this->requisitesBlacklistRepository = $requisitesBlacklistRepository;
    }

    public function get(?string $requisite, ?array $pagination): LengthAwarePaginator
    {
        return $this->requisitesBlacklistRepository->get($requisite, $pagination);
    }

    public function create(int $adminId, string $requisite, ?string $comment): void
    {
        $requisitesBlacklistId = $this->requisitesBlacklistRepository->create($adminId, $requisite, $comment);

        $this->checkNewRequisiteInCreatedPayoutsAndSave($requisitesBlacklistId, $requisite);
    }

    public function destroy(int $deleteAdminId, $id): void
    {
        $this->requisitesBlacklistRepository->destroy($deleteAdminId, $id);
    }

    public function checkNewRequisiteInCreatedPayoutsAndSave(int $requisitesBlacklistId, string $requisite): void
    {
        $payoutsWithDetail = Payout::query()->with('detail')->where('status', 'created')->get();

        foreach ($payoutsWithDetail as $payoutWithDetail) {
            if ($this->checkRequisiteExists($payoutWithDetail, $requisite)) {
                $this->requisitesBlacklistRepository->saveIntoRequisiteBlacklist(
                    $payoutWithDetail->player_id,
                    $payoutWithDetail->id,
                    $requisitesBlacklistId
                );
            }
        }
    }

    public function checkRequisiteExists(Payout $payoutWithDetail, string $requisite)
    {
        if (!is_null($payoutWithDetail->detail)) {
            return array_search($requisite, $payoutWithDetail->detail->details, true);
        }
        return false;
    }
}
