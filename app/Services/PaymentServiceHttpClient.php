<?php declare(strict_types=1);

namespace App\Services;

use App\Exceptions\PaymentResponseException;
use App\Traits\PaymentResponse;
use IntegratorCore\Services\Dto\ResponseOutputDto;

class PaymentServiceHttpClient extends HttpService
{
    use PaymentResponse;

    /**
     * @throws PaymentResponseException
     */
    protected function doRequest(string $requestType, string $url): ResponseOutputDto
    {
        $response = parent::doRequest($requestType, $url);

        return $this->handlePaymentResponse($response);
    }
}
