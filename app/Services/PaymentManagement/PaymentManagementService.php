<?php declare(strict_types=1);

namespace App\Services\PaymentManagement;

use App\Services\PaymentManagementServiceHttpClient;

class PaymentManagementService
{
    private PaymentManagementServiceHttpClient $paymentManagementServiceHttpClient;

    public function __construct(PaymentManagementServiceHttpClient $paymentManagementServiceHttpClient)
    {
        $this->paymentManagementServiceHttpClient = $paymentManagementServiceHttpClient;
    }

    public function getAvailablePaymentMethods(array $request): array
    {
        $uri = 'admin/payment/methods/available';

        $result = $this->paymentManagementServiceHttpClient->setJson($request)->doGetRequest($uri);

        return $result->content;
    }

    public function getAvailablePaymentAggregators(array $request): array
    {
        $uri = 'admin/payment/aggregators/available';

        $result = $this->paymentManagementServiceHttpClient->setJson($request)->doGetRequest($uri);

        return $result->content;
    }

    public function listPaymentMethod(array $request): array
    {
        $uri = 'admin/payment/methods/list';

        $result = $this->paymentManagementServiceHttpClient->setJson($request)->doGetRequest($uri);

        return $result->content;
    }

    public function getPaymentMethod($id): array
    {
        $uri = "admin/payment/methods/$id";

        $result = $this->paymentManagementServiceHttpClient->setJson([])->doGetRequest($uri);

        return $result->content;
    }

    public function deletePaymentMethod($id, array $queryParams = []): array
    {
        $uri = "admin/payment/methods/$id";

        $result = $this->paymentManagementServiceHttpClient->setQuery($queryParams)->doDeleteRequest($uri);

        return $result->content;
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function createPaymentSystem(array $postData, array $files = []): array
    {
        $uri = 'admin/payment/methods';

        $result = $this->paymentManagementServiceHttpClient->setMultipartFormData($postData, $files)->doPostRequest($uri);

        return $result->content;
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function createDuplicatePaymentSystem($id): array
    {
        $uri = "admin/payment/methods/duplicate/$id";

        return $this->paymentManagementServiceHttpClient->doPostRequest($uri)->content;
    }

    public function getPaymentPresets(array $request, $id): array
    {
        $uri = "admin/payment/methods/$id/presets";

        $result = $this->paymentManagementServiceHttpClient->setJson($request)->doGetRequest($uri);

        return $result->content;
    }

    public function savePaymentPresets(array $request, $id): array
    {
        $uri = "admin/payment/methods/$id/presets";

        $result = $this->paymentManagementServiceHttpClient->setJson($request)->doPostRequest($uri);

        return $result->content;
    }

    /**
     * @throws \JsonException
     */
    public function updatePaymentSystem(array $postData, $id, array $files = []): array
    {
        $uri = "admin/payment/methods/$id";

        $result = $this->paymentManagementServiceHttpClient->setMultipartFormData($postData, $files)->doPostRequest($uri);

        return $result->content;
    }
}
