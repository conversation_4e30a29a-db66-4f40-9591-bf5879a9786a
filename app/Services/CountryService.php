<?php
namespace App\Services;

use App\Models\Country;
use App\Repositories\CountryRepository;
use App\Repositories\SlotProviderRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\Collection;

final class CountryService
{
    private CountryRepository $countryRepository;
    private SlotProviderRepository $slotProviderRepository;

    public function __construct(CountryRepository $countryRepository, SlotProviderRepository $slotProviderRepository)
    {
        $this->countryRepository = $countryRepository;
        $this->slotProviderRepository = $slotProviderRepository;
    }

    /**
     * @throws ModelNotFoundException
     * @param int $countryId
     * @return Country|Model
     */
    public function find(int $countryId): Country
    {
        return $this->countryRepository->findCountryById($countryId);
    }

    /**
     * @throws ModelNotFoundException
     * @param int $countryId
     * @return bool
     */
    public function bindProvidersToCountry(int $countryId): bool
    {
        $country = $this->countryRepository->findCountryById($countryId);
        $slotProviders = $this->slotProviderRepository->getSlotProvidersOutsideCountry($countryId);

        $insertData = [];
        foreach ($slotProviders as $slotProvider) {
            $insertData[] = [
                'country_id' => $country->id,
                'slot_provider_id' => $slotProvider->id,
                'is_enabled' => !$slotProvider->suspended
            ];
        }

        return $this->countryRepository->insertCountrySlotProviders($insertData);
    }

    public function getCountries(): Collection
    {
        return $this->countryRepository->getCountries();
    }
}
