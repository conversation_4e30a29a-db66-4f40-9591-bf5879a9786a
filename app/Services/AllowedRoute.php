<?php
namespace App\Services;

final class AllowedRoute
{
    public string $method;
    public string $pattern;
    public ?string $permission;
    public array $middleware;

    public function __construct(
        string $method,
        string $pattern,
        ?string $permission,
        array $middleware = []
    ) {
        $this->method = $method;
        $this->pattern = $pattern;
        $this->permission = $permission;
        $this->middleware = $middleware;
    }

    public function getName(): string
    {
        return md5($this->method . $this->pattern);
    }
}
