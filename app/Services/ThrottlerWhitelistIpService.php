<?php

namespace App\Services;

use App\Enums\TestThrottlerEnum;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Date;

final class ThrottlerWhitelistIpService
{
    private PlayerAppClient $client;

    public function __construct(PlayerAppClient $client)
    {
        $this->client = $client;
    }

    /**
     * @throws GuzzleException
     */
    public function updateWhitelistIpAddress(string $ipAddress): void
    {
        $whitelistExpirationTime = Date::now()->addMinutes(config('common.throttler.whitelist_expiration_time'));

        /* @note Update the whitelist IP address for the core */
        $this->client->doRequest('POST', '/api/v3/tests/initialize', [], ['ip' => $ipAddress]);

        Cache::put(TestThrottlerEnum::WHITELIST_IP_CACHE_KEY_NAME, $ipAddress, $whitelistExpirationTime);
    }

    public function isWhitelistedIpAddress(string $ipAddress): bool
    {
        $whitelistedIpAddress = Cache::get(TestThrottlerEnum::WHITELIST_IP_CACHE_KEY_NAME);

        return $whitelistedIpAddress === $ipAddress;
    }

    public function getWhitelistedIpAddress(): ?string
    {
        return Cache::get(TestThrottlerEnum::WHITELIST_IP_CACHE_KEY_NAME);
    }
}
