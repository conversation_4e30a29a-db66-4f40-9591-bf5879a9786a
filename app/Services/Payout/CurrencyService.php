<?php

namespace App\Services\Payout;

use App\Services\PayoutServiceHttpClient;

/**
 * Class CurrencyService
 * @package App\Services\Payout
 */
class CurrencyService
{
    private PayoutServiceHttpClient $payoutHttpClient;

    public function __construct(PayoutServiceHttpClient $payoutHttpClient)
    {
        $this->payoutHttpClient = $payoutHttpClient;
    }

    /**
     * @param array $request
     * @return array
     * @throws \Exception
     */
    public function getCurrencies(array $request): array
    {
        $result =  $this->payoutHttpClient->setJson($request)->doGetRequest('admin/currencies');

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }

}
