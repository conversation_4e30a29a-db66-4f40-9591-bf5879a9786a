<?php

namespace App\Services\Payout;

use App\Enums\InternalPayoutStatusEnum;
use App\Jobs\CancelledPayoutChangeStatusJob;
use App\Jobs\RetryPayoutChangeStatusJob;
use App\Models\Payout;
use App\Models\PayoutStatus;
use App\Services\PayoutServiceHttpClient;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use IntegratorCore\Services\Dto\ResponseOutputDto;

/**
 * Class CurrencyService
 * @package App\Services\Payout
 */
class WithdrawOrderService
{
    private PayoutServiceHttpClient $payoutHttpClient;

    public function __construct(PayoutServiceHttpClient $payoutHttpClient)
    {
        $this->payoutHttpClient = $payoutHttpClient;
    }

    /**
     * @param array $orderIds
     * @return array|JsonResponse|null
     * @throws \Exception
     */
    public function getMethodsToChange(array $orderIds)
    {
        $uri = 'admin/withdraw/orders/mass_methods_to_change';
        $payload = [
            'order_ids' => $orderIds,
        ];

        $result =  $this->payoutHttpClient->setJson($payload)->doPostRequest($uri);

        if (!$result->isSuccess) {
            $message = $this->getErrorMessage($result);

            if ($result->responseCode >= 400 && $result->responseCode < 500) {
                return response()->json([
                    'message' => $message
                ], $result->responseCode);
            }

            if ($result->responseCode >= 500) {
                throw new \Exception($message);
            }
        }

        return $result->content;
    }

    /**
     * @param array $request
     *
     * @return array
     */
    public function orderRetry(array $request): array
    {
        $retryPayouts = $request['retry_payouts'];

        foreach ($retryPayouts as $retryPayout) {
            $internalPayoutId = $retryPayout['payout_id'];
            $withdrawOrderToken = $retryPayout['withdraw_order_token'];

            if (!$this->existsPayout($retryPayout['payout_id'])) {
                $payoutStatus = new PayoutStatus();
                $payoutStatus->payouts_id = $retryPayout['payout_id'];
                $payoutStatus->transaction_token = $withdrawOrderToken;
                $payoutStatus->save();
            }

            unset($retryPayout['payout_id']);

            dispatch(new RetryPayoutChangeStatusJob($internalPayoutId, $withdrawOrderToken, $retryPayout));
        }

        return ['ok'];
    }


    /**
     * @param array $request
     * @param int   $clientId
     *
     * @return array
     */
    public function orderClose(array $request, int $clientId): array
    {

        $closePayouts = $request['close_payouts'];
        $comment = $request['comment'];

        foreach ($closePayouts as $closePayout) {

            $internalPayoutId = $closePayout['payout_id'];
            $withdrawOrderToken = $closePayout['withdraw_order_token'];
            $paymentGateway = $closePayout['payment_gateway'];
            $transactionId = $closePayout['transaction_id'];

            if (!$this->existsPayout($closePayout['payout_id'])) {

                $payoutStatus = new PayoutStatus();
                $payoutStatus->payouts_id = $internalPayoutId;
                $payoutStatus->transaction_token = $withdrawOrderToken;
                $payoutStatus->save();
            }

            dispatch(new CancelledPayoutChangeStatusJob($internalPayoutId, $withdrawOrderToken, $paymentGateway, $transactionId, $comment, $clientId));
        }

        return ['ok'];
    }

    public function existsPayout($payoutId): bool
    {
        return PayoutStatus::where('payouts_id', $payoutId)->exists();
    }

    /**
     * @param array $orderIds
     * @param string $methodToken
     * @return array
     * @throws \Exception
     */
    public function getMethodsToChangeUpdate(array $orderIds, string $methodToken): array
    {
        $payload = [
            'order_ids' => $orderIds,
            'withdraw_method_token' => $methodToken,
        ];

        $uri = 'admin/withdraw/orders/mass_update';

        $currentPayoutStatuses = Payout::query()
            ->whereIn('id', $orderIds)
            ->get()
            ->pluck('status', 'id')
            ->toArray();
        Payout::query()
            ->whereIn('id', $orderIds)
            ->update(['status' => InternalPayoutStatusEnum::CHANGING_AGGREGATOR]);

        $result =  $this->payoutHttpClient->setJson($payload)->doPostRequest($uri);

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            foreach ($currentPayoutStatuses as $key => $currentPayoutStatus) {
                Payout::find($key)->update(['status' => $currentPayoutStatus]);
            }

            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }

    public function getInternalPayoutStatus(int $payoutId): ?PayoutStatus
    {
        return PayoutStatus::query()->where('payouts_id', $payoutId)->first();
    }

    public function syncStatuses(): void
    {
        $payoutStatuses = PayoutStatus::whereIn('status', [InternalPayoutStatusEnum::IN_PROCESS, InternalPayoutStatusEnum::RETRY_FAILED])->get();
        $corePayoutTokens = $payoutStatuses->pluck('payouts_id')->all();
        $corePayouts = Payout::whereIn('id', $corePayoutTokens)->get();

        foreach ($payoutStatuses as $payoutStatus) {
            $whitelabelPayout = $corePayouts->where('id', $payoutStatus->payouts_id)->first();

            $statusesToDelete = [InternalPayoutStatusEnum::CLOSED, InternalPayoutStatusEnum::PENDING, InternalPayoutStatusEnum::APPROVED];
            /** @noinspection NullPointerExceptionInspection */
            if (in_array($whitelabelPayout->status, $statusesToDelete, true) || $payoutStatus->created_at->isBefore(Carbon::now()->subHours(1))) {
                $payoutStatus->delete();
            }
        }
    }

    /**
     * @return PayoutStatus[]|Collection|\LaravelIdea\Helper\App\Models\_IH_PayoutStatus_C
     */
    public function getInternalPayoutStatuses(?array $payoutsIds = null): Collection
    {
        $this->syncStatuses();

        if ($payoutsIds === null) {
            return PayoutStatus::all();
        }

        return PayoutStatus::whereIn('payouts_id', $payoutsIds)->get();
    }

    public function getInternalPayout(string $withdrawOrderToken): ?Payout
    {
        return Payout::query()->where('transaction_token', $withdrawOrderToken)->first();
    }

    public function updateInternalPayoutStatus(int $adminPayoutStatusId, ?string $internalStatus, ?int $payoutsSystemStatus, ?string $message, ?string $token = null): ?PayoutStatus
    {
        $adminPayoutStatus = PayoutStatus::find($adminPayoutStatusId);

        if ($adminPayoutStatus === null) {
            return null;
        }

        if ($payoutsSystemStatus !== null) {
            $adminPayoutStatus->payouts_system_status = $payoutsSystemStatus;
        }
        if ($internalStatus !== null) {
            $adminPayoutStatus->status = $internalStatus;
        }
        if ($message !== null) {
            $adminPayoutStatus->message = $message;
        }
        if ($token !== null) {
            $adminPayoutStatus->transaction_token = $token;
        }
        $adminPayoutStatus->save();

        return $adminPayoutStatus;
    }

    public function deleteInternalPayoutStatus(int $adminPayoutStatusId): bool
    {
        $adminPayoutStatus = PayoutStatus::find($adminPayoutStatusId);

        if ($adminPayoutStatus === null) {
            return false;
        }

        $adminPayoutStatus->delete();

        return true;
    }

    private function getErrorMessage(ResponseOutputDto $result): string
    {
        if (isset($result->content['errors'][0])) {
            $message = $result->content['message'] ?? $result->content['errors'][0];
        } else {
            $message = $result->content['message'] ?? 'Unknown error occurred.';
        }

        return (string)$message;
    }
}
