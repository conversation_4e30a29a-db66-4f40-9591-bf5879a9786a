<?php

namespace App\Services\Payout;

use App\Services\PayoutServiceHttpClient;

/**
 * Class PayoutMethodService
 * @package App\Services\Payment
 */
class PayoutMethodService
{
    private PayoutServiceHttpClient $payoutHttpClient;

    public function __construct(PayoutServiceHttpClient $payoutHttpClient)
    {
        $this->payoutHttpClient = $payoutHttpClient;
    }

    /**
     * @param array $request
     * @return array
     * @throws \Exception
     */
    public function getMethods(array $request): array
    {
        $result = $this->payoutHttpClient->setJson($request)->doGetRequest('admin/withdraw/methods');

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }

    /**
     * @throws \Exception
     */
    public function getAdminAggregators(array $request): array
    {
        $result = $this->payoutHttpClient->setJson($request)->doGetRequest('admin/aggregators');

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }

    /**
     * @throws \Exception
     */
    public function update(array $request, string $token): array
    {
        $uri = "admin/withdraw/methods/$token";

        $result = $this->payoutHttpClient->setJson($request)->doPatchRequest($uri);

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }
}
