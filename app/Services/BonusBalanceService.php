<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\Statistics\Dto\BonusBalanceDto;
use App\Services\Statistics\StatisticsClient;
use GuzzleHttp\Exception\GuzzleException;
use Throwable;

class BonusBalanceService
{
    private StatisticsClient $statisticClient;

    public function __construct(StatisticsClient $statisticClient)
    {
        $this->statisticClient = $statisticClient;
    }

    /**
     * @throws GuzzleException
     * @throws Throwable
     */
    public function getBonusBalanceFromStatistic(BonusBalanceDto $bonusBalanceDto): array
    {
        return $this->statisticClient->getBonusesBalances($bonusBalanceDto);
    }
}
