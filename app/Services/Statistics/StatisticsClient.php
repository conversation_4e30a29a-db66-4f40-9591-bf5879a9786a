<?php

declare(strict_types=1);

namespace App\Services\Statistics;

use App\Services\Statistics\Dto\Bets\SearchPaymentsStatisticForBetsDto;
use App\Services\Statistics\Dto\BonusBalanceDto;
use App\Services\Statistics\Dto\Logs\SearchPaymentLogsDto;
use GuzzleHttp\Exception\GuzzleException;
use Skipper\ApiClient\BasicApiClient;

class StatisticsClient extends BasicApiClient
{
    private string $authToken;

    public function __construct(
        string $baseUrl,
        string $authToken,
        array $options
    ) {
        parent::__construct($baseUrl, null, $options);
        $this->authToken = $authToken;
    }

    /**
     * @throws GuzzleException
     */
    public function searchPlayerPaymentsLogs(SearchPaymentLogsDto $dto): array
    {
        $params['player_id'] = $dto->getPlayerId();
        $params['limit'] = $dto->getLimit();

        if ($dto->getOffset() !== null) {
            $params['offset'] = $dto->getOffset();
        }

        if ($dto->getStartDate() !== null) {
            $params['start_date'] = $dto->getStartDate();
        }

        if ($dto->getEndDate() !== null) {
            $params['end_date'] = $dto->getEndDate();
        }

        if ($dto->getTransactionType() !== null) {
            $params['transaction_type'] = $dto->getTransactionType();
        }

        if ($dto->getBonusType() !== null) {
            $params['bonus_type'] = $dto->getBonusType();
        }

        if ($dto->getPlayerUuid() !== null) {
            $params['player_uuid'] = $dto->getPlayerUuid();
        }

        return $this->request('GET', $this->baseUrl . '/api/v1/admin/payments/logs', [
            'json' => $params,
            'headers' => [
                'Authorization' => $this->authToken,
            ],
        ]);
    }

    /**
     * @throws GuzzleException
     */
    public function searchPaymentsStatisticForBets(SearchPaymentsStatisticForBetsDto $dto): array
    {
        $params['player_id'] = $dto->getPlayerId();
        $params['limit'] = $dto->getLimit();
        $params['balance_type'] = $dto->getBalanceType()
            ->getState();

        if ($dto->getOffset() !== null) {
            $params['offset'] = $dto->getOffset();
        }

        if ($dto->getStartDate() !== null) {
            $params['start_date'] = $dto->getStartDate();
        }

        if ($dto->getEndDate() !== null) {
            $params['end_date'] = $dto->getEndDate();
        }

        return $this->request('GET', $this->baseUrl . '/api/v1/admin/bets/logs', [
            'json' => $params,
            'headers' => [
                'Authorization' => $this->authToken,
            ],
        ]);
    }

    /**
     * @throws GuzzleException
     */
    public function getBonusesBalances(BonusBalanceDto $bonusBalanceDto): array
    {
        $params = [
            'pagination' => [
                'limit' => $bonusBalanceDto->getLimit(),
                'page' => $bonusBalanceDto->getPage(),
            ],
            'bonus_external_id' => $bonusBalanceDto->getBonusExternalId(),
        ];

        if ($bonusBalanceDto->getLimit() !== null) {
            $params['with'] = $bonusBalanceDto->getWith();
        }

        return $this->request('GET', $this->baseUrl . '/api/v1/bonuses/balances', [
            'json' => $params,
            'headers' => [
                'Authorization' => $this->authToken,
            ],
        ]);
    }
}
