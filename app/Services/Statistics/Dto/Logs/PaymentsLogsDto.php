<?php

declare(strict_types=1);

namespace App\Services\Statistics\Dto\Logs;

use ArrayIterator;
use IteratorAggregate;

class PaymentsLogsDto implements IteratorAggregate
{
    private array $paymentLogs;

    public function __construct(array $paymentsLogs)
    {
        foreach ($paymentsLogs as $paymentLog) {
            $this->paymentLogs[] = new PaymentLogDto($paymentLog);
        }
    }

    public function getIterator(): ArrayIterator
    {
        return new ArrayIterator($this->paymentLogs);
    }
}
