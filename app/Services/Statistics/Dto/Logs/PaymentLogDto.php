<?php

declare(strict_types=1);

namespace App\Services\Statistics\Dto\Logs;

use Carbon\Carbon;

class PaymentLogDto
{
    private int $id;
    private int $sourceId;
    private ?string $currency;
    private string $sourceType;
    private ?int $amount;
    private ?int $sourceFee;
    private ?int $balanceHistory;
    private Carbon $createdAt;
    private Carbon $updatedAt;
    private ?string $status;
    private ?int $bonusId;

    public function __construct(array $data)
    {
        $this->id = (int)$data['id'];
        $this->sourceId = (int)$data['sourceId'];
        $this->currency = $data['currency'] ?? null;
        $this->sourceType = $data['sourceType'] ?? null;
        $this->amount = (int)$data['amount'];
        $this->sourceFee = $data['sourceFee'] ?? null;
        $this->balanceHistory = $data['balanceHistory'] !== null ? (int)$data['balanceHistory'] : null;
        $this->createdAt = Carbon::parse($data['createdAt']);
        $this->updatedAt = Carbon::parse($data['updatedAt']);
        $this->status = $data['status'] ?? null;
        $this->bonusId = $data['bonusId'] !== null ? (int)$data['bonusId'] : null;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getSourceId(): int
    {
        return $this->sourceId;
    }

    public function getSourceType(): ?string
    {
        return $this->sourceType;
    }

    public function getSourceFee(): ?int
    {
        return $this->sourceFee;
    }

    public function getBalanceHistory(): ?int
    {
        return $this->balanceHistory;
    }

    public function getCreatedAt(): Carbon
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): Carbon
    {
        return $this->updatedAt;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function getBonusId(): ?int
    {
        return $this->bonusId;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function getAmount(): ?int
    {
        return $this->amount;
    }
}
