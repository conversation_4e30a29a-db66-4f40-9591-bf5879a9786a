<?php

declare(strict_types=1);

namespace App\Services\Statistics\Dto\Logs;

use Carbon\Carbon;

class SearchPaymentLogsDto
{
    private int $playerId;
    private ?Carbon $startDate;
    private ?Carbon $endDate;
    private int $limit;
    private ?int $offset;
    private ?string $transactionType;
    private ?string $bonusType;
    private ?string $playerUuid;

    public function __construct(array $data)
    {
        $this->playerId = $data['playerId'];
        $this->startDate = $data['startDate'];
        $this->endDate = $data['endDate'];
        $this->limit = $data['limit'];
        $this->offset = $data['offset'];
        $this->transactionType = $data['transactionType'] ?? null;
        $this->bonusType = $data['bonusType'] ?? null;
        $this->playerUuid = $data['playerUuid'];
    }

    public function getPlayerId(): int
    {
        return $this->playerId;
    }

    public function getStartDate(): ?Carbon
    {
        return $this->startDate;
    }

    public function getEndDate(): ?Carbon
    {
        return $this->endDate;
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public function getOffset(): ?int
    {
        return $this->offset;
    }

    public function getTransactionType(): ?string
    {
        return $this->transactionType;
    }

    public function getBonusType(): ?string
    {
        return $this->bonusType;
    }

    public function getPlayerUuid(): ?string
    {
        return $this->playerUuid;
    }
}
