<?php

declare(strict_types=1);

namespace App\Services\Statistics\Dto\Bets;

use ArrayIterator;
use IteratorAggregate;

class PaymentsStatisticForBetsPluralDto implements IteratorAggregate
{
    private array $paymentsStatisticForBets;

    public function __construct(array $paymentsData)
    {
        foreach ($paymentsData as $paymentsDatum) {
            $this->paymentsStatisticForBets[] = new PaymentsStatisticForBetsDto($paymentsDatum);
        }
    }

    public function getIterator(): ArrayIterator
    {
        return new ArrayIterator($this->paymentsStatisticForBets);
    }
}
