<?php

declare(strict_types=1);

namespace App\Services\Statistics\Dto\Bets;

use App\Enums\Payments\PaymentBalanceTypeEnum;

class SearchPaymentsStatisticForBetsDto
{
    private int $playerId;
    private PaymentBalanceTypeEnum $balanceType;
    private ?int $limit;
    private ?int $offset;
    private ?string $startDate;
    private ?string $endDate;

    public function __construct(array $data)
    {
        $this->playerId = $data['playerId'];
        $this->balanceType = $data['balanceType'];
        $this->limit = $data['limit'];
        $this->offset = $data['offset'];
        $this->startDate = $data['startDate'];
        $this->endDate = $data['endDate'];
    }

    public function getPlayerId(): int
    {
        return $this->playerId;
    }

    public function getBalanceType(): PaymentBalanceTypeEnum
    {
        return $this->balanceType;
    }

    public function getLimit(): ?int
    {
        return $this->limit;
    }

    public function getOffset(): ?int
    {
        return $this->offset;
    }

    public function getStartDate(): ?string
    {
        return $this->startDate;
    }

    public function getEndDate(): ?string
    {
        return $this->endDate;
    }
}
