<?php

declare(strict_types=1);

namespace App\Services\Statistics\Dto\Bets;

use Carbon\Carbon;

class PaymentsStatisticForBetsDto
{
    private int $id;
    private int $sourceId;
    private ?string $sourceExternalId;
    private ?string $sourceIdHash;
    private ?array $sourceEvents;
    private string $status;
    private ?int $sourcePossibleWin;
    private int $sourceAmount;
    private int $profit;
    private string $currency;
    private ?float $sourceFactor;
    private Carbon $createdAt;
    private Carbon $updatedAt;
    private ?int $bonusId;
    private ?string $market;
    private ?string $outcome;

    public function __construct(array $data)
    {
        $this->id = (int)$data['id'];
        $this->sourceId = (int)$data['sourceId'];
        $this->sourceExternalId = $data['sourceExternalId'];
        $this->sourceIdHash = $data['sourceIdHash'];
        $this->sourceEvents = array_key_exists('sourceEvents', $data) ? json_decode($data['sourceEvents']) : null;
        $this->sourcePossibleWin = $data['sourcePossibleWin'] !== null ? (int)$data['sourcePossibleWin'] : null;
        $this->sourceAmount = (int)$data['sourceAmount'];
        $this->status = $data['status'];
        $this->profit = (int)$data['profit'];
        $this->currency = $data['currency'];
        $this->sourceFactor = $data['sourceFactor'] !== null ? (float)$data['sourceFactor'] : null;
        $this->createdAt = Carbon::parse($data['createdAt']);
        $this->updatedAt = Carbon::parse($data['updatedAt']);
        $this->bonusId = $data['bonusId'] !== null ? (int)$data['bonusId'] : null;
        $this->market = $data['market'] ?? null;
        $this->outcome = $data['outcome'] ?? null;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getSourceId(): int
    {
        return $this->sourceId;
    }

    public function getSourceExternalId(): ?string
    {
        return $this->sourceExternalId;
    }

    public function getSourceIdHash(): ?string
    {
        return $this->sourceIdHash;
    }

    public function getSourceEvents(): ?array
    {
        return $this->sourceEvents;
    }

    public function getSourcePossibleWin(): ?int
    {
        return $this->sourcePossibleWin;
    }

    public function getSourceAmount(): int
    {
        return $this->sourceAmount;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getProfit(): int
    {
        return $this->profit;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function getSourceFactor(): ?float
    {
        return $this->sourceFactor;
    }

    public function getCreatedAt(): Carbon
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): Carbon
    {
        return $this->updatedAt;
    }

    public function getBonusId(): ?int
    {
        return $this->bonusId;
    }

    public function getMarket(): ?string
    {
        return $this->market;
    }

    public function setMarket(?string $market): void
    {
        $this->market = $market;
    }

    public function getOutcome(): ?string
    {
        return $this->outcome;
    }

    public function setOutcome(?string $outcome): void
    {
        $this->outcome = $outcome;
    }
}
