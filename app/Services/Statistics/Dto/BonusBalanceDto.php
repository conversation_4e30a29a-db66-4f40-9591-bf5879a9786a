<?php

declare(strict_types=1);

namespace App\Services\Statistics\Dto;

class BonusBalanceDto
{
    public int $limit;
    public int $page;
    public int $bonusExternalId;
    public ?string $with;
    public ?int $clientId;

    public function __construct(array $data)
    {
        $this->limit = (int)$data['pagination']['limit'];
        $this->page =  (int)$data['pagination']['page'];
        $this->bonusExternalId = (int)$data['bonus_external_id'];
        $this->with = $data['with'];
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public function getBonusExternalId(): int
    {
        return $this->bonusExternalId;
    }

    public function getWith(): ?string
    {
        return $this->with;
    }
}
