<?php

declare(strict_types=1);

namespace App\Services\Cache;

use App\Core\Exceptions\ExceptionDTO;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use JsonException;
use Throwable;

class SingleFlight
{
    protected int $lockTimeout;

    public function __construct(int $lockTimeout = 60)
    {
        $this->lockTimeout = $lockTimeout;
    }

    /**
     * @param string $cacheGroup
     * @param string $key
     * @param callable $callback
     * @param null $ttl
     * @param bool $update
     * @param bool $extendTtl
     *
     * @return array|mixed
     * @throws Throwable
     */
    public function call(
        string $cacheGroup,
        string $key,
        callable $callback,
        $ttl = null,
        bool $update = false,
        bool $extendTtl = false
    ) {
        $cacheKey = "single-flight:$cacheGroup:$key";
        $lockKey = "single-flight-lock:$cacheGroup:$key";
        $errorFlagKey = "single-flight-error:$cacheGroup:$key";

        $isErrorResponse = Cache::get($errorFlagKey, false);

        if ($isErrorResponse) {
            $exceptionDto = Cache::get($cacheKey);

            throw $this->getThrowableFromExceptionDto($exceptionDto);
        }

        if ($update === false) {
            // Trying to get data from the cache
            $result = Cache::get($cacheKey);

            if ($result !== null) {
                if ($extendTtl === true) {
                    Redis::expire($cacheGroup, $ttl ?? 300);
                    Redis::expire($cacheKey, $ttl ?? 300);
                }

                return $result;
            }
        }

        // Lock to prevent re-execution
        $lock = Cache::lock($lockKey, $this->lockTimeout);
        try {
            if ($lock->get()) {
                if ($update === true) {
                    $result = null;
                } else {
                    // Check again to see if the data is already cached
                    $result = Cache::get($cacheKey);
                }
                if ($result === null) {
                    $result = $callback();

                    $this->addResultToCache($result, $cacheKey, $cacheGroup, $ttl ?? 300);
                }

                $lock->release();

                return $result;
            }

            // If we couldn't get the lock, wait
            usleep(500000); // wait 0.5 second

            return $this->call($cacheGroup, $key, $callback, $ttl);
        } catch (Throwable $e) {
            // Set error flag and cache error for 30 seconds to prevent high load on database
            Cache::put($errorFlagKey, true, 30);

            $this->addResultToCache($this->getExceptionDtoFromThrowable($e), $cacheKey, $cacheGroup, 30);

            $lock->forceRelease();
            throw $e;
        }
    }

    public function deleteCacheGroup(string $cacheGroup): void
    {
        $keys = Redis::lRange($cacheGroup, 0, -1);

        if (empty($keys)) {
            return;
        }

        foreach ($keys as $key) {
            Cache::delete($key);
        }

        Redis::delete($cacheGroup);
    }

    /**
     * @throws JsonException
     */
    public function cacheKeyFromParams(array $params): string
    {
        return md5(json_encode($params, JSON_THROW_ON_ERROR));
    }

    protected function addResultToCache($data, string $cacheKey, string $cacheGroup, int $ttl): void
    {
        Cache::put($cacheKey, $data, $ttl);

        $keys = Redis::lRange($cacheGroup, 0, -1);
        if ( !in_array($cacheKey, $keys, true)) {
            Redis::rPush($cacheGroup, $cacheKey);
        }

        Redis::expire($cacheGroup, $ttl);
    }

    protected function getExceptionDtoFromThrowable(Throwable $e): ExceptionDTO
    {
        return new ExceptionDTO(
            get_class($e), $e->getCode(), $e->getMessage(),
        );
    }

    protected function getThrowableFromExceptionDto(ExceptionDTO $exceptionDto): Throwable
    {
        $class = $exceptionDto->getClass();

        return new $class(
            $exceptionDto->getMessage(), $exceptionDto->getCode(),
        );
    }
}

