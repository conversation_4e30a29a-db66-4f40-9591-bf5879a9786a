<?php

namespace App\Services;

use App\Repositories\SlotProviderRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

/**
 * Class SlotProviderService
 * @package App\Services
 */
class SlotProviderService
{
    private SlotProviderRepository $slotProviderRepository;
    public function __construct(SlotProviderRepository $slotProviderRepository)
    {
        $this->slotProviderRepository = $slotProviderRepository;
    }

    /**
     * @throws ModelNotFoundException
     */
    public function updateIsEnabledParam(int $slotProviderId, int $countryId, bool $isEnabled): bool
    {
        return $this->slotProviderRepository
            ->updateIsEnabledParam($slotProviderId, $countryId, $isEnabled);
    }

    /**
     * @throws ModelNotFoundException
     */
    public function detachProviderFromTheCountry(int $countryId): bool
    {
        return $this->slotProviderRepository->deleteCountrySlotProviderByCountry($countryId);
    }
}
