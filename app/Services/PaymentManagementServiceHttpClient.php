<?php declare(strict_types=1);

namespace App\Services;

use App\Core\Helpers\TypeHelper;
use App\Exceptions\PaymentResponseException;
use App\Traits\PaymentManagementResponse;
use Deversin\Signature\SignatureInterface;
use Illuminate\Http\UploadedFile;
use IntegratorCore\Services\Dto\ResponseOutputDto;
use IntegratorCore\Services\HttpRequests\MultipartHttpItem;
use IntegratorCore\Services\HttpRequests\MultipartHttpRequest;

class PaymentManagementServiceHttpClient extends HttpService
{
    use PaymentManagementResponse;

    /**
     * @throws PaymentResponseException
     */
    protected function doRequest(string $requestType, string $url): ResponseOutputDto
    {
        $response = parent::doRequest($requestType, $url);

        return $this->handlePaymentManagementResponse($response);
    }

    public function setMultipartFormData(array $postData, array $files = []): PaymentManagementServiceHttpClient
    {
        /** @var SignatureInterface $signature */
        $signature = app(SignatureInterface::class);

        $multipartRequest = $this->makeMultipartRequestInstance(array_merge($postData, $files), $requestBody);

        $signature = $signature->sign(json_encode($requestBody, JSON_THROW_ON_ERROR));

        return $this
            ->setHeaders([SignatureInterface::SIGNATURE_HEADER => $signature])
            ->setMultipart($multipartRequest);
    }

    protected function makeMultipartRequestInstance(array $requestData, &$requestBody): MultipartHttpRequest
    {
        $multipartRequest = new MultipartHttpRequest();

        foreach ($requestData as $property => $value) {
            if ($value instanceof UploadedFile) {
                $multipartRequest->addItem(new MultipartHttpItem($property, $value->getContent(), $value->getClientOriginalName()));
            } elseif (is_array($value)) {
                foreach ($value as $valIndex => $subItem) {
                    if (is_array($subItem)) {
                        foreach ($subItem as $subItemIndex => $subItemValue) {
                            $subProperty = sprintf("%s[%s][%s]", $property, $valIndex, $subItemIndex);
                            $multipartRequest->addItem(new MultipartHttpItem($subProperty, $subItemValue));
                        }
                    } else {
                        $subProperty = sprintf("%s[%s]", $property, $valIndex);
                        $multipartRequest->addItem(new MultipartHttpItem($subProperty, $subItem));
                    }
                }

                $requestBody[$property] = $value;
            } else {
                if (is_string($value) && !is_numeric($value)) {
                    $value = empty($value) || $value === 'null' ? null : trim($value);
                }

                $requestBody[$property] = $value;

                $multipartRequest->addItem(new MultipartHttpItem($property, $value));
            }
        }

        return $multipartRequest;
    }
}
