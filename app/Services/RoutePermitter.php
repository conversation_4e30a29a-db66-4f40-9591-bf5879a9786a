<?php
namespace App\Services;

use App\Enums\MiddlewareEnum;
use WhiteLabelAdmin\Entities\PermissionList;

final class RoutePermitter
{
    /**
     * @return AllowedRoute[]
     */
    public function getRoutes(): array
    {
        return [
            new AllowedRoute('post', '/api/v1/players/comments', PermissionList::COMMENT_ASSIGN),
            new AllowedRoute('get', '/api/v1/players/{id:\d+}', PermissionList::USERS_LIST),
            new AllowedRoute('put', '/api/v1/players/{id:\d+}', PermissionList::USERS_EDIT),
            new AllowedRoute('post', '/api/v1/players/{id:\d+}', PermissionList::USERS_EDIT),
            new AllowedRoute('post', '/api/v1/players/{id:\d+}/meta', PermissionList::USERS_EDIT),
            new AllowedRoute('get', '/api/v1/players', PermissionList::USERS_LIST),
            new AllowedRoute('get', '/api/v3/players/segments', PermissionList::USERS_LIST),
            new AllowedRoute('get', '/api/v1/players/search', PermissionList::USERS_LIST),
            new AllowedRoute('post', '/api/v1/players', PermissionList::USERS_EDIT),
            new AllowedRoute('post', '/api/v1/gateways/webhook/payout', PermissionList::USERS_EDIT),
            new AllowedRoute('post', '/api/v1/players/passwords', PermissionList::USERS_EDIT),
            new AllowedRoute('post', '/api/v1/players/passwords/recover', PermissionList::USERS_EDIT),
            new AllowedRoute('patch', '/api/v1/players/email', null),
            new AllowedRoute('get', '/api/v1/players/{id:\d+}/balance', PermissionList::USERS_LIST),
            new AllowedRoute('get', '/api/v1/players/{id:\d+}/balance', PermissionList::USERS_LIST),
            new AllowedRoute('post', '/api/v1/players/{id:\d+}/balance', PermissionList::USERS_EDIT),
            new AllowedRoute('get', '/api/v3/players/{id:\d+}/comments', PermissionList::USERS_COMMENTS_LIST),
            new AllowedRoute('get', '/api/v3/players/search-id', PermissionList::DETERMINING_USER_ID),
            new AllowedRoute('post', '/api/v3/languages/update-weights', PermissionList::LANGUAGES_EDIT),
            new AllowedRoute('get', '/api/v1/bonuses/info/enabled/{playerId:\d+}', PermissionList::USERS_LIST),
            new AllowedRoute('get', '/api/v3/bonus-balances/{playerId:\d+}', PermissionList::USERS_LIST),
            new AllowedRoute('get', '/api/v3/bonus-balances-information/{bonusBalanceId:\d+}', PermissionList::USERS_LIST),

            new AllowedRoute('post', '/api/v1/system/hooks/slots/{provider}', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('get', '/api/v1/slots/categories', PermissionList::GET_SLOTS),
            new AllowedRoute('post', '/api/v1/slots/categories', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('put', '/api/v1/slots/categories/{id:\d+}', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('get', '/api/v1/slots/categories/slug/{slug}', PermissionList::GET_SLOTS),
            new AllowedRoute('get', '/api/v1/slots/categories/sections', PermissionList::GET_SLOTS),
            new AllowedRoute('get', '/api/v1/slots', PermissionList::GET_SLOTS),
            new AllowedRoute('put', '/api/v1/slots/{id:\d+}', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('put', '/api/v1/slots/ids', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('get', '/api/v1/slots/subscriptions', PermissionList::GET_SLOTS),
            new AllowedRoute('get', '/api/v1/slots/providers', PermissionList::GET_SLOTS),
            new AllowedRoute('post', '/api/v1/slots/providers/{id:\d+}', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('post', '/api/v1/slots/providers', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('get',  '/api/v1/subscriptions', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('get', '/api/v1/slots/sections', PermissionList::GET_SLOTS),
            new AllowedRoute('post', '/api/v1/slots/sections', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('put', '/api/v1/slots/sections/{id}', PermissionList::UPDATE_SLOTS),

            new AllowedRoute('get', '/api/v1/slots/country-settings', PermissionList::GET_SLOTS),
            new AllowedRoute('post', '/api/v1/slots/country-settings', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('delete', '/api/v1/slots/country-settings', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('post', '/api/v3/slots/params', PermissionList::UPDATE_SLOTS),

            new AllowedRoute('get', '/api/v1/slots/categories/sections', PermissionList::GET_SLOTS),
            new AllowedRoute('get', '/api/v3/admin/slots/aggregators', PermissionList::GET_SLOTS),
            new AllowedRoute('get', '/api/v1/settings', PermissionList::SEO_LIST),
            new AllowedRoute('post', '/api/v1/settings', PermissionList::SEO_EDIT),

            new AllowedRoute('get', '/api/v1/payments', PermissionList::PAYMENTS_LIST),
            new AllowedRoute('post', '/api/v1/gateways/deposit', PermissionList::DEPOSITS_LIST),
            new AllowedRoute('post', '/api/v1/gateways/payout', PermissionList::PAYOUTS_LIST),

            new AllowedRoute('get', '/api/v1/settings/banners', PermissionList::BANNERS_LIST),
            new AllowedRoute('post', '/api/v1/settings/banners', PermissionList::BANNERS_ADD),
            new AllowedRoute('put', '/api/v1/settings/banners/{id}', PermissionList::BANNERS_EDIT),
            new AllowedRoute('delete', '/api/v1/settings/banners/{id}', PermissionList::BANNERS_DELETE),

            new AllowedRoute('get', '/api/v1/players/data-moderation', PermissionList::PROFILE_MODERATORS_LIST),
            new AllowedRoute('put', '/api/v1/players/data-moderation', PermissionList::PROFILE_MODERATORS_EDIT),
            new AllowedRoute('get', '/api/v1/players/email-changes-history', PermissionList::PROFILE_MODERATORS_LIST),
            new AllowedRoute('get', '/api/v1/players/email-changes-history', PermissionList::EMAIL_CHANGE_lIST),

            new AllowedRoute('get', '/api/v1/settings/notifications/players/{id}', PermissionList::NOTIFICATIONS_LIST),
            new AllowedRoute('post', '/api/v1/settings/notifications', PermissionList::NOTIFICATIONS_ADD),
            new AllowedRoute('put', '/api/v1/settings/notifications/{id}', PermissionList::NOTIFICATIONS_EDIT),
            new AllowedRoute('put', '/api/v1/settings/notifications/', PermissionList::NOTIFICATIONS_EDIT),
            new AllowedRoute('delete', '/api/v1/settings/notifications', PermissionList::NOTIFICATIONS_DELETE),
            new AllowedRoute('delete', '/api/v1/settings/notifications/{id}', PermissionList::NOTIFICATIONS_DELETE),

            new AllowedRoute('get', '/api/v1/settings/pages', PermissionList::SEO_LIST),
            new AllowedRoute('post', '/api/v1/settings/pages', PermissionList::SEO_ADD),
            new AllowedRoute('put', '/api/v1/settings/pages/{id}', PermissionList::SEO_EDIT),
            new AllowedRoute('delete', '/api/v1/settings/pages/{id}', PermissionList::SEO_DELETE),

            new AllowedRoute('get', '/api/v1/settings/static/pages', PermissionList::STATIC_LIST),
            new AllowedRoute('post', '/api/v1/settings/static/pages', PermissionList::STATIC_ADD),
            new AllowedRoute('put', '/api/v1/settings/static/pages/{id}', PermissionList::STATIC_EDIT),
            new AllowedRoute('delete', '/api/v1/settings/static/pages/{id}', PermissionList::STATIC_DELETE),

            new AllowedRoute('get', '/api/v1/settings/countries', PermissionList::COUNTRIES_LIST),
            new AllowedRoute('post', '/api/v1/settings/countries', PermissionList::COUNTRIES_EDIT),
            new AllowedRoute('get', '/api/v1/settings/currencies', PermissionList::CURRENCIES_LIST),
            new AllowedRoute('post', '/api/v1/settings/currencies', PermissionList::CURRENCIES_EDIT),
            new AllowedRoute('get', '/api/v1/settings/languages', PermissionList::LANGUAGES_lIST),
            new AllowedRoute('post', '/api/v1/settings/languages', PermissionList::LANGUAGES_EDIT),

            new AllowedRoute('get', '/api/v1/bonuses/info', PermissionList::INFO_BONUSES_PAGE),
            new AllowedRoute('put', '/api/v1/bonuses/info/{id}', PermissionList::INFO_BONUSES_EDIT),
            new AllowedRoute('post', '/api/v1/bonuses/info', PermissionList::INFO_BONUSES_ADD),

            new AllowedRoute('post', '/api/v1/bonuses', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('put', '/api/v1/bonuses/{id}', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('get', '/api/v1/bonuses', PermissionList::GET_BONUSES),
            new AllowedRoute('get', '/api/v1/bonuses/statistics', PermissionList::GET_BONUSES),
            new AllowedRoute('get', '/api/v1/bonuses/statistics/games', PermissionList::STATISTICS_PAGE),
            new AllowedRoute('post', '/api/v1/bonuses/welcome', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('get', '/api/v1/bonuses/betby', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('post', '/api/v1/bonuses/betby/{id}', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('post', '/api/v1/bonuses/onetime/csv', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('post', '/api/v1/bonuses/onetime', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('get', '/api/v1/bonuses/balances/players/{playerId:me|\d+}', PermissionList::GET_BONUSES),
            new AllowedRoute('post', '/api/v1/bonuses/balances', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('get', '/api/v1/bonuses/balances', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('delete', '/api/v1/bonuses/balances/{id:\d+}', PermissionList::UPDATE_BONUSES),
            new AllowedRoute('delete', '/api/v1/bonuses/balances/{id:\d+}', PermissionList::UPDATE_BONUSES),

            new AllowedRoute('post', '/api/v1/bonuses/promo', PermissionList::UPDATE_PROMOCODE),
            new AllowedRoute('put', '/api/v1/bonuses/promo/{id}', PermissionList::UPDATE_PROMOCODE),
            new AllowedRoute('get', '/api/v1/bonuses/promo', PermissionList::GET_PROMOCODE),


            new AllowedRoute('get', '/api/v1/deposits', PermissionList::DEPOSITS_LIST, [MiddlewareEnum::ENCRYPT_PAYMENT_SYSTEM]),
            new AllowedRoute('get', '/api/v1/payouts', PermissionList::PAYOUTS_LIST),
            new AllowedRoute('get', '/api/v1/payouts/count', PermissionList::PAYOUTS_LIST),
            new AllowedRoute('put', '/api/v1/payouts/{id:\d+}', PermissionList::PAYOUTS_EDIT),
            new AllowedRoute('post', '/api/v1/payouts/{id:\d+}', PermissionList::PAYOUTS_EDIT),
            new AllowedRoute('get', '/api/v1/payouts/stats', PermissionList::PAYOUTS_EDIT),
            new AllowedRoute('get', '/api/v1/payouts/count', PermissionList::PAYOUTS_EDIT),
            new AllowedRoute('post', '/api/v1/players/payouts/comment/{id:\d+}', PermissionList::PAYOUTS_LIST),

            new AllowedRoute('get', '/api/v3/payouts', PermissionList::PAYOUTS_LIST, [MiddlewareEnum::ENCRYPT_PAYMENT_SYSTEM]),
            new AllowedRoute('get', '/api/v3/payouts/count', PermissionList::PAYOUTS_LIST),
            new AllowedRoute('get', '/api/v3/payouts/stats', PermissionList::PAYOUTS_LIST),
            new AllowedRoute('post', '/api/v3/payouts/{id:\d+}/decline', PermissionList::PAYOUTS_EDIT),
            new AllowedRoute('post', '/api/v3/payouts/{id:\d+}/approve', PermissionList::PAYOUTS_EDIT),
            new AllowedRoute('post', '/api/v3/payouts/{id:\d+}/comment', PermissionList::PAYOUTS_EDIT),

            new AllowedRoute('get', '/api/v1/slots/bets', PermissionList::GET_SLOTS),
            new AllowedRoute('get', '/api/v1/sports-bets', PermissionList::GET_SLOTS),
            new AllowedRoute('get', '/api/v1/roles', PermissionList::ROLES_LIST),
            new AllowedRoute('get', '/api/v1/roles?id={id:\d+}', PermissionList::ROLES_LIST),
            new AllowedRoute('put', '/api/v1/roles/{id:\d+}', PermissionList::ROLES_EDIT),
            new AllowedRoute('post', '/api/v1/roles', PermissionList::ROLES_EDIT),
            new AllowedRoute('delete', '/api/v1/roles/{id:\d+}', PermissionList::ROLES_DELETE),
            new AllowedRoute('get', '/api/v1/roles/permissions', PermissionList::ROLES_LIST),

            new AllowedRoute('get', '/api/v3/autopayment', PermissionList::PAYOUTS_SETTING_AUTO_PAYOUT_SWITCH),
            new AllowedRoute('post', '/api/v3/autopayment/manage', PermissionList::PAYOUTS_SETTING_AUTO_PAYOUT_SWITCH),

            new AllowedRoute('get', '/api/v3/players/{id:\d+}/logs/{eventName}', PermissionList::USERS_LIST),

            // Tags
            new AllowedRoute('post', '/api/v3/players/tag', PermissionList::USERS_EDIT),
            new AllowedRoute('delete', '/api/v3/players/tag', PermissionList::USERS_EDIT),

            new AllowedRoute('post', '/api/v3/players/verifications', PermissionList::USERS_VERIFICATIONS_CREATE),
            new AllowedRoute('post', '/api/v3/players/verifications/cancel', PermissionList::USERS_VERIFICATIONS_CANCEL),

            new AllowedRoute('get', '/api/v3/payments', PermissionList::PAYMENTS_LIST),
            new AllowedRoute('get', '/api/v3/admin/slots/provider/names-and-ids-by-providers', PermissionList::GET_SLOTS),

            new AllowedRoute('get', '/api/v3/slots-bets-admin', PermissionList::SLOTS_BETS_HISTORY_ADMIN),
            new AllowedRoute('get', '/api/v3/slots-bets-admin/archive', PermissionList::SLOTS_BETS_HISTORY_ADMIN),

            new AllowedRoute('get', '/api/v4/smart-links/', PermissionList::SMART_LINK_ADMIN_UPDATE_VIEW),
            new AllowedRoute('patch', '/api/v4/smart-links/{id:\d+}/domain', PermissionList::SMART_LINK_ADMIN_UPDATE_VIEW),
            new AllowedRoute('get', '/api/v4/smart-links/history', PermissionList::SMART_LINK_ADMIN_HISTORY),

            new AllowedRoute('get', '/api/v3/slots/sorted', PermissionList::GET_SLOTS),
            new AllowedRoute('put', '/api/v3/admin/slots/providers/change-enable', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('delete', '/api/v3/admin/slots/providers/geolocations/{countryId}', PermissionList::SLOT_PROVIDERS_LOCATION),
            new AllowedRoute('post', '/api/v3/admin/slots/providers/bind-providers-to-country/{countryId}', PermissionList::SLOT_PROVIDERS_LOCATION),

            new AllowedRoute('get', '/api/v3/admin/slots/providers/for-selector', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('get', '/api/v3/admin/slots/providers/{providerId}/slots', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('get', '/api/v3/admin/slots/providers/groups', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('get', '/api/v1/slots/providers/ordered-by-group', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('post', '/api/v3/admin/slots/providers/{providerId}/add-to-group', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('post', '/api/v3/admin/slots/providers/{providerId}/remove-from-group', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('put', '/api/v3/admin/slots/providers/{providerId:\d+}', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('put', '/api/v3/admin/slots/providers/update-slots-weight', PermissionList::UPDATE_SLOTS),
            new AllowedRoute('put', '/api/v3/admin/slots/providers/ids', PermissionList::UPDATE_SLOTS),


            # anything else
//            new AllowedRoute('delete', '{url:.+}', PermissionList::ADMIN),
//            new AllowedRoute('put', '{url:.+}', PermissionList::ADMIN),
//            new AllowedRoute('post', '{url:.+}', PermissionList::ADMIN),
//            new AllowedRoute('get', '{url:.+}', PermissionList::ADMIN),
        ];
    }
}
