<?php


namespace App\Services;

use Deversin\Signature\SignatureInterface;
use Skipper\ApiClient\BasicApiClient;

/**
 * Class PayoutMethodService
 * @package App\Services\Payment
 * @deprecated Use App\Services\HttpService
 */
class PaymentSystemClient extends BasicApiClient
{
    private SignatureInterface $signature;

    /**
     * @param string             $baseUrl
     * @param SignatureInterface $signature
     * @param array              $options
     * @deprecated Use App\Services\HttpService
     */
    public function __construct(
        string             $baseUrl,
        SignatureInterface $signature,
        array              $options = []
    ) {
        parent::__construct($baseUrl, null, $options);
        $this->signature = $signature;
    }

    /**
     * @param $method
     * @param $uri
     * @param $params
     *
     * @return array|object|string
     * @throws \GuzzleHttp\Exception\GuzzleException|\JsonException
     *
     * @deprecated Use App\Services\HttpService
     */
    public function sendRequest($method, $uri, $params)
    {
        return $this->request($method, $uri, [
            'json' => $params,
        ]);
    }

    /**
     * @param string $method
     * @param string $uri
     * @param array  $options
     *
     * @return array|object|string
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \JsonException
     *
     * @deprecated Use App\Services\HttpService
     */
    protected function request(string $method, string $uri, array $options = [])
    {
        $params = $options['json'];

        $options['headers'][SignatureInterface::SIGNATURE_HEADER] =
            $this->signature->sign(json_encode($params, JSON_THROW_ON_ERROR));

        return parent::request($method, $uri, $options);
    }
}
