<?php


namespace App\Services\FreeSpins;

use App\Dto\FreeSpinsAvailableAggregatorDto;
use App\Dto\FreeSpinsAvailableProvidersDto;
use App\Repositories\FreeSpins\DataSubscriptionRepository;
use App\Repositories\FreeSpins\SlotsProviderRepository;
use App\Repositories\FreeSpins\SlotsRepository;
use App\Repositories\SubscriptionRepository;
use App\Services\HttpClientService;
use Illuminate\Support\Collection;
use Exception;
use JsonException;


/**
 * Class FreeSpinsService
 * @package App\Services\FreeSpins
 */
class FreeSpinsService
{
    private const BET_ID_URI = '/api/v1/freespins/bets';

    /**
     * @var SlotsRepository
     */
    private SlotsRepository $slotsRepository;
    /**
     * @var SlotsProviderRepository
     */
    private SlotsProviderRepository $slotsProviderRepository;
    /**
     * @var DataSubscriptionRepository
     */
    private DataSubscriptionRepository $dataSubscriptionRepository;
    /**
     * @var HttpClientService
     */
    private HttpClientService $httpClientService;
    /**
     * @var array
     */
    private array $toIntegratorData = [];
    private SubscriptionRepository $subscriptionRepository;

    /**
     * FreeSpinsService constructor.
     * @param SlotsRepository $slotsRepository
     * @param SlotsProviderRepository $slotsProviderRepository
     * @param DataSubscriptionRepository $dataSubscriptionRepository
     * @param HttpClientService $httpClientService
     * @param SubscriptionRepository $subscriptionRepository
     */
    public function __construct(
        SlotsRepository            $slotsRepository,
        SlotsProviderRepository    $slotsProviderRepository,
        DataSubscriptionRepository $dataSubscriptionRepository,
        HttpClientService          $httpClientService,
        SubscriptionRepository     $subscriptionRepository
    )
    {
        $this->slotsRepository = $slotsRepository;
        $this->slotsProviderRepository = $slotsProviderRepository;
        $this->dataSubscriptionRepository = $dataSubscriptionRepository;
        $this->httpClientService = $httpClientService;
        $this->subscriptionRepository = $subscriptionRepository;
    }

    public function getSubscriptionsDetails(int $slots_id)
    {
        $provider = $this->slotsRepository->getBySlotsID("provider", "external_id", $slots_id);
        return $this->dataSubscriptionRepository->getByProvider($provider->provider);
    }

    /**
     * @throws JsonException
     */
    public function getGamesAdditionalData(int $slots_id, string $game_uuid, string $currency): array
    {
        $dataURI = [];
        $data = [];

        $subscriptions_details = $this->getSubscriptionsDetails($slots_id);

        $dataURI['subscriberId'] = (int)$subscriptions_details->sub_id;
        $dataURI['currency'] = $currency;
        $dataURI['game_uuid'] = $game_uuid;
        $response = $this->httpClientService->get($dataURI, $subscriptions_details->game_token, self::BET_ID_URI, env('FREE_SPIN_HOST'));
        if ($response->getStatusCode() !== 200) {
            throw new Exception('Freespin settings not found for this (game, currency) combination');
        }
        $response = json_decode($response->getBody(), false, 512, JSON_THROW_ON_ERROR);

        $denominations = $response->denominations;
        $data['bets'] = $response->bets;
        $data['denominations'] = count($denominations) > 0 ? $denominations : null;


        return $data;
    }

    /**
     * @param int $provider_id
     * @return mixed
     */
    public function availableGames(int $provider_id)
    {
        return $this->slotsRepository->getGames($provider_id);
    }

    /**
     * @return mixed
     */
    public function availableProviders(FreeSpinsAvailableProvidersDto $availableProvidersDto)
    {
        return $this->slotsProviderRepository->availableProviders($availableProvidersDto);
    }

    public function getIPLFreeSpinsAvailableSuppliers(): Collection
    {
        return $this->subscriptionRepository->getSubscriptionsByNames(
            config('free-spins.available-suppliers')
        );
    }

    public function getIPLFreeSpinsAvailableSupplierAggregators($supplierId): array
    {
        $supplierName = $this->subscriptionRepository->getSubscriptionNameById($supplierId);

        return config("free-spins.aggregators.$supplierName") ?? [];
    }

    public function getFreeSpinsAvailableSuppliers(): Collection
    {
        return $this->subscriptionRepository->getSubscriptionsByNames(
            config('free-spins.available-suppliers')
        );
    }

    public function getFreeSpinsAvailableSupplierAggregators(FreeSpinsAvailableAggregatorDto $freeSpinsAvailableAggregatorDto): array
    {
        return $this->slotsProviderRepository->getAvailableAggregators($freeSpinsAvailableAggregatorDto);
    }

    public function getFreeSpinsAvailableAggregators(): array
    {
        return $this->slotsProviderRepository->getAvailableAggregators();
    }
}
