<?php


namespace App\Services\Payment;

use App\Services\PaymentServiceHttpClient;

/**
 * Class DepositsService
 * @package App\Services\Payment
 */
class DepositsService
{
    private PaymentServiceHttpClient $paymentHttpClient;

    public function __construct(PaymentServiceHttpClient $paymentHttpClient)
    {
        $this->paymentHttpClient = $paymentHttpClient;
    }

    /**
     * @param array $request
     * @param string $label
     * @return array
     * @throws \Exception
     */
    public function showFailed(array $request, string $label): array
    {
        $uri = "admin/user/$label/deposits/failed";

        $result = $this->paymentHttpClient->setJson($request)->doGetRequest($uri);

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }
}
