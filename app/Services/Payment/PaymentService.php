<?php

declare(strict_types=1);

namespace App\Services\Payment;

use App\Services\Statistics\Dto\Bets\PaymentsStatisticForBetsPluralDto;
use App\Services\Statistics\Dto\Bets\SearchPaymentsStatisticForBetsDto;
use App\Services\Statistics\Dto\Logs\PaymentsLogsDto;
use App\Services\Statistics\Dto\Logs\SearchPaymentLogsDto;
use App\Services\Statistics\StatisticsClient;
use GuzzleHttp\Exception\GuzzleException;
use Throwable;

class PaymentService
{
    private StatisticsClient $statisticClient;

    public function __construct(
        StatisticsClient $statisticClient
    ) {
        $this->statisticClient = $statisticClient;
    }

    /**
     * @throws GuzzleException
     * @throws Throwable
     */
    public function searchPaymentsLogs(
        SearchPaymentLogsDto $dto
    ): ?PaymentsLogsDto {
        $searchResult = $this->statisticClient->searchPlayerPaymentsLogs($dto);

        if (empty($searchResult)) {
            return null;
        }

        return new PaymentsLogsDto($searchResult);
    }

    /**
     * @throws GuzzleException
     */
    public function searchPaymentsStatisticsForBets(
        SearchPaymentsStatisticForBetsDto $dto
    ): ?PaymentsStatisticForBetsPluralDto {
        $searchResult = $this->statisticClient->searchPaymentsStatisticForBets($dto);

        if (empty($searchResult)) {
            return null;
        }

        return new PaymentsStatisticForBetsPluralDto($searchResult);
    }
}
