<?php

namespace App\Services\Payment;

use App\Services\PaymentServiceHttpClient;

/**
 * Class PresetService
 * @package App\Services\Payment
 */
class PresetService
{
    private PaymentServiceHttpClient $paymentHttpClient;

    public function __construct(PaymentServiceHttpClient $paymentHttpClient)
    {
        $this->paymentHttpClient = $paymentHttpClient;
    }

    /**
     * @param array $request
     * @param string $token
     * @return array
     * @throws \Exception
     */
    public function show(array $request, string $token): array
    {
        $uri = "admin/methods/$token/presets";
        $result = $this->paymentHttpClient->setJson($request)->doGetRequest($uri);

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }

    /**
     * @param array $request
     * @param string $token
     * @return array
     * @throws \Exception
     */
    public function store(array $request, string $token): array
    {
        $uri = "admin/methods/$token/presets";
        $result = $this->paymentHttpClient->setJson($request)->doPostRequest($uri);

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }

    /**
     * @param array $request
     * @param string $token
     * @return array
     * @throws \Exception
     */
    public function destroy(array $request, string $token): array
    {
        $uri = "admin/methods/$token/presets";
        $result = $this->paymentHttpClient->setJson($request)->doDeleteRequest($uri);

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }
}
