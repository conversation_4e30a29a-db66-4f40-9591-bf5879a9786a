<?php

namespace App\Services\Payment;

use App\Services\PaymentServiceHttpClient;

/**
 * Class PaymentMethodsService
 * @package App\Services\Payment
 */
class PaymentMethodsService
{
    private PaymentServiceHttpClient $paymentHttpClient;

    public function __construct(PaymentServiceHttpClient $paymentHttpClient)
    {
        $this->paymentHttpClient = $paymentHttpClient;
    }

    /**
     * @throws \Exception
     */
    public function show(array $request, string $iso): array
    {
        $uri = "admin/methods/get/$iso";

        $result = $this->paymentHttpClient->setJson($request)->doGetRequest($uri);

        return $result->content;
    }

    /**
     * @throws \Exception
     */
    public function showAggregatorsPayments(array $request, string $iso): array
    {
        $uri = "admin/aggregators/get/$iso";

        $result = $this->paymentHttpClient->setJson($request)->doGetRequest($uri);

        return $result->content;
    }

    /**
     * @throws \Exception
     */
    public function getDeterminingUser(array $request): array
    {
        $result = $this->paymentHttpClient->setJson($request)->doGetRequest('admin/transaction/token');

        return $result->content;
    }
}
