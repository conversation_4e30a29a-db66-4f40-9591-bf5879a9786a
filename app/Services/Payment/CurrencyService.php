<?php

namespace App\Services\Payment;

use App\Services\PaymentServiceHttpClient;

/**
 * Class CurrencyService
 * @package App\Services\Payment
 */
class CurrencyService
{
    private PaymentServiceHttpClient $paymentHttpClient;

    public function __construct(PaymentServiceHttpClient $paymentHttpClient)
    {
        $this->paymentHttpClient = $paymentHttpClient;
    }

    /**
     * @param array $request
     *
     * @return array
     * @throws \Exception
     */
    public function getCurrencies(array $request): array
    {
        $result = $this->paymentHttpClient->setJson($request)->doGetRequest('admin/currencies/get');

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }

}
