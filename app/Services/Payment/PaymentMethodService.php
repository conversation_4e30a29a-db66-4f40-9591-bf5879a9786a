<?php


namespace App\Services\Payment;

use App\Services\PaymentServiceHttpClient;

/**
 * Class PaymentMethodService
 * @package App\Services\Payment
 */
class PaymentMethodService
{
    private PaymentServiceHttpClient $paymentHttpClient;

    public function __construct(PaymentServiceHttpClient $paymentHttpClient)
    {
        $this->paymentHttpClient = $paymentHttpClient;
    }

    /**
     * @param array $request
     * @param string $token
     * @return array
     * @throws \Exception
     */
    public function show(array $request, string $token): array
    {
        $uri = "admin/method/$token";

        $result = $this->paymentHttpClient->setJson($request)->doGetRequest($uri);

        if (isset($result->content['status']) && $result->content['status'] === 'fail') {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;
    }

    /**
     * @param array  $request
     * @param string $token
     *
     * @return array
     * @throws \Exception
     */
    public function update(array $request, string $token): array
    {
        $uri = "admin/method/$token";

        $result = $this->paymentHttpClient->setJson($request)->doPostRequest($uri);

        if (!$result->isSuccess) {
            throw new \Exception($result->content['message'] ?? $result->content['errors'][0]);
        }

        return $result->content;

    }

}
