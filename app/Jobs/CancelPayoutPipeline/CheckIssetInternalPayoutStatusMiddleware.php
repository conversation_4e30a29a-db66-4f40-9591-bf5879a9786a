<?php declare(strict_types=1);


namespace App\Jobs\CancelPayoutPipeline;


use App\Jobs\CancelledPayoutChangeStatusJob;
use App\Services\Payout\WithdrawOrderService;
use Illuminate\Support\Facades\Log;
use Closure;

class CheckIssetInternalPayoutStatusMiddleware
{
    protected WithdrawOrderService $withdrawOrderService;

    public function __construct(WithdrawOrderService $withdrawOrderService)
    {
        $this->withdrawOrderService = $withdrawOrderService;
    }

    /**
     * @param CancelledPayoutChangeStatusJob $job
     * @param Closure                        $next
     *
     * @return mixed|void
     */
    public function handle(CancelledPayoutChangeStatusJob $job, Closure $next)
    {
        $adminPayoutStatus = $this->withdrawOrderService->getInternalPayoutStatus($job->internalPayoutId);
        if ($adminPayoutStatus !== null) {
            return $next($job);
        }

        Log::info("Internal payout status not found by payout_id = $job->internalPayoutId");
    }
}
