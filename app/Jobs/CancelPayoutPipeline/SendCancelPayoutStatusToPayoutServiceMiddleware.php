<?php declare(strict_types=1);


namespace App\Jobs\CancelPayoutPipeline;


use App\Enums\InternalPayoutStatusEnum;
use App\Enums\PayoutServicePayoutStatusEnum;
use App\Jobs\CancelledPayoutChangeStatusJob;
use App\Services\Payout\WithdrawOrderService;
use App\Services\PayoutServiceHttpClient;
use Closure;
use Illuminate\Support\Facades\Log;

class SendCancelPayoutStatusToPayoutServiceMiddleware
{
    protected WithdrawOrderService $withdrawOrderService;
    protected PayoutServiceHttpClient $payoutServiceHttpClient;

    public function __construct(WithdrawOrderService $withdrawOrderService, PayoutServiceHttpClient $payoutServiceHttpClient)
    {
        $this->withdrawOrderService = $withdrawOrderService;
        $this->payoutServiceHttpClient = $payoutServiceHttpClient;
    }

    /**
     * @param CancelledPayoutChangeStatusJob $job
     * @param Closure                        $next
     *
     * @return mixed|void
     */
    public function handle(CancelledPayoutChangeStatusJob $job, Closure $next)
    {
        $adminPayoutStatus = $this->withdrawOrderService->getInternalPayoutStatus($job->internalPayoutId);

        if ($adminPayoutStatus === null) {
            return;
        }

        if ($adminPayoutStatus->payouts_system_status === PayoutServicePayoutStatusEnum::PAYOUT_CLOSE) {
            return $next($job);
        }

        $payoutServiceResponse = $this->payoutServiceHttpClient
            ->setJson(['withdraw_order_token' => $job->withdrawOrderToken])
            ->doPostRequest('admin/withdraw/order/close');

        if ($payoutServiceResponse->isSuccess === false) {
            $this->withdrawOrderService->updateInternalPayoutStatus(
                $adminPayoutStatus->id,
                InternalPayoutStatusEnum::CLOSE_FAILED,
                null,
                'Произошла ошибка при отправке запроса на закрытие выплаты в платёжный сервис'
            );

            return;
        }

        $newStatus = $payoutServiceResponse->content['status'] ?? null;

        if ($newStatus === PayoutServicePayoutStatusEnum::PAYOUT_CLOSE || $newStatus === PayoutServicePayoutStatusEnum::NO_CHANGES) {
            $this->withdrawOrderService->updateInternalPayoutStatus(
                $adminPayoutStatus->id,
                null,
                PayoutServicePayoutStatusEnum::PAYOUT_CLOSE,
                null
            );

            return $next($job);
        }

        Log::error('Failed to cancel the payout on the payment service side');

        $this->withdrawOrderService->updateInternalPayoutStatus(
            $adminPayoutStatus->id,
            InternalPayoutStatusEnum::RETRY_FAILED,
            null,
            'Не удалось отменить заявку на вывод на стороне платежного сервиса',
        );
    }
}
