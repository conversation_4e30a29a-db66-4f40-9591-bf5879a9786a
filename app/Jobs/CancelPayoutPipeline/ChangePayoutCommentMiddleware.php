<?php declare(strict_types=1);


namespace App\Jobs\CancelPayoutPipeline;


use App\Jobs\CancelledPayoutChangeStatusJob;
use App\Models\Payout;
use Closure;

class ChangePayoutCommentMiddleware
{
    /**
     * @param CancelledPayoutChangeStatusJob $job
     * @param Closure                        $next
     *
     * @return mixed|void
     */
    public function handle(CancelledPayoutChangeStatusJob $job, Closure $next)
    {
        Payout::query()
            ->where('id', $job->internalPayoutId)
            ->update(['comment' => $job->comment]);

        return $next($job);
    }
}
