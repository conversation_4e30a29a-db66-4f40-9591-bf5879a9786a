<?php declare(strict_types=1);


namespace App\Jobs\CancelPayoutPipeline;


use App\Enums\InternalPayoutStatusEnum;
use App\Enums\PayoutServicePayoutStatusEnum;
use App\Jobs\CancelledPayoutChangeStatusJob;
use App\Models\Payout;
use App\Services\Payout\WithdrawOrderService;
use App\Services\PlayerAppClient;
use Closure;
use Illuminate\Support\Facades\Log;

class SendCancelPayoutStatusToWhitelabelMiddleware
{
    protected WithdrawOrderService $withdrawOrderService;
    protected PlayerAppClient $playerAppClient;

    public function __construct(WithdrawOrderService $withdrawOrderService, PlayerAppClient $playerAppClient)
    {
        $this->withdrawOrderService = $withdrawOrderService;
        $this->playerAppClient = $playerAppClient;
    }

    /**
     * @param CancelledPayoutChangeStatusJob $job
     * @param Closure                        $next
     *
     * @return mixed|void
     */
    public function handle(CancelledPayoutChangeStatusJob $job, Closure $next)
    {
        $adminPayoutStatus = $this->withdrawOrderService->getInternalPayoutStatus($job->internalPayoutId);

        if ($adminPayoutStatus === null) {
            return;
        }

        if ($adminPayoutStatus->payouts_system_status !== PayoutServicePayoutStatusEnum::PAYOUT_CLOSE) {
            return;
        }

        $payout = $this->withdrawOrderService->getInternalPayout($job->withdrawOrderToken);
        if ($payout === null || $payout->status === InternalPayoutStatusEnum::CLOSED) {
            $this->withdrawOrderService->deleteInternalPayoutStatus($adminPayoutStatus->id);

            return $next($job);
        }

        try {
            $this->playerAppClient->sendClosedPayoutStatus($job->internalPayoutId, $job->clientId);
        } catch (\Throwable $e) {
            Log::error("Close request to core service failed | {$e->getMessage()}");
            $this->withdrawOrderService->updateInternalPayoutStatus(
                $adminPayoutStatus->id,
                InternalPayoutStatusEnum::CLOSE_CORE_FAILED,
                PayoutServicePayoutStatusEnum::PAYOUT_CLOSE,
                'Произошла ошибка при отправке запроса на закрытие выплаты в кору'
            );

            return;
        }

        $this->withdrawOrderService->deleteInternalPayoutStatus($adminPayoutStatus->id);

        return $next($job);
    }
}
