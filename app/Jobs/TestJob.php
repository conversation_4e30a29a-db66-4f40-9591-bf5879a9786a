<?php declare(strict_types=1);


namespace App\Jobs;


use Illuminate\Queue\Middleware\RateLimitedWithRedis;
use Illuminate\Support\Facades\Log;

class TestJob extends Job
{
    public function middleware(): array
    {
        return [
            (new RateLimitedWithRedis('oneTimeBonusJob'))
        ];
    }
    public function handle()
    {
        usleep(500_000);
        Log::info('Test job success completed');
    }
}
