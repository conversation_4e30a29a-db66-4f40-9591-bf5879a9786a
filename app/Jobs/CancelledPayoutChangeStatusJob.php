<?php declare(strict_types=1);

namespace App\Jobs;

use App\Jobs\CancelPayoutPipeline\ChangePayoutCommentMiddleware;
use App\Jobs\CancelPayoutPipeline\CheckIssetInternalPayoutStatusMiddleware;
use App\Jobs\CancelPayoutPipeline\SendCancelPayoutStatusToPayoutServiceMiddleware;
use App\Jobs\CancelPayoutPipeline\SendCancelPayoutStatusToWhitelabelMiddleware;

class CancelledPayoutChangeStatusJob extends Job
{
    public string $withdrawOrderToken;
    public string $paymentGateway;
    public string $transactionId;
    public string $comment;
    public int $clientId;
    public int $internalPayoutId;

    /**
     * CancelledPayoutChangeStatusJob constructor.
     *
     * @param int    $internalPayoutId
     * @param string $withdrawOrderToken
     * @param string $paymentGateway
     * @param string $transactionId
     * @param string $comment
     * @param int    $clientId
     */
    public function __construct(int $internalPayoutId, string $withdrawOrderToken, string $paymentGateway, string $transactionId, string $comment, int $clientId)
    {
        $this->afterCommit();

        $this->internalPayoutId = $internalPayoutId;
        $this->withdrawOrderToken = $withdrawOrderToken;
        $this->paymentGateway = $paymentGateway;
        $this->transactionId = $transactionId;
        $this->comment = $comment;
        $this->clientId = $clientId;
        $this->onQueue('cancel_payout_queue');
    }

    public function backoff(): array
    {
        return [
            1,
            60,
            120,
        ];
    }

    public function middleware(): array
    {
        return [
            CheckIssetInternalPayoutStatusMiddleware::class,
            SendCancelPayoutStatusToPayoutServiceMiddleware::class,
            SendCancelPayoutStatusToWhitelabelMiddleware::class,
            ChangePayoutCommentMiddleware::class,
        ];
    }

    public function handle(): void
    {

    }
}
