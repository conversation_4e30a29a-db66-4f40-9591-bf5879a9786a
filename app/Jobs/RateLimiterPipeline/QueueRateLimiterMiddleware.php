<?php

namespace App\Jobs\RateLimiterPipeline;

use Closure;
use Exception;
use Illuminate\Contracts\Redis\LimiterTimeoutException;
use Illuminate\Support\Facades\Redis;

class QueueRateLimiterMiddleware
{
    protected bool $enabled;
    protected string $connectionName = '';
    protected string $aggregatorName;
    protected int $timeSpanInSeconds;
    protected int $allowedNumberOfJobsInTimeSpan;
    protected int $releaseInSeconds;
    protected bool $dontRelease;

    public function __construct()
    {
        $this->initConfig();
    }

    public static function jobExceptionCounterKey(string $jobUUID): string
    {
        return "job_exception_counter:" . $jobUUID;
    }

    public function enabled(bool $enabled = true): QueueRateLimiterMiddleware
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function dontRelease(): QueueRateLimiterMiddleware
    {
        $this->dontRelease = true;

        return $this;
    }

    public function connectionName(string $connectionName): QueueRateLimiterMiddleware
    {
        $this->connectionName = $connectionName;

        return $this;
    }

    public function aggregatorName(string $aggregatorName): QueueRateLimiterMiddleware
    {
        $this->aggregatorName = $aggregatorName;

        return $this;
    }

    public function allow(int $allowedNumberOfJobsInTimeSpan): QueueRateLimiterMiddleware
    {
        $this->allowedNumberOfJobsInTimeSpan = $allowedNumberOfJobsInTimeSpan;

        return $this;
    }

    public function everySeconds(int $timespanInSeconds = 1): QueueRateLimiterMiddleware
    {
        $this->timeSpanInSeconds = $timespanInSeconds;

        return $this;
    }

    public function everyMinutes(int $timespanInMinutes = 1): QueueRateLimiterMiddleware
    {
        return $this->everySeconds($timespanInMinutes * 60);
    }

    public function releaseAfterSeconds(int $releaseInSeconds): QueueRateLimiterMiddleware
    {
        $this->releaseInSeconds = $releaseInSeconds;

        return $this;
    }

    public function releaseAfterMinutes(int $releaseInMinutes): QueueRateLimiterMiddleware
    {
        return $this->releaseAfterSeconds($releaseInMinutes * 60);
    }

    protected function getThrottleKey(QueueRateLimiterInterface $job): string
    {
        $aggregatorName = $this->aggregatorName ?? get_class($job) ?? '';

        return "queue_rate_limiter_throttler:$aggregatorName";
    }

    /**
     * @throws LimiterTimeoutException
     */
    public function handle(QueueRateLimiterInterface $job, Closure $next): void
    {
        if (!$this->enabled) {
            $next($job);
            return;
        }

        Redis::connection($this->connectionName)
            ->throttle($this->getThrottleKey($job))
            ->block(0)
            ->allow($this->allowedNumberOfJobsInTimeSpan)
            ->every($this->timeSpanInSeconds)
            ->then(function () use ($job, $next) {
                try {
                    $next($job);
                } catch (Exception $exception){
                    $this->handleException($job, $exception);
                }
            }, function () use ($job) {
                $this->releaseJob($job);
            });
    }

    /**
     * @throws Exception
     */
    private function handleException(QueueRateLimiterInterface $job, Exception $exception): void
    {
        $jobId = $job->job->getJobId();
        $counterKey = QueueRateLimiterMiddleware::jobExceptionCounterKey($jobId);
        $ttl = array_sum($job->backoff()) + $this->timeSpanInSeconds;

        // Проверяем, существует ли ключ в Redis
        if (!Redis::get($counterKey)) {
            // Если ключ не существует, устанавливаем его и устанавливаем время жизни
            Redis::set($counterKey, $jobId);
            Redis::expire($counterKey, $ttl);
        }

        throw $exception;
    }

    private function releaseJob(QueueRateLimiterInterface $job): void
    {
        if (!$this->dontRelease) {
            $job->release($this->releaseInSeconds);
        }
    }

    private function initConfig(): void
    {
        $this->enabled = config('queue.rate_limiter.enabled');
        $this->dontRelease = config('queue.rate_limiter.dont_release');
        $this->allowedNumberOfJobsInTimeSpan = config('queue.rate_limiter.allowed_number');
        $this->timeSpanInSeconds = config('queue.rate_limiter.time_span_seconds');
        $this->releaseInSeconds = config('queue.rate_limiter.release_seconds');
    }
}
