<?php declare(strict_types=1);

namespace App\Jobs;

use App\Enums\InternalPayoutStatusEnum;
use App\Enums\PayoutServicePayoutStatusEnum;
use App\Services\Payout\WithdrawOrderService;
use App\Services\PayoutServiceHttpClient;
use Carbon\Carbon;
use Illuminate\Contracts\Bus\Dispatcher;
use Illuminate\Support\Facades\Log;

/**
 * Class RetryPayoutChangeStatusJob
 * @package App\Jobs
 */
class RetryPayoutChangeStatusJob extends Job
{
    private array $retryPayout;
    private bool $checkInProcess;
    private int $internalPayoutId;

    private string $withdrawOrderToken;

    /**
     * RetryPayoutChangeStatusJob constructor.
     *
     * @param int    $internalPayoutId
     * @param string $withdrawOrderToken
     * @param array  $retryPayout
     * @param bool   $checkInProcess
     */
    public function __construct(int $internalPayoutId, string $withdrawOrderToken, array $retryPayout, bool $checkInProcess = false)
    {
        $this->afterCommit();

        $this->retryPayout = $retryPayout;
        $this->onQueue('retry_payout_queue');
        $this->checkInProcess = $checkInProcess;
        $this->internalPayoutId = $internalPayoutId;
        $this->withdrawOrderToken = $withdrawOrderToken;
    }

    public function backoff(): array
    {
        return [
            1,
            60,
            120,
        ];
    }

    public function handle(PayoutServiceHttpClient $payoutServiceHttpClient, WithdrawOrderService $withdrawOrderService, Dispatcher $dispatcher): void
    {
        $adminPayoutStatus = $withdrawOrderService->getInternalPayoutStatus($this->internalPayoutId);
        if ($adminPayoutStatus === null) {
            return;
        }

        if ($adminPayoutStatus->updated_at < Carbon::now()->subHour()) {
            $withdrawOrderService->updateInternalPayoutStatus(
                $adminPayoutStatus->id,
                InternalPayoutStatusEnum::UNDEFINED_ERROR,
                null,
                'Запрос на смену агрегатора был отправлен в платёжный сервис, но смена не произошла по в течение часа'
            );

            Log::error("Retry failed by timeout");
        }

        if ($this->checkInProcess) {
            $payout = $withdrawOrderService->getInternalPayout($this->withdrawOrderToken);
            if ($payout !== null && ($payout->status !== InternalPayoutStatusEnum::DECLINED || $payout->status !== InternalPayoutStatusEnum::CREATED)) {
                $withdrawOrderService->deleteInternalPayoutStatus($adminPayoutStatus->id);
                return;
            }
        } else {
            $payoutServiceResponse = $payoutServiceHttpClient->setJson($this->retryPayout)->doPostRequest('admin/withdraw/order/retry');
            if ($payoutServiceResponse->isSuccess === false) {
                $withdrawOrderService->updateInternalPayoutStatus(
                    $adminPayoutStatus->id,
                    InternalPayoutStatusEnum::RETRY_FAILED,
                    null,
                    'Произошла ошибка при отправке запроса на смену агрегатора в платёжный сервис'
                );

                return;
            }

            if (isset($payoutServiceResponse->content['status']) && $payoutServiceResponse->content['status'] === PayoutServicePayoutStatusEnum::INVALID) {
                $withdrawOrderService->updateInternalPayoutStatus(
                    $adminPayoutStatus->id,
                    InternalPayoutStatusEnum::RETRY_FAILED,
                    null,
                    $payoutServiceResponse->content['message'] ?? null
                );

                Log::info('Retry failed on payout service side', ['data' => $payoutServiceResponse->content]);

                return;
            }

            if (isset($payoutServiceResponse->content['token'])) {
                $this->withdrawOrderToken = $payoutServiceResponse->content['token'];
                $withdrawOrderService->updateInternalPayoutStatus(
                    $adminPayoutStatus->id,
                    null,
                    null,
                    null,
                    $this->withdrawOrderToken
                );
            }
        }

        $this->retryJob($dispatcher);
    }

    protected function retryJob(Dispatcher $dispatcher): void
    {
        $job = new RetryPayoutChangeStatusJob($this->internalPayoutId, $this->withdrawOrderToken, $this->retryPayout, true);

        $job->delay(10);
        $dispatcher->dispatch($job);
    }
}
