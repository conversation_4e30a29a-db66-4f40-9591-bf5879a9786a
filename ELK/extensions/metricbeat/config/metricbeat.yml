## Metricbeat configuration
## https://github.com/elastic/beats/blob/master/deploy/docker/metricbeat.docker.yml
#

metricbeat.config:
  modules:
    path: ${path.config}/modules.d/*.yml
    # Reload module configs as they change:
    reload.enabled: false

metricbeat.autodiscover:
  providers:
    - type: docker
      hints.enabled: true

metricbeat.modules:
- module: docker
  metricsets:
    - container
    - cpu
    - diskio
    - healthcheck
    - info
    #- image
    - memory
    - network
  hosts: ['unix:///var/run/docker.sock']
  period: 10s
  enabled: true

processors:
  - add_cloud_metadata: ~

output.elasticsearch:
  hosts: ['http://elasticsearch:9200']
  username: elastic
  password: changeme

## HTTP endpoint for health checking
## https://www.elastic.co/guide/en/beats/metricbeat/current/http-endpoint.html
#

http.enabled: true
http.host: 0.0.0.0
