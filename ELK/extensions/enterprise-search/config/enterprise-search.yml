---
## Enterprise Search core configuration
## https://www.elastic.co/guide/en/enterprise-search/current/configuration.html
#

## --------------------- REQUIRED ---------------------

# Encryption keys to protect application secrets.
secret_management.encryption_keys:
  # add encryption keys below
  #- add encryption keys here

## ----------------------------------------------------

# IP address Enterprise Search listens on
ent_search.listen_host: 0.0.0.0

# URL at which users reach Enterprise Search
ent_search.external_url: http://localhost:3002

# Elasticsearch URL and credentials
elasticsearch.host: http://elasticsearch:9200
elasticsearch.username: elastic
elasticsearch.password: changeme

# Allow Enterprise Search to modify Elasticsearch settings. Used to enable auto-creation of Elasticsearch indexes.
allow_es_settings_modification: true
