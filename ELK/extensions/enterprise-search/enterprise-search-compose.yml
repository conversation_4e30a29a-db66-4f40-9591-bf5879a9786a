version: '3.2'

services:
  enterprise-search:
    build:
      context: extensions/enterprise-search/
      args:
        ELK_VERSION: $ELK_VERSION
    volumes:
      - type: bind
        source: ./extensions/enterprise-search/config/enterprise-search.yml
        target: /usr/share/enterprise-search/config/enterprise-search.yml
        read_only: true
    environment:
      JAVA_OPTS: -Xmx2g -Xms2g
      ENT_SEARCH_DEFAULT_PASSWORD: changeme
    ports:
      - '3002:3002'
    networks:
      - elk
    depends_on:
      - elasticsearch
