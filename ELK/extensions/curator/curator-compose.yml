version: '3.2'

services:
  curator:
    build:
      context: extensions/curator/
    init: true
    volumes:
      - type: bind
        source: ./extensions/curator/config/curator.yml
        target: /usr/share/curator/config/curator.yml
        read_only: true
      - type: bind
        source: ./extensions/curator/config/delete_log_files_curator.yml
        target: /usr/share/curator/config/delete_log_files_curator.yml
        read_only: true
    networks:
      - elk
    depends_on:
      - elasticsearch
