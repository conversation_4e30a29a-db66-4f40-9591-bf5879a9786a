version: '3.2'

services:
  apm-server:
    build:
      context: extensions/apm-server/
      args:
        ELK_VERSION: $ELK_VERSION
    command:
        # Disable strict permission checking on 'apm-server.yml' configuration file
        # https://www.elastic.co/guide/en/beats/libbeat/current/config-file-permissions.html
      - --strict.perms=false
    volumes:
      - type: bind
        source: ./extensions/apm-server/config/apm-server.yml
        target: /usr/share/apm-server/apm-server.yml
        read_only: true
    ports:
      - '8200:8200'
    networks:
      - elk
    depends_on:
      - elasticsearch
