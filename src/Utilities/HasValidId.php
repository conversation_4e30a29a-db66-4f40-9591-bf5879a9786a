<?php
namespace WhiteLabelAdmin\Utilities;

use Symfony\Component\Validator\Constraints as Assert;

trait HasValidId
{
    /**
     * @var int|null
     * @Assert\NotBlank(groups={"get", "update", "delete", "change_password"})
     * @Assert\Type(type="numeric")
     * @Assert\GreaterThan(value=0)
     */
    private $id;

    /**
     * @var int
     * @Assert\NotBlank
     * @Assert\Type(type="numeric")
     * @Assert\GreaterThan(value=0)
     */
    private $clientId;

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return (int)$this->clientId;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id ? (int)$this->id : null;
    }
}
