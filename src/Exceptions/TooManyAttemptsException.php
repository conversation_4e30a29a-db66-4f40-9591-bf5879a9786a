<?php
namespace WhiteLabelAdmin\Exceptions;

use Skipper\Exceptions\DomainException;
use Skipper\Exceptions\Error;
use Throwable;

class TooManyAttemptsException extends DomainException
{
    private int $maxAttempts;
    private int $blockedUntil;
    private string $path;

    public function __construct(
        string $path,
        int $maxAttempts,
        int $blockedUntil,
        Throwable $previous = null
    ) {
        parent::__construct(__('errors.too_many_attempts'), 'request', [], $previous, 429);
        $this->cleanErrors();
        $this->addError(new Error(__('errors.too_many_attempts'), 'tooManyRequests', 'request'));
        $this->maxAttempts = $maxAttempts;
        $this->blockedUntil = $blockedUntil;
        $this->path = $path;
    }

    /**
     * @return string
     */
    public function getPath(): string
    {
        return $this->path;
    }

    /**
     * @return array
     */
    public function render(): array
    {
        return array_merge(parent::render(), [
            'max-attempts' => $this->getMaxAttempts(),
            'retry-after' => $this->getBlockedUntil(),
            'path' => $this->getPath(),
        ]);
    }

    /**
     * @return int
     */
    public function getMaxAttempts(): int
    {
        return $this->maxAttempts;
    }

    /**
     * @return int
     */
    public function getBlockedUntil(): int
    {
        return $this->blockedUntil;
    }

}
