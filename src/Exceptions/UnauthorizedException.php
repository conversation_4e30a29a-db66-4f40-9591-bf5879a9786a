<?php
namespace WhiteLabelAdmin\Exceptions;

use <PERSON>pper\Exceptions\DomainException;

class UnauthorizedException extends DomainException
{
    public function __construct(?\Throwable $previous = null, int $code = 401) {
        parent::__construct('Unauthorized', 'headers.authorization', [
            'hint' => 'use Bearer token to pass valid access token',
        ], $previous, $code);
    }
}
