<?php
namespace WhiteLabelAdmin\Factories;

use White<PERSON>abelAdmin\Requests\ClientSettingsRequest;
use WhiteLabelAdmin\Requests\CountryRequest;
use WhiteLabelAdmin\Requests\CurrencyRequest;
use WhiteLabelAdmin\Requests\GameCategoryRequest;
use WhiteLabelAdmin\Requests\PageRequest;
use WhiteLabelAdmin\Requests\PaymentGatewayRequest;
use WhiteLabelAdmin\Requests\PlayerCommentRequest;
use WhiteLabelAdmin\Requests\PlayerRequest;
use WhiteLabelAdmin\Requests\ProxyRequest;
use WhiteLabelAdmin\Requests\RoleRequest;
use WhiteLabelAdmin\Requests\SearchRequest;
use WhiteLabelAdmin\Requests\UserRequest;

interface RequestFactoryInterface
{
    /**
     * @param int $playerId
     * @return PlayerRequest
     */
    public function createPlayerRequest(int $playerId): PlayerRequest;

    /**
     * @param int|null $userId
     * @return UserRequest
     */
    public function createUserRequest(?int $userId): UserRequest;

    /**
     * @param int|null $roleId
     * @return RoleRequest
     */
    public function createRoleRequest(?int $roleId): RoleRequest;

    /**
     * @return SearchRequest
     */
    public function createSearchRequest(): SearchRequest;

    /**
     * @param string $iso
     * @return CurrencyRequest
     */
    public function createCurrencyRequest(string $iso): CurrencyRequest;

    /**
     * @param string $iso
     * @return CountryRequest
     */
    public function createCountryRequest(string $iso): CountryRequest;

    /**
     * @param string $slug
     * @return PageRequest
     */
    public function createPageRequest(string $slug): PageRequest;

    /**
     * @param string $name
     * @return PaymentGatewayRequest
     */
    public function createGatewayRequest(string $name): PaymentGatewayRequest;

    /**
     * @return ClientSettingsRequest
     */
    public function createSettingsRequest(): ClientSettingsRequest;

    /**
     * @param string $type
     * @param string $slug
     * @return GameCategoryRequest
     */
    public function createCategoryRequest(string $type, string $slug): GameCategoryRequest;

    /**
     * @return PlayerCommentRequest
     */
    public function createPlayerCommentRequest(): PlayerCommentRequest;

    /**
     * @return ProxyRequest
     */
    public function createProxyRequest(): ProxyRequest;
}
