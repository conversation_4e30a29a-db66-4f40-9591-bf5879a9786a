<?php
namespace WhiteLabelAdmin\Controllers;

use Skipper\Exceptions\InvalidArgumentException;
use Skipper\Exceptions\SymfonyValidationException;
use Skipper\Repository\Exceptions\EntityNotFoundException;
use Skipper\Repository\Exceptions\StorageException;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Services\ObjectValidator;
use WhiteLabelAdmin\Services\Search\UserSeeker;
use WhiteLabelAdmin\Services\UserService;
use OpenApi\Annotations as OA;

final class UserController
{
    use SearchTrait;

    /**
     * @var RequestFactoryInterface
     */
    private RequestFactoryInterface $factory;

    /**
     * @var ObjectValidator
     */
    private ObjectValidator $validator;

    /**
     * @var UserService
     */
    private UserService $users;

    /**
     * @var UserSeeker
     */
    private UserSeeker $seeker;

    public function __construct(
        RequestFactoryInterface $factory,
        ObjectValidator $validator,
        UserService $users,
        UserSeeker $seeker
    ) {
        $this->factory = $factory;
        $this->validator = $validator;
        $this->users = $users;
        $this->seeker = $seeker;
    }

    /**
     * @return array
     * @throws InvalidArgumentException
     * @throws SymfonyValidationException
     * @throws EntityNotFoundException
     * @throws StorageException
     * @OA\Post(
     *     path="/api/v1/users",
     *     description="register user",
     *     tags={"User"},
     *     security={{"oauth": {}}},
     *     @OA\RequestBody(@OA\JsonContent(properties={
     *          @OA\Property(property="login", description="user login", type="string"),
     *          @OA\Property(property="email", description="user email", type="string", format="email"),
     *          @OA\Property(property="password", description="user password", type="string", format="string", minLength=6),
     *          @OA\Property(property="repeat_password", description="repeat user password", type="string", format="string", minLength=6),
     *          @OA\Property(property="role_id", description="assigned role", type="integer", minimum=1)
     *     })),
     *     @OA\Response(response="200", description="created user", @OA\JsonContent(ref="#/components/schemas/user"))
     * )
     */
    public function register(): array
    {
        $request = $this->factory->createUserRequest(null);
        $this->validator->validate($request, ['create']);
        $user = $this->users->register($request);

        return $user->jsonSerialize();
    }

    /**
     * @param int $userId
     * @return array
     * @throws InvalidArgumentException
     * @throws SymfonyValidationException
     * @throws StorageException
     * @OA\Put(
     *     path="/api/v1/users/{id}",
     *     description="update user role",
     *     tags={"User"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/id"),
     *     @OA\RequestBody(@OA\JsonContent(properties={
     *          @OA\Property(property="role_id", description="assigned role", type="integer", minimum=1)
     *     })),
     *     @OA\Response(response="200", description="updated user", @OA\JsonContent(ref="#/components/schemas/user"))
     * )
     */
    public function updateUser(int $userId): array
    {
        $request = $this->factory->createUserRequest($userId);
        $this->validator->validate($request, ['update']);
        $user = $this->users->updateUser($request);

        return $user->jsonSerialize();
    }

    /**
     * @param int $userId
     * @return array
     * @throws SymfonyValidationException
     * @throws StorageException
     * @throws UnauthorizedException
     * @OA\Post(
     *     path="/api/v1/users/{id}",
     *     description="change password",
     *     tags={"User"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/id"),
     *     @OA\RequestBody(@OA\JsonContent(properties={
     *          @OA\Property(property="password", description="user password", type="string", format="string", minLength=6),
     *          @OA\Property(property="repeat_password", description="repeat user password", type="string", format="string", minLength=6),
     *          @OA\Property(property="old_password", description="old user password", type="string", format="string", minLength=6)
     *     })),
     *     @OA\Response(response="200", description="updated user", @OA\JsonContent(ref="#/components/schemas/user"))
     * )
     */
    public function changePassword(int $userId): array
    {
        $request = $this->factory->createUserRequest($userId);
        $this->validator->validate($request, ['change_password']);
        $user = $this->users->changePassword($request);

        return $user->jsonSerialize();
    }

    /**
     * @param int $userId
     * @return array
     * @throws SymfonyValidationException
     * @throws StorageException
     * @OA\Delete(
     *     path="/api/v1/users/{id}",
     *     description="delete user",
     *     tags={"User"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/id"),
     *     @OA\Response(response="200", description="deleted user", @OA\JsonContent(ref="#/components/schemas/user"))
     * )
     */
    public function deleteUser(int $userId): array
    {
        $request = $this->factory->createUserRequest($userId);
        $this->validator->validate($request, ['delete']);
        $user = $this->users->deleteUser($request);

        return $user->jsonSerialize();
    }

    /**
     * @param int $userId
     * @return array
     * @throws SymfonyValidationException
     * @OA\Get(
     *     path="/api/v1/users/{id}",
     *     description="get user profile",
     *     tags={"User"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/id"),
     *     @OA\Response(response="200", description="found user", @OA\JsonContent(ref="#/components/schemas/user"))
     * )
     */
    public function getUserProfile(int $userId): array
    {
        $request = $this->factory->createUserRequest($userId);
        $this->validator->validate($request, ['get']);
        $user = $this->users->getUser($request);

        return $user->jsonSerialize();
    }

    /**
     * @return array
     * @OA\Get(
     *     path="/api/v1/users",
     *     tags={"User"},
     *     description="search for user",
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/page_param"),
     *     @OA\Parameter(ref="#/components/parameters/limit_param"),
     *     @OA\Parameter(ref="#/components/parameters/all_param"),
     *     @OA\Parameter(ref="#/components/parameters/id_param"),
     *     @OA\Parameter(ref="#/components/parameters/ids_param"),
     *     @OA\Parameter(ref="#/components/parameters/desc_param"),
     *     @OA\Parameter(ref="#/components/parameters/asc_param"),
     *     @OA\Parameter(in="query", name="email", required=false, allowEmptyValue=false, description="user's email", @OA\Schema(type="string", format="email")),
     *     @OA\Parameter(in="query", name="login", required=false, allowEmptyValue=false, description="user's login", @OA\Schema(type="string")),
     *     @OA\Parameter(in="query", name="role", required=false, allowEmptyValue=false, description="user's role", @OA\Schema(type="string")),
     *     @OA\Response(response="200", description="search result", @OA\JsonContent(properties={
     *          @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/user")),
     *          @OA\Property(property="meta", ref="#/components/schemas/meta")
     *     }))
     * )
     */
    public function searchUsers(): array
    {
        return $this->search($this->seeker, $this->factory);
    }
}
