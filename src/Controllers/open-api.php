<?php

use OpenApi\Annotations as OA;

/**
 * @OA\Parameter(parameter="id", in="path", name="id", @OA\Schema(type="integer", minimum=1), description="entity id", required=true)
 *
 * @OA\SecurityScheme(securityScheme="oauth", type="oauth2", description="default api auth", flows={
 *     @OA\Flow(flow="password", tokenUrl="/api/v1/oauth/token", refreshUrl="/api/v1/oauth/token/refresh", scopes={}),
 *     @OA\Flow(flow="clientCredentials", tokenUrl="/api/v1/oauth/token", refreshUrl="/api/v1/oauth/token/refresh", scopes={})
 * })
 * @OA\Schema(schema="meta", description="search metadata", type="object", properties={
 *     @OA\Property(property="pagination", description="search pagination", type="object", properties={
 *          @OA\Property(property="limit", description="search result limit", type="integer", minimum=1),
 *          @OA\Property(property="offset", description="search offset", type="integer", minimum=0),
 *          @OA\Property(property="page", description="search page", type="integer", minimum=1)
 *     }),
 *     @OA\Property(property="counts", description="search counts", type="object", properties={
 *          @OA\Property(property="total", description="total results", type="integer", minimum=0),
 *          @OA\Property(property="count", description="page results", type="integer", minimum=0),
 *     }),
 *     @OA\Property(property="links", description="page links", type="object", properties={
 *          @OA\Property(property="next", description="next page", type="string"),
 *          @OA\Property(property="prev", description="prev page", type="string"),
 *     }),
 * })
 *
 * @OA\PathItem(
 *     path="/api/v2/{url}",
 *     @OA\Parameter(in="path", name="url", @OA\Schema(type="string", description="api url")),
 *     @OA\Get(
 *          description="get proxy",
 *          tags={"API PROXY"},
 *          @OA\Response(response="200", description="get response")
 *     ),
 *     @OA\Post(
 *          description="post proxy",
 *          tags={"API PROXY"},
 *          @OA\Response(response="200", description="post response")
 *     ),
 *     @OA\Put(
 *          description="put proxy",
 *          tags={"API PROXY"},
 *          @OA\Response(response="200", description="put response")
 *     ),
 *     @OA\Patch(
 *          description="patch proxy",
 *          tags={"API PROXY"},
 *          @OA\Response(response="200", description="patch response")
 *     ),
 *     @OA\Delete(
 *          description="delete proxy",
 *          tags={"API PROXY"},
 *          @OA\Response(response="200", description="delete response")
 *     )
 * )
 *
 * @OA\Parameter(parameter="page_param", in="query", name="pagination", required=false, description="pagination object", @OA\Schema(type="object", properties={
 *     @OA\Property(property="limit", type="integer", minimum=1, description="search limit"),
 *     @OA\Property(property="offset", type="integer", minimum=0, description="search offset"),
 *     @OA\Property(property="page", type="integer", minimum=1, description="search page")
 * }), allowEmptyValue=false, style="deepObject", explode=true)
 * @OA\Parameter(parameter="limit_param", in="query", name="limit", required=false, description="limit", @OA\Schema(type="integer", minimum=1, default=50), allowEmptyValue=false)
 * @OA\Parameter(parameter="all_param", in="query", name="all", required=false, description="offset", @OA\Schema(type="string", enum={"1", "yes", "true", "on"}), allowEmptyValue=false)
 * @OA\Parameter(parameter="id_param", in="query", name="id", required=false, description="id", @OA\Schema(type="integer", minimum=1), allowEmptyValue=false)
 * @OA\Parameter(parameter="desc_param", in="query", name="desc", required=false, description="sort desc", @OA\Schema(type="string"), allowEmptyValue=false)
 * @OA\Parameter(parameter="asc_param", in="query", name="asc", required=false, description="sort asc", @OA\Schema(type="string"), allowEmptyValue=false)
 * @OA\Parameter(parameter="ids_param", in="query", name="ids", required=false, description="comma separated ids", @OA\Schema(type="array", @OA\Items(type="integer", minimum=1)), allowEmptyValue=false, style="form", explode=false)
 *
 */
