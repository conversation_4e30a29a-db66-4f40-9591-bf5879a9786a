<?php
namespace WhiteLabelAdmin\Controllers;

use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Services\CountryService;
use WhiteLabelAdmin\Services\ObjectValidator;
use WhiteLabelAdmin\Services\Search\CountrySeeker;

final class CountryController
{
    use SearchTrait;

    private RequestFactoryInterface $factory;
    private ObjectValidator $validator;
    private CountryService $countries;
    private CountrySeeker $seeker;

    public function __construct(RequestFactoryInterface $factory, ObjectValidator $validator, CountryService $countries, CountrySeeker $seeker)
    {
        $this->factory = $factory;
        $this->validator = $validator;
        $this->countries = $countries;
        $this->seeker = $seeker;
    }

    /**
     * @param string $iso
     * @return array
     * @throws \Skipper\Exceptions\SymfonyValidationException
     * @throws \Skipper\Repository\Exceptions\StorageException
     * @OA\Post(
     *     path="/api/v1/countries/{iso}",
     *     description="manage country",
     *     tags={"Settings"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(name="iso", description="iso code", in="path", required=true, @OA\Schema(type="string")),
     *     @OA\RequestBody(@OA\JsonContent(properties={
     *          @OA\Property(property="is_accessible", type="boolean", description="is available for access")
     *     })),
     *     @OA\Response(response="200", description="updated country", @OA\JsonContent(ref="#/components/schemas/country"))
     * )
     */
    public function addCountry(string $iso): array
    {
        $request = $this->factory->createCountryRequest($iso);
        $this->validator->validate($request);

        return $this->countries->manageCountry($request)->jsonSerialize();
    }

    /**
     * @return array
     * @OA\Get(
     *     path="/api/v1/countries",
     *     tags={"Settings"},
     *     description="search for countries",
     *     security={{"oauth": {}}},
     *     @OA\Parameter(in="query", name="iso", required=false, allowEmptyValue=false, description="country's iso", @OA\Schema(type="string")),
     *     @OA\Response(response="200", description="search result", @OA\JsonContent(properties={
     *          @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/country")),
     *          @OA\Property(property="meta", ref="#/components/schemas/meta")
     *     }))
     * )
     */
    public function searchCountries(): array
    {
        return $this->search($this->seeker, $this->factory);
    }
}
