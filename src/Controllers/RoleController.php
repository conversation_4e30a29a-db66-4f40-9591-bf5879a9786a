<?php
namespace WhiteLabelAdmin\Controllers;

use Skipper\Exceptions\SymfonyValidationException;
use Skipper\Repository\Exceptions\EntityNotFoundException;
use Skipper\Repository\Exceptions\StorageException;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Services\ObjectValidator;
use WhiteLabelAdmin\Services\RoleService;
use WhiteLabelAdmin\Services\Search\RoleSeeker;
use OpenApi\Annotations as OA;

final class RoleController
{
    use SearchTrait;

    /**
     * @var RequestFactoryInterface
     */
    private RequestFactoryInterface $factory;

    /**
     * @var ObjectValidator
     */
    private ObjectValidator $validator;

    /**
     * @var RoleService
     */
    private RoleService $roles;

    /**
     * @var RoleSeeker
     */
    private RoleSeeker $seeker;

    public function __construct(
        RequestFactoryInterface $factory,
        ObjectValidator $validator,
        RoleService $roles,
        RoleSeeker $seeker
    ) {
        $this->factory = $factory;
        $this->validator = $validator;
        $this->roles = $roles;
        $this->seeker = $seeker;
    }

    /**
     * @return array
     * @throws SymfonyValidationException
     * @throws StorageException
     * @OA\Post(
     *     path="/api/v1/roles",
     *     description="creates role",
     *     tags={"Role"},
     *     security={{"oauth": {}}},
     *     @OA\RequestBody(@OA\JsonContent(properties={
     *          @OA\Property(property="name", description="role name, should be unique", type="string"),
     *          @OA\Property(property="permissions", description="role permissions", type="array", @OA\Items(type="string", description="single permissions name")),
     *     })),
     *     @OA\Response(response="200", description="created role", @OA\JsonContent(ref="#/components/schemas/role"))
     * )
     */
    public function createRole(): array
    {
        $request = $this->factory->createRoleRequest(null);
        $this->validator->validate($request, ['create']);
        $role = $this->roles->createRole($request);

        return $role->jsonSerialize();
    }

    /**
     * @param int $roleId
     * @return array
     * @throws SymfonyValidationException
     * @throws EntityNotFoundException
     * @throws StorageException
     * @throws UnauthorizedException
     * @OA\Put(
     *     path="/api/v1/roles/{id}",
     *     description="update role",
     *     tags={"Role"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/id"),
     *     @OA\RequestBody(@OA\JsonContent(required={"permissions"}, properties={
     *          @OA\Property(property="permissions", description="new role permissions", type="array", @OA\Items(type="string", description="single permissions name")),
     *          @OA\Property(property="name", description="new role name", type="string")
     *     })),
     *     @OA\Response(response="200", description="updated role", @OA\JsonContent(ref="#/components/schemas/role"))
     * )
     */
    public function updateRole(int $roleId): array
    {
        $request = $this->factory->createRoleRequest($roleId);
        $this->validator->validate($request, ['update']);
        $role = $this->roles->updatePermissions($request);

        return $role->jsonSerialize();
    }

    /**
     * @param int $roleId
     * @return array
     * @throws SymfonyValidationException
     * @throws EntityNotFoundException
     * @throws StorageException
     * @throws UnauthorizedException
     * @OA\Delete(
     *     path="/api/v1/roles/{id}",
     *     description="delete role",
     *     tags={"Role"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/id"),
     *     @OA\Response(response="200", description="deleted role", @OA\JsonContent(ref="#/components/schemas/role"))
     * )
     */
    public function deleteRole(int $roleId): array
    {
        $request = $this->factory->createRoleRequest($roleId);
        $this->validator->validate($request, ['delete']);
        $role = $this->roles->deleteRole($request);

        return $role->jsonSerialize();
    }

    /**
     * @return array
     * @OA\Get(
     *     path="/api/v1/roles",
     *     description="search for roles",
     *     tags={"Role"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/page_param"),
     *     @OA\Parameter(ref="#/components/parameters/limit_param"),
     *     @OA\Parameter(ref="#/components/parameters/all_param"),
     *     @OA\Parameter(ref="#/components/parameters/id_param"),
     *     @OA\Parameter(ref="#/components/parameters/ids_param"),
     *     @OA\Parameter(ref="#/components/parameters/desc_param"),
     *     @OA\Parameter(ref="#/components/parameters/asc_param"),
     *     @OA\Parameter(in="query", name="name", required=false, allowEmptyValue=false, description="role name", @OA\Schema(type="string")),
     *     @OA\Parameter(in="query", name="has", required=false, allowEmptyValue=false, description="role has permissions", @OA\Schema(type="array", @OA\Items(type="string")), style="form", explode=false),
     *     @OA\Response(response="200", description="search result", @OA\JsonContent(properties={
     *          @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/role")),
     *          @OA\Property(property="meta", ref="#/components/schemas/meta")
     *     }))
     * )
     */
    public function searchRoles(): array
    {
        return $this->search($this->seeker, $this->factory);
    }
}
