<?php
namespace WhiteLabelAdmin\Controllers;

use WhiteLabelAdmin\Repositories\RoleRepositoryInterface;
use OpenApi\Annotations as OA;

final class PermissionController
{
    /**
     * @var RoleRepositoryInterface
     */
    private RoleRepositoryInterface $roles;

    public function __construct(RoleRepositoryInterface $roles)
    {
        $this->roles = $roles;
    }

    /**
     * @return array
     * @OA\Get(
     *     path="/api/v1/roles/permissions",
     *     description="get all permissions",
     *     tags={"Role"},
     *     security={{"oauth": {}}},
     *     @OA\Response(response="200", description="all permissions", @OA\JsonContent(properties={
     *          @OA\Property(property="permissions", description="permission list", type="array", @OA\Items(type="object", properties={
     *              @OA\Property(property="id", description="group id", default="null", type="integer", nullable=true),
     *              @OA\Property(property="name", description="permission name", type="string"),
     *              @OA\Property(property="permissions", description="permissions", properties={
     *                  @OA\Property(property="id", description="permissions id", type="string"),
     *                  @OA\Property(property="name", description="permission name", type="string")
     *              })
     *          }))
     *     }))
     * )
     */
    public function getAllPermissions(): array
    {
        return [
            'permissions' => $this->roles->getAllPermissions(),
        ];
    }
}
