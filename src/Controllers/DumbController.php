<?php
namespace WhiteLabelAdmin\Controllers;

use App\Services\PlayerAppClient;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Services\ObjectValidator;
use OpenApi\Annotations as OA;

final class DumbController
{
    private RequestFactoryInterface $factory;
    private ObjectValidator $validator;
    private PlayerAppClient $players;

    public function __construct(RequestFactoryInterface $factory, ObjectValidator $validator, PlayerAppClient $players)
    {
        $this->factory = $factory;
        $this->validator = $validator;
        $this->players = $players;
    }

    /**
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Skipper\Exceptions\SymfonyValidationException
     * @OA\Post(
     *     path="/api/v1/players/comments",
     *     description="mass comments assign",
     *     tags={"Player"},
     *     security={{"oauth": {}}},
     *     @OA\RequestBody(@OA\JsonContent(properties={
     *          @OA\Property(property="emails", type="array", description="emails to match", @OA\Items(type="string", format="email")),
     *          @OA\Property(property="comment", type="string", maxLength=255, description="new comment")
     *     })),
     *     @OA\Response(response="200", description="players affected count", @OA\JsonContent(properties={
     *          @OA\Property(property="players_affected", type="integer", description="players affected count")
     *     }))
     * )
     */
    public function assignComment(): array
    {
        $request = $this->factory->createPlayerCommentRequest();
        $this->validator->validate($request, ['create']);

        return $this->players->massCommentAssignment($request->getEmails(), $request->getComment());
    }
}
