<?php

namespace WhiteLabelAdmin\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Dto\SearchTemplateSaveDto;
use App\Http\Controllers\Dto\SearchTemplateUpdateDto;
use App\Repositories\SearchTemplateRepository;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

final class SearchTemplateController extends Controller
{
    private SearchTemplateRepository $searchTemplateRepository;

    public function __construct(SearchTemplateRepository $searchTemplateRepository)
    {
        $this->searchTemplateRepository = $searchTemplateRepository;
    }

    /**
     * @param Request $request
     * @return array
     * @throws ValidationException
     */
    public function list(Request $request): array
    {
        $this->validate($request, [
            'type' => ['required', 'string'],
        ]);

        return [
            'templates' => $this->searchTemplateRepository->findTemplatesByType($request->type),
        ];
    }

    /**
     * @throws ValidationException
     */
    public function createTemplate(Request $request): array
    {
        $data = $this->validate($request, [
            'type' => ['required', 'string'],
            'name' => ['required', 'string'],
            'params' => ['required', 'array'],
        ]);

        $searchTemplateSaveDto = new SearchTemplateSaveDto();
        $searchTemplateSaveDto->name = $data['name'];
        $searchTemplateSaveDto->type = $data['type'];
        $searchTemplateSaveDto->params = $data['params'];

        return [
            'success' => $this->searchTemplateRepository->insertNewTemplate($searchTemplateSaveDto),
        ];
    }

    /**
     * @throws ValidationException
     * @throws \JsonException
     */
    public function updateTemplate(int $id, Request $request): array
    {
        $data = $this->validate($request, [
            'type' => ['sometimes', 'string'],
            'name' => ['sometimes', 'string'],
            'params' => ['sometimes', 'array'],
        ]);

        $searchTemplateUpdateDto = new SearchTemplateUpdateDto();
        $searchTemplateUpdateDto->id = $id;
        $searchTemplateUpdateDto->name = $data['name'];
        $searchTemplateUpdateDto->type = $data['type'];
        $searchTemplateUpdateDto->params = $data['params'];

        return [
            'success' => $this->searchTemplateRepository->updateNewTemplate($searchTemplateUpdateDto),
        ];
    }

    public function deleteTemplate(int $id): array
    {
        return [
            'success' => $this->searchTemplateRepository->deleteNewTemplate($id),
        ];
    }

}
