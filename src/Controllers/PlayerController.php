<?php
namespace WhiteLabelAdmin\Controllers;

use Skipper\Exceptions\SymfonyValidationException;
use Skipper\Repository\Exceptions\EntityNotFoundException;
use Skipper\Repository\Exceptions\StorageException;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Services\ObjectValidator;
use WhiteLabelAdmin\Services\PlayerService;
use WhiteLabelAdmin\Services\Search\PlayerSeeker;
use OpenApi\Annotations as OA;

final class PlayerController
{
    use SearchTrait;

    /**
     * @var RequestFactoryInterface
     */
    private RequestFactoryInterface $factory;

    /**
     * @var ObjectValidator
     */
    private ObjectValidator $validator;

    /**
     * @var PlayerService
     */
    private PlayerService $players;

    /**
     * @var PlayerSeeker
     */
    private PlayerSeeker $seeker;

    public function __construct(
        RequestFactoryInterface $factory,
        ObjectValidator $validator,
        PlayerService $players,
        PlayerSeeker $seeker
    ) {
        $this->factory = $factory;
        $this->validator = $validator;
        $this->players = $players;
        $this->seeker = $seeker;
    }

    /**
     * @param int $playerId
     * @return array
     * @throws SymfonyValidationException
     * @throws EntityNotFoundException
     * @throws StorageException
     * @throws UnauthorizedException
     * @OA\Put(
     *     path="/api/v1/players/{id}",
     *     tags={"Player"},
     *     description="updates player's info",
     *     @OA\Parameter(ref="#/components/parameters/id"),
     *     security={{"oauth": {}}},
     *     @OA\RequestBody(@OA\JsonContent(properties={
     *          @OA\Property(property="fake", description="is player fake", type="boolean"),
     *          @OA\Property(property="blocked", description="is player blocked", type="boolean"),
     *          @OA\Property(property="casino", description="is player has access to casino", type="boolean"),
     *          @OA\Property(property="suspect", description="is player suspicious", type="boolean"),
     *          @OA\Property(property="first_name", description="player first name", type="string"),
     *          @OA\Property(property="last_name", description="player last name", type="string"),
     *          @OA\Property(property="birth", description="player birthday", type="string", format="date"),
     *          @OA\Property(property="email", description="player email", type="string", format="email"),
     *          @OA\Property(property="phone", description="player phone", type="string"),
     *          @OA\Property(property="phone_code", description="phone code", type="string"),
     *          @OA\Property(property="comment", description="comment by admin", type="string"),
     *          @OA\Property(property="wager", description="wager balance in cents", type="integer"),
     *          @OA\Property(property="withdraw", description="withdraw limit in cents", type="integer")
     *     })),
     *     @OA\Response(response="200", description="updated player", @OA\JsonContent(ref="#/components/schemas/player"))
     * )
     */
    public function updatePlayer(int $playerId): array
    {
        $request = $this->factory->createPlayerRequest($playerId);
        $this->validator->validate($request, ['update']);
        $player = $this->players->updatePlayer($request);

        return $player->jsonSerialize();
    }

    /**
     * @param int $playerId
     * @return array
     * @throws EntityNotFoundException
     * @throws SymfonyValidationException
     * @OA\Get(
     *     path="/api/v1/players/{id}",
     *     tags={"Player"},
     *     description="get player's info",
     *     @OA\Parameter(ref="#/components/parameters/id"),
     *     security={{"oauth": {}}},
     *     @OA\Response(response="200", description="found player", @OA\JsonContent(ref="#/components/schemas/player"))
     * )
     */
    public function getPlayer(int $playerId): array
    {
        $request = $this->factory->createPlayerRequest($playerId);
        $this->validator->validate($request, ['get']);
        $player = $this->players->getPlayerById($request->getId());

        return $player->jsonSerialize();
    }

    /**
     * @return array
     * @OA\Get(
     *     path="/api/v1/players/search",
     *     tags={"Player"},
     *     description="search players",
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/page_param"),
     *     @OA\Parameter(ref="#/components/parameters/limit_param"),
     *     @OA\Parameter(ref="#/components/parameters/all_param"),
     *     @OA\Parameter(ref="#/components/parameters/id_param"),
     *     @OA\Parameter(ref="#/components/parameters/ids_param"),
     *     @OA\Parameter(ref="#/components/parameters/desc_param"),
     *     @OA\Parameter(ref="#/components/parameters/asc_param"),
     *     @OA\Parameter(in="query", name="email", required=false, allowEmptyValue=false, description="player's email", @OA\Schema(type="string", format="email")),
     *     @OA\Parameter(in="query", name="ip", required=false, allowEmptyValue=false, description="player's last seen ip", @OA\Schema(type="string", format="ipv4")),
     *     @OA\Parameter(in="query", name="blocked", required=false, allowEmptyValue=false, description="player's blockage status", @OA\Schema(type="string", enum={"1", "0"})),
     *     @OA\Parameter(in="query", name="with", required=false, allowEmptyValue=false, description="include entities", @OA\Schema(type="string", enum={"balances"})),
     *     @OA\Response(response="200", description="search result", @OA\JsonContent(properties={
     *          @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/player")),
     *          @OA\Property(property="meta", ref="#/components/schemas/meta")
     *     }))
     * )
     */
    public function searchPlayers(): array
    {
        return $this->search($this->seeker, $this->factory);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/players",
     *     tags={"Player"},
     *     description="get players",
     *     security={{"oauth": {}}},
     *     @OA\Response(response="200", description="search result", @OA\JsonContent(properties={
     *          @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/player")),
     *          @OA\Property(property="meta", ref="#/components/schemas/meta")
     *     }))
     * )
     */
}
