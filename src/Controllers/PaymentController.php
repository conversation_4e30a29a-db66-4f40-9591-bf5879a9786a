<?php
namespace WhiteLabelAdmin\Controllers;

use App\Services\PlayerAppClient;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use OpenApi\Annotations as OA;

final class PaymentController
{
    private PlayerAppClient $client;
    private RequestFactoryInterface $factory;

    public function __construct(PlayerAppClient $client, RequestFactoryInterface $factory)
    {
        $this->client = $client;
        $this->factory = $factory;
    }

    /**
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @OA\Get(
     *     path="/api/v1/payments",
     *     description="search for payments",
     *     tags={"Payments"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(ref="#/components/parameters/page_param"),
     *     @OA\Parameter(ref="#/components/parameters/limit_param"),
     *     @OA\Parameter(ref="#/components/parameters/all_param"),
     *     @OA\Parameter(ref="#/components/parameters/id_param"),
     *     @OA\Parameter(ref="#/components/parameters/ids_param"),
     *     @OA\Parameter(ref="#/components/parameters/desc_param"),
     *     @OA\Parameter(ref="#/components/parameters/asc_param"),
     *     @OA\Parameter(in="query", allowEmptyValue=false, name="currency", required=false, description="payment currency", @OA\Schema(type="string", minLength=3, maxLength=3)),
     *     @OA\Parameter(in="query", allowEmptyValue=false, name="player", required=false, description="player id", @OA\Schema(type="integer")),
     *     @OA\Parameter(in="query", allowEmptyValue=false, name="status", required=false, description="payment status", @OA\Schema(type="string", enum={"approved", "pending", "declined"})),
     *     @OA\Parameter(in="query", allowEmptyValue=false, name="direction", required=false, description="payment direction", @OA\Schema(type="string", enum={"none", "withdraw", "deposit"})),
     *     @OA\Response(response="200", description="search result", @OA\JsonContent(properties={
     *          @OA\Property(property="data", type="array", @OA\Items(type="string", enum={"see in api"})),
     *          @OA\Property(property="meta", ref="#/components/schemas/meta")
     *     }))
     * )
     */
    public function getPayments(): array
    {
        $request = $this->factory->createSearchRequest();

        return $this->client->searchPayments($request->getRequest());
    }
}
