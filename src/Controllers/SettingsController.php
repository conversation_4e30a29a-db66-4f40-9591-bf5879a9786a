<?php
namespace WhiteLabelAdmin\Controllers;

use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Services\ObjectValidator;
use WhiteLabelAdmin\Services\SettingsService;

final class SettingsController
{
    /**
     * @var SettingsService
     */
    private SettingsService $settings;

    /**
     * @var RequestFactoryInterface
     */
    private RequestFactoryInterface $factory;

    /**
     * @var ObjectValidator
     */
    private ObjectValidator $validator;

    public function __construct(SettingsService $settings, RequestFactoryInterface $factory, ObjectValidator $validator)
    {
        $this->settings = $settings;
        $this->factory = $factory;
        $this->validator = $validator;
    }

    /**
     * @return array
     * @throws \Skipper\Exceptions\SymfonyValidationException
     * @throws \Skipper\Repository\Exceptions\EntityNotFoundException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function manageSettings(): array
    {
        $request = $this->factory->createSettingsRequest();
        $this->validator->validate($request);

        $settings = $this->settings->manageSettings($request);

        return $settings->jsonSerialize();
    }
}
