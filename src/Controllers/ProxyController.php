<?php
namespace WhiteLabelAdmin\Controllers;

use GuzzleHttp\Exception\GuzzleException;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Services\ProxyService;

final class ProxyController
{
    private RequestFactoryInterface $factory;
    private ProxyService $proxy;

    public function __construct(RequestFactoryInterface $factory, ProxyService $proxy)
    {
        $this->factory = $factory;
        $this->proxy = $proxy;
    }

    /**
     * @return array
     * @throws GuzzleException
     */
    public function proxy(): array
    {
        $request = $this->factory->createProxyRequest();
        return $this->proxy->proxy($request);
    }
}
