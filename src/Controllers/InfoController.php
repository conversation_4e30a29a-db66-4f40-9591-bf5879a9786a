<?php
namespace WhiteLabelAdmin\Controllers;

use OpenApi\Annotations as OA;
use function OpenApi\scan;

/**
 * Class InfoController
 * @OA\Info(
 *     title="WhiteLabel Admin",
 *     description="New Admin api",
 *     version="0.0.1",
 * )
 */
final class InfoController
{
    /**
     * @return array
     * @OA\Get(
     *     description="Check health",
     *     path="/info",
     *     tags={"ServiceRoutes"},
     *     @OA\Response(response="200", description="Api info",
     *          @OA\JsonContent(properties={
     *              @OA\Property(property="status", type="string", default="ok"),
     *              @OA\Property(property="name", type="string", default="WhiteLabelApp"),
     *              @OA\Property(property="timestamp", type="integer", format="U"),
     *              @OA\Property(property="time", type="object", properties={
     *                   @OA\Property(property="date", type="string"),
     *              }),
     *              @OA\Property(property="hostname", type="string", description="hostname")
     *          })
     *     )
     * )
     */
    public function getInfoAction(): array
    {
        return [
            'status' => 'ok',
            'time' => new \DateTimeImmutable,
            'timestamp' => time(),
            'name' => 'WhiteLabelAdmin',
            'hostname' => gethostname(),
        ];
    }

    /**
     * @return string
     * @OA\Get(
     *     path="/docs",
     *     description="get open api json docs",
     *     tags={"ServiceRoutes"},
     *     @OA\Response(response="200", description="Api docs")
     * )
     */
    public function getJsonDocsAction(): string
    {
        if (isProduction()) {
            return response('Forbidden', 403);
        }

        $api = scan(realpath(__DIR__ . '/../'));

        return str_replace('\\', '\\\\', $api->toJson());
    }
}
