<?php
namespace WhiteLabelAdmin\Controllers;

use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Services\ObjectValidator;
use WhiteLabelAdmin\Services\PaymentGatewayService;
use OpenApi\Annotations as OA;
use WhiteLabelAdmin\Services\Search\GatewaySeeker;

final class PaymentGatewayController
{
    use SearchTrait;

    /**
     * @var PaymentGatewayService
     */
    private PaymentGatewayService $service;

    /**
     * @var ObjectValidator
     */
    private ObjectValidator $validator;

    /**
     * @var RequestFactoryInterface
     */
    private RequestFactoryInterface $factory;

    /**
     * @var GatewaySeeker
     */
    private GatewaySeeker $seeker;

    public function __construct(PaymentGatewayService $service, ObjectValidator $validator, RequestFactoryInterface $factory, GatewaySeeker $seeker)
    {
        $this->service = $service;
        $this->validator = $validator;
        $this->factory = $factory;
        $this->seeker = $seeker;
    }

    /**
     * @param string $name
     * @return array
     * @throws \Skipper\Exceptions\SymfonyValidationException
     * @throws \Skipper\Repository\Exceptions\StorageException
     * @OA\Post(
     *     path="/api/v1/gateways/{name}",
     *     description="manage payment gateway",
     *     tags={"Payment Gateways"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(name="name", description="gateway name", in="path", required=true, @OA\Schema(type="string")),
     *     @OA\RequestBody(@OA\JsonContent(properties={
     *          @OA\Property(property="deposit", type="boolean", nullable=true),
     *          @OA\Property(property="payout", type="boolean", nullable=true)
     *     })),
     *     @OA\Response(response="200", description="updated gateway", @OA\JsonContent(ref="#/components/schemas/gateway"))
     * )
     */
    public function manage(string $name): array
    {
        $request = $this->factory->createGatewayRequest($name);
        $this->validator->validate($request);

        return $this->service->manage($request)->jsonSerialize();
    }

    /**
     * @return array
     * @OA\Get(
     *     path="/api/v1/gateways",
     *     tags={"Payment Gateways"},
     *     description="search for payment gateways",
     *     security={{"oauth": {}}},
     *     @OA\Parameter(in="query", name="name", required=false, allowEmptyValue=false, description="gateway name", @OA\Schema(type="string")),
     *     @OA\Parameter(in="query", name="deposit", required=false, allowEmptyValue=false, description="is deposit available", @OA\Schema(enum={"1", "0"})),
     *     @OA\Parameter(in="query", name="payout", required=false, allowEmptyValue=false, description="is payout available", @OA\Schema(enum={"1", "0"})),
     *     @OA\Response(response="200", description="search result", @OA\JsonContent(properties={
     *          @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/gateway")),
     *          @OA\Property(property="meta", ref="#/components/schemas/meta")
     *     }))
     * )
     */
    public function searchAction(): array
    {
        return $this->search($this->seeker, $this->factory);
    }
}
