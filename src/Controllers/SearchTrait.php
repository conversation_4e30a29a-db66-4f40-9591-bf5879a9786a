<?php
namespace WhiteLabelAdmin\Controllers;

use Skipper\Search\Services\AbstractSeeker;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;

trait SearchTrait
{
    /**
     * @param AbstractSeeker $seeker
     * @param RequestFactoryInterface $factory
     * @return array
     */
    private function search(AbstractSeeker $seeker, RequestFactoryInterface $factory): array
    {
        $request = $factory->createSearchRequest();
        $searchResult = $seeker->seek(array_merge($request->getRequest(), [
            'client' => $request->getClientId(),
        ]));

        return [
            'data' => $searchResult->getData(),
            'meta' => $searchResult->getMeta(),
        ];
    }
}
