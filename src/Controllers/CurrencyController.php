<?php
namespace WhiteLabelAdmin\Controllers;

use Skipper\Exceptions\SymfonyValidationException;
use Skipper\Repository\Exceptions\StorageException;
use WhiteLabelAdmin\Factories\RequestFactoryInterface;
use WhiteLabelAdmin\Services\CurrencyService;
use WhiteLabelAdmin\Services\ObjectValidator;
use WhiteLabelAdmin\Services\Search\CurrencySeeker;
use OpenApi\Annotations as OA;

final class CurrencyController
{
    use SearchTrait;

    /**
     * @var CurrencyService
     */
    private CurrencyService $currencies;

    /**
     * @var RequestFactoryInterface
     */
    private RequestFactoryInterface $factory;

    /**
     * @var ObjectValidator
     */
    private ObjectValidator $validator;

    /**
     * @var CurrencySeeker
     */
    private CurrencySeeker $seeker;

    public function __construct(
        CurrencyService $currencies,
        RequestFactoryInterface $factory,
        ObjectValidator $validator,
        CurrencySeeker $seeker
    ) {
        $this->currencies = $currencies;
        $this->factory = $factory;
        $this->validator = $validator;
        $this->seeker = $seeker;
    }

    /**
     * @param string $iso
     * @return array
     * @throws SymfonyValidationException
     * @throws StorageException
     * @OA\Post(
     *     path="/api/v1/currencies/{iso}",
     *     description="manage currency",
     *     tags={"Settings"},
     *     security={{"oauth": {}}},
     *     @OA\Parameter(name="iso", description="iso code", in="path", required=true, @OA\Schema(type="string")),
     *     @OA\RequestBody(@OA\JsonContent(properties={
     *          @OA\Property(property="min_bet", type="integer", description="min bet"),
     *          @OA\Property(property="is_available", type="boolean", description="is available"),
     *          @OA\Property(property="max_win", type="integer", description="max win")
     *     })),
     *     @OA\Response(response="200", description="updated currency", @OA\JsonContent(ref="#/components/schemas/currency"))
     * )
     */
    public function updateCurrency(string $iso): array
    {
        $request = $this->factory->createCurrencyRequest($iso);
        $this->validator->validate($request);

        return $this->currencies->manageCurrency($request)->jsonSerialize();
    }

    /**
     * @return array
     * @OA\Get(
     *     path="/api/v1/currencies",
     *     tags={"Settings"},
     *     description="search for currency",
     *     security={{"oauth": {}}},
     *     @OA\Parameter(in="query", name="iso", required=false, allowEmptyValue=false, description="country's iso", @OA\Schema(type="string", maxLength=3, minLength=3)),
     *     @OA\Response(response="200", description="search result", @OA\JsonContent(properties={
     *          @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/currency")),
     *          @OA\Property(property="meta", ref="#/components/schemas/meta")
     *     }))
     * )
     */
    public function searchCurrencies(): array
    {
        return $this->search($this->seeker, $this->factory);
    }
}
