<?php
namespace WhiteLabelAdmin\Tests\Services;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use WhiteLabelAdmin\Entities\Player;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;
use WhiteLabelAdmin\Repositories\PlayerRepositoryInterface;
use WhiteLabelAdmin\Requests\PlayerRequest;
use WhiteLabelAdmin\Services\PlayerService;

final class PlayerServiceTest extends TestCase
{
    private PlayerService $service;

    /**
     * @var PlayerRepositoryInterface|MockObject
     */
    private $players;

    protected function setUp(): void
    {
        parent::setUp();

        $this->players = $this->createMock(PlayerRepositoryInterface::class);
        $this->service = new PlayerService($this->players);
    }

    public function testUnauthorized(): void
    {
        $request = $this->createMock(PlayerRequest::class);
        $request->method('getId')->willReturn(1);
        $request->method('getClientId')->willReturn(123);
        $this->expectException(UnauthorizedException::class);

        $player = $this->createMock(Player::class);
        $this->players->expects(self::once())
            ->method('find')
            ->with(1)
            ->willReturn($player);
        $player->method('getClientId')->willReturn(122);

        $this->service->updatePlayer($request);
    }
}
