<?php
namespace WhiteLabelAdmin\Tests\Services;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Skipper\Repository\Exceptions\EntityNotFoundException;
use WhiteLabelAdmin\Entities\SystemCurrency;
use WhiteLabelAdmin\Repositories\CurrencyRepositoryInterface;
use WhiteLabelAdmin\Requests\CurrencyRequest;
use WhiteLabelAdmin\Services\CurrencyInfoService;
use WhiteLabelAdmin\Services\CurrencyService;

final class CurrencyServiceTest extends TestCase
{
    private CurrencyService $service;

    /**
     * @var CurrencyRepositoryInterface|MockObject
     */
    private $currencies;

    protected function setUp(): void
    {
        parent::setUp();

        $this->currencies = $this->createMock(CurrencyRepositoryInterface::class);
        $this->service = new CurrencyService($this->currencies, new CurrencyInfoService);
    }

    public function testNotCreate(): void
    {
        $this->currencies->expects(self::once())
            ->method('findByIsoCodeAndClientId')
            ->with('USD', 123)
            ->willThrowException(new EntityNotFoundException('currency'));
        $this->currencies->expects(self::once())
            ->method('save');

        $request = $this->createMock(CurrencyRequest::class);
        $request->method('getIsoCode')->willReturn('USD');
        $request->method('getClientId')->willReturn(123);
        $currency = $this->service->manageCurrency($request);

        self::assertInstanceOf(SystemCurrency::class, $currency);
        self::assertEquals('USD', $currency->getIsoCode());
        self::assertEquals('US Dollar', $currency->getName());
        self::assertEquals(123, $currency->getClientId());
    }
}
