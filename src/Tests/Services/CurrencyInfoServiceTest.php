<?php
namespace WhiteLabelAdmin\Tests\Services;

use Money\Currency;
use PHPUnit\Framework\TestCase;
use WhiteLabelAdmin\Services\CurrencyInfoService;

final class CurrencyInfoServiceTest extends TestCase
{
    private CurrencyInfoService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new CurrencyInfoService;
    }


    public function testName(): void
    {
        self::assertEquals('US Dollar', $this->service->getName(new Currency('USD')));
        self::assertEquals('Unknown currency', $this->service->getName(new Currency('LOL')));
    }
}
