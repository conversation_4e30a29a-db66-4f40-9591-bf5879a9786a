<?php
namespace WhiteLabelAdmin\Tests\Entities;

use PHPUnit\Framework\TestCase;
use WhiteLabelAdmin\Entities\Player;

final class PlayerTest extends TestCase
{
    public function testGeters(): void
    {
        $player = new Player([
            'client_id' => 123,
            'id' => 1,
        ]);

        self::assertEquals(123, $player->getClientId());
        self::assertEquals(1, $player->getId());
        self::assertEquals([], $player->getUpdated());
        self::assertEquals([
            'client_id' => 123,
            'id' => 1,
        ], $player->getData());

        $player->setNewData([
            'lol' => '123',
        ]);
        self::assertEquals([
            'lol' => '123',
        ], $player->getData());
    }
}
