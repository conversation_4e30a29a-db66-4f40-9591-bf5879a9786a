<?php
namespace WhiteLabelAdmin\Repositories;

use Skipper\Repository\Contracts\Repository;
use Skipper\Repository\Exceptions\EntityNotFoundException;
use WhiteLabelAdmin\Entities\SystemCurrency;

interface CurrencyRepositoryInterface extends Repository
{
    /**
     * @param string $iso
     * @param int $clientId
     * @return SystemCurrency
     * @throws EntityNotFoundException
     */
    public function findByIsoCodeAndClientId(string $iso, int $clientId): SystemCurrency;
}
