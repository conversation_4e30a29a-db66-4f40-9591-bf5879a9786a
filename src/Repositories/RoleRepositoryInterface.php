<?php
namespace WhiteLabelAdmin\Repositories;

use <PERSON><PERSON>\Repository\Contracts\Repository;
use WhiteLabelAdmin\Entities\Role;

interface RoleRepositoryInterface extends Repository
{
    /**
     * @param Role $role
     * @param string[] $permissions
     * @return Role
     */
    public function updateRolePermissions(Role $role, array $permissions): Role;

    /**
     * @return string[]
     */
    public function getAllPermissions(): array;
}
