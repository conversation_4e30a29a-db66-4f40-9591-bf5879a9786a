<?php
namespace WhiteLabelAdmin\Repositories;

use Skipper\Repository\Contracts\Repository;
use Skipper\Repository\Exceptions\EntityNotFoundException;
use WhiteLabelAdmin\Entities\PaymentGateway;

interface PaymentGatewayRepositoryInterface extends Repository
{
    /**
     * @param int $clientId
     * @param string $name
     * @return PaymentGateway
     * @throws EntityNotFoundException
     */
    public function getByClientIdAndName(int $clientId, string $name): PaymentGateway;

    /**
     * @return array
     */
    public function getAvailableGateways(): array;
}
