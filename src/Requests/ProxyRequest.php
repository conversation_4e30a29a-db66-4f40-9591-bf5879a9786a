<?php
namespace WhiteLabelAdmin\Requests;

use WhiteLabelAdmin\Utilities\HasValidId;

final class ProxyRequest
{
    use HasValidId;

    /**
     * @var string
     */
    private $method;

    /**
     * @var string
     */
    private $uri;

    /**
     * @var array
     */
    private $query;

    /**
     * @var array
     */
    private $body;

    public function __construct($clientId, $method, $uri, $query, $body)
    {
        $this->method = $method;
        $this->uri = $uri;
        $this->query = $query;
        $this->body = $body;
        $this->clientId = $clientId;
    }

    /**
     * @return string
     */
    public function getMethod(): string
    {
        return $this->method;
    }

    /**
     * @return string
     */
    public function getUri(): string
    {
        return $this->uri;
    }

    /**
     * @return array
     */
    public function getQuery(): array
    {
        return $this->query;
    }

    /**
     * @return array
     */
    public function getBody(): array
    {
        return $this->body;
    }
}
