<?php
namespace WhiteLabelAdmin\Requests;

final class SearchRequest
{
    private $request;
    private $clientId;

    public function __construct($request, $clientId)
    {
        $this->request = $request;
        $this->clientId = $clientId;
    }

    /**
     * @return int
     */
    public function getClientId()
    {
        return $this->clientId;
    }

    /**
     * @return array
     */
    public function getRequest()
    {
        return $this->request;
    }
}
