<?php
namespace WhiteLabelAdmin\Requests;

use App\Core\Validator\ValidationService;
use WhiteLabelAdmin\Utilities\HasValidId;
use Symfony\Component\Validator\Constraints as Assert;

final class PlayerCommentRequest
{
    use HasValidId;

    /**
     * @var string[]
     * @Assert\NotBlank
     */
    private $emails;

    /**
     * @var string
     * @Assert\NotBlank
     * @Assert\Type(type="string")
     * @Assert\Length(max=255)
     */
    private $comment;

    public function __construct($clientId, $emails, $comment)
    {
        $this->clientId = $clientId;
        $this->emails = $emails;
        $this->comment = $comment;

        $validationService = app(ValidationService::class);
        $validator = $validationService
            ->createValidator(
                [
                    'emails' => $emails,
                ],
                [
                    'emails.*' => 'email|max:255',
                ]
            );

        $validator->validate();
    }

    /**
     * @return string[]
     */
    public function getEmails(): array
    {
        return $this->emails;
    }

    /**
     * @return string
     */
    public function getComment(): string
    {
        return $this->comment;
    }
}
