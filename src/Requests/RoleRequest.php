<?php
namespace WhiteLabelAdmin\Requests;

use Symfony\Component\Validator\Constraints as Assert;
use WhiteLabelAdmin\Utilities\HasValidId;

final class RoleRequest
{
    use HasValidId;

    /**
     * @var string
     * @Assert\NotBlank(groups={"create"})
     * @Assert\Type(type="string")
     * @Assert\Length(max="255")
     */
    private $name;

    /**
     * @var array
     * @Assert\NotBlank(groups={"create", "update"})
     * @Assert\Type(type="array")
     * @Assert\All({
     *     @Assert\Type(type="string"),
     *     @Assert\Length(max="255")
     * })
     */
    private $permissions;

    public function __construct($id, $clientId, $name, $permissions)
    {
        $this->id = $id;
        $this->clientId = $clientId;
        $this->name = $name;
        $this->permissions = $permissions;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @return array
     */
    public function getPermissions(): array
    {
        return $this->permissions;
    }
}
