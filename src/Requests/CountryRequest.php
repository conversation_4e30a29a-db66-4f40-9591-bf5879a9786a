<?php
namespace WhiteLabelAdmin\Requests;

use Symfony\Component\Validator\Constraints as Assert;

final class CountryRequest
{
    /**
     * @var string
     * @Assert\NotBlank
     * @Assert\Type(type="string")
     * @Assert\Length(min=2, max=2)
     */
    private $isoCode;

    /**
     * @var int
     * @Assert\NotBlank
     * @Assert\Type(type="numeric")
     * @Assert\GreaterThan(value=0)
     */
    private $clientId;

    /**
     * @var bool
     * @Assert\NotNull
     * @Assert\Type(type="boolean")
     */
    private $available;

    public function __construct($clientId, $isoCode, $isRegisterAvailable)
    {
        $this->clientId = $clientId;
        $this->isoCode = $isoCode;
        $this->available = $isRegisterAvailable;
    }

    /**
     * @return string
     */
    public function getIsoCode(): string
    {
        return $this->isoCode;
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return bool
     */
    public function isAvailable(): bool
    {
        return $this->available;
    }
}
