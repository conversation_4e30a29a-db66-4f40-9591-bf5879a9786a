<?php
namespace WhiteLabelAdmin\Requests;

use Symfony\Component\Validator\Constraints as Assert;

class GameCategoryRequest
{
    /**
     * @var int
     * @Assert\NotBlank
     * @Assert\Type(type="numeric")
     * @Assert\GreaterThan(value=0)
     */
    private $clientId;

    /**
     * @var string
     * @Assert\NotBlank
     * @Assert\Choice(choices={"casino", "dealers", "sports"})
     */
    private $type;

    /**
     * @var string
     * @Assert\NotBlank
     * @Assert\Type(type="string")
     */
    private $slug;

    /**
     * @var bool|null
     * @Assert\Type(type="boolean")
     */
    private $enabled;

    /**
     * @var string|null
     * @Assert\Type(type="string")
     */
    private $name;

    /**
     * @var int|null
     * @Assert\Type(type="integer")
     */
    private $weight;

    public function __construct($clientId, $type, $slug, $name, $enabled, $weight)
    {
        $this->clientId = $clientId;
        $this->type = $type;
        $this->slug = $slug;
        $this->name = $name;
        $this->enabled = $enabled;
        $this->weight = $weight;
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @return string
     */
    public function getSlug(): string
    {
        return $this->slug;
    }

    /**
     * @return bool|null
     */
    public function getEnabled(): ?bool
    {
        return $this->enabled;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @return int|null
     */
    public function getWeight(): ?int
    {
        return $this->weight;
    }
}
