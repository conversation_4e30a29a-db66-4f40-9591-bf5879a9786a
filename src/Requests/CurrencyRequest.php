<?php
namespace WhiteLabelAdmin\Requests;

use Symfony\Component\Validator\Constraints as Assert;

class CurrencyRequest
{
    /**
     * @var string
     * @Assert\NotBlank
     * @Assert\Currency
     */
    private $isoCode;

    /**
     * @var bool|null
     * @Assert\Type(type="boolean")
     */
    private $isRegisterAvailable;

    /**
     * @var int|null
     * @Assert\Type(type="integer")
     * @Assert\GreaterThan(value="1")
     */
    private $minBet;

    /**
     * @var int|null
     * @Assert\Type(type="integer")
     * @Assert\GreaterThan(value="1")
     * @Assert\LessThanOrEqual(value="100000000")
     */
    private $maxWin;

    /**
     * @var int
     * @Assert\NotBlank()
     * @Assert\Type(type="numeric")
     * @Assert\GreaterThan(value=0)
     */
    private $clientId;

    public function __construct($isoCode, $clientId, $isRegisterAvailable, $minBet, $maxWin)
    {
        $this->isoCode = is_string($isoCode) ? strtoupper($isoCode) : $isoCode;
        $this->clientId = $clientId;
        $this->isRegisterAvailable = $isRegisterAvailable;
        $this->minBet = $minBet;
        $this->maxWin = $maxWin;
    }

    /**
     * @return string
     */
    public function getIsoCode(): string
    {
        return $this->isoCode;
    }

    /**
     * @return bool|null
     */
    public function getIsRegisterAvailable(): ?bool
    {
        return $this->isRegisterAvailable;
    }

    /**
     * @return int|null
     */
    public function getMinBet(): ?int
    {
        return $this->minBet;
    }

    /**
     * @return int|null
     */
    public function getMaxWin(): ?int
    {
        return $this->maxWin;
    }


    /**
     * @return int
     */
    public function getClientId(): int
    {
        return (int)$this->clientId;
    }

}
