<?php
namespace WhiteLabelAdmin\Requests;

use <PERSON>ymfony\Component\Validator\Constraints as Assert;

class ClientSettingsRequest
{
    /**
     * @var int
     * @Assert\NotBlank()
     * @Assert\Type(type="numeric")
     * @Assert\GreaterThan(value=0)
     */
    private $clientId;

    /**
     * @var string|null
     * @Assert\Type(type="string")
     */
    private $name;

    /**
     * @var array|null
     * @Assert\Type(type="array")
     * @Assert\Collection(allowExtraFields=true, allowMissingFields=true, fields={
     *     "facebook"={
     *          @Assert\Url,
     *     },
     *     "instagram"={
     *          @Assert\Url,
     *     },
     *     "telegram"={
     *          @Assert\Url,
     *     },
     *     "youtube"={
     *          @Assert\Url,
     *     },
     *     "ios"={
     *          @Assert\Url,
     *     },
     *     "android"={
     *          @Assert\Url,
     *     },
     * })
     */
    private $socialLinks;

    public function __construct($clientId, $name, $socialLinks)
    {
        $this->clientId = $clientId;
        $this->name = $name;
        $this->socialLinks = $socialLinks;
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @return array|null
     */
    public function getSocialLinks(): ?array
    {
        return $this->socialLinks;
    }
}
