<?php
namespace WhiteLabelAdmin\Requests;

use App\Core\Validator\ValidationService;
use Symfony\Component\Validator\Constraints as Assert;
use WhiteLabelAdmin\Utilities\HasValidId;

class PlayerRequest
{
    use HasValidId;

    /**
     * @var bool|null
     * @Assert\Type(type="bool")
     */
    private $isFake;

    /**
     * @var bool|null
     * @Assert\Type(type="bool")
     */
    private $isSuspicious;

    /**
     * @var string|null
     * @Assert\Type(type="string")
     * @Assert\Length(max=255)
     */
    private $firstName;

    /**
     * @var string|null
     * @Assert\Type(type="string")
     * @Assert\Length(max=255)
     */
    private $lastName;

    /**
     * @var string|null
     * @Assert\Date
     */
    private $birthDate;

    /**
     * @var string|null
     */
    private $email;

    /**
     * @var string|null
     * @Assert\Type(type="string")
     */
    private $phone;

    /**
     * @var string|null
     * @Assert\Type(type="string")
     */
    private $phoneCode;

    /**
     * @var bool|null
     * @Assert\Type(type="bool")
     */
    private $blocked;

    /**
     * @var bool|null
     * @Assert\Type(type="bool")
     */
    private $casinoAccess;

    /**
     * @var int|null
     * @Assert\Type(type="integer")
     * @Assert\GreaterThan(value=0)
     */
    private $withdrawLimit;

    /**
     * @var int|null
     * @Assert\Type(type="integer")
     * @Assert\GreaterThan(value=0)
     */
    private $wager;

    /**
     * @var string|null
     * @Assert\Type(type="string")
     * @Assert\Length(max=255)
     */
    private $comment;

    /**
     * UserRequest constructor.
     * @param bool|null $isFake
     * @param bool|null $isSuspicious
     * @param string|null $firstName
     * @param string|null $lastName
     * @param \DateTimeInterface|null $birthDate
     * @param string|null $email
     * @param string|null $phone
     * @param bool|null $blocked
     * @param bool|null $casinoAccess
     * @param int|null $withdrawLimit
     * @param int|null $wager
     * @param string|null $comment
     */
    public function __construct(
        $id,
        $clientId,
        $isFake,
        $isSuspicious,
        $firstName,
        $lastName,
        $birthDate,
        $email,
        $phone,
        $blocked,
        $casinoAccess,
        $withdrawLimit,
        $wager,
        $comment,
        $phoneCode
    ) {
        $this->id = $id;
        $this->clientId = $clientId;
        $this->isFake = $isFake;
        $this->isSuspicious = $isSuspicious;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->birthDate = $birthDate;
        $this->email = $email;
        $this->phone = $phone;
        $this->blocked = $blocked;
        $this->casinoAccess = $casinoAccess;
        $this->withdrawLimit = $withdrawLimit;
        $this->wager = $wager;
        $this->comment = $comment;
        $this->phoneCode = $phoneCode;

        $validationService = app(ValidationService::class);
        $validator = $validationService
            ->createValidator(
                [
                    'email' => $email,
                ],
                [
                    'email' => 'nullable|email|max:255',
                ]
            );

        $validator->validate();
    }

    /**
     * @return string|null
     */
    public function getPhoneCode(): ?string
    {
        return $this->phoneCode;
    }

    /**
     * @return bool|null
     */
    public function getIsFake(): ?bool
    {
        return $this->isFake;
    }

    /**
     * @return bool|null
     */
    public function getIsSuspicious(): ?bool
    {
        return $this->isSuspicious;
    }

    /**
     * @return string|null
     */
    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    /**
     * @return string|null
     */
    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    /**
     * @return string|null
     */
    public function getBirthDate(): ?string
    {
        return $this->birthDate;
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getPhone(): ?string
    {
        return $this->phone;
    }

    /**
     * @return bool|null
     */
    public function getBlocked(): ?bool
    {
        return $this->blocked;
    }

    /**
     * @return bool|null
     */
    public function getCasinoAccess(): ?bool
    {
        return $this->casinoAccess;
    }

    /**
     * @return int|null
     */
    public function getWithdrawLimit(): ?int
    {
        return $this->withdrawLimit;
    }

    /**
     * @return int|null
     */
    public function getWager(): ?int
    {
        return $this->wager;
    }

    /**
     * @return string|null
     */
    public function getComment(): ?string
    {
        return $this->comment;
    }
}
