<?php
namespace WhiteLabelAdmin\Requests;

use Symfony\Component\Validator\Constraints as Assert;

final class PageRequest
{
    /**
     * @var string
     * @Assert\NotBlank
     * @Assert\Length(max="255")
     */
    private $slug;

    /**
     * @var int
     * @Assert\NotBlank
     * @Assert\Type(type="numeric")
     * @Assert\GreaterThan(value=0)
     */
    private $clientId;

    /**
     * @var string
     * @Assert\Type(type="string")
     */
    private $title;

    /**
     * @var string
     * @Assert\NotBlank
     */
    private $content;

    /**
     * @var string[]
     * @Assert\NotBlank
     * @Assert\Type(type="array")
     */
    private $meta;

    public function __construct($slug, $title, $content, $meta, $clientId)
    {
        $this->slug = $slug;
        $this->content = $content;
        $this->meta = $meta;
        $this->clientId = $clientId;
        $this->title = $title;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return string
     */
    public function getSlug(): string
    {
        return $this->slug;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @return string[]
     */
    public function getMeta(): array
    {
        return $this->meta;
    }
}
