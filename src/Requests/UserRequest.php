<?php
namespace WhiteLabelAdmin\Requests;

use App\Core\Validator\ValidationService;
use Symfony\Component\Validator\Constraints as Assert;
use WhiteLabelAdmin\Utilities\HasValidId;

final class UserRequest
{
    use HasValidId;

    /**
     * @var string|null
     * @Assert\NotBlank(groups={"create"})
     * @Assert\Type(type="string")
     * @Assert\Length(max=40)
     */
    private $login;

    /**
     * @var string|null
     * @Assert\NotBlank(groups={"create"})
     */
    private $email;

    /**
     * @var string|null
     * @Assert\NotBlank(groups={"create", "change_password"})
     * @Assert\Type(type="string")
     * @Assert\Length(min=6)
     */
    private $password;

    /**
     * @var string|null
     */
    private $repeatPassword;

//    /**
//     * @var string|null
//     * @Assert\NotBlank(groups={"change_password"})
//     * @Assert\Type(type="string")
//     */
//    private $oldPassword;

    /**
     * @var int|null
     * @Assert\NotBlank(groups={"update"})
     * @Assert\Type(type="integer")
     * @Assert\GreaterThan(value=0)
     */
    private $roleId;

    public function __construct($id, $clientId, $login, $password, $email, $repeatPassword, $roleId)
    {
        $this->id = $id;
        $this->clientId = $clientId;
        $this->login = $login;
        $this->password = $password;
        $this->email = $email;
        $this->repeatPassword = $repeatPassword;
//        $this->oldPassword = $oldPassword;
        $this->roleId = $roleId;

        $validationService = app(ValidationService::class);
        $validator = $validationService
            ->createValidator(
                [
                    'email' => $email,
                ],
                [
                    'email' => 'nullable|email|max:255',
                ]
            );

        $validator->validate();
    }

    /**
     * @return bool
     * @Assert\IsTrue(groups={"create", "change_password"})
     */
    public function isPasswordsSame(): bool
    {
        return $this->repeatPassword === $this->password;
    }

    /**
     * @return string|null
     */
    public function getLogin(): ?string
    {
        return $this->login;
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

//    /**
//     * @return string|null
//     */
//    public function getOldPassword(): ?string
//    {
//        return $this->oldPassword;
//    }

    /**
     * @return int|null
     */
    public function getRoleId(): ?int
    {
        return $this->roleId;
    }
}
