<?php
namespace WhiteLabelAdmin\Requests;

use Symfony\Component\Validator\Constraints as Assert;

final class PaymentGatewayRequest
{
    /**
     * @var int
     * @Assert\NotBlank
     * @Assert\Type(type="numeric")
     * @Assert\GreaterThan(value=0)
     */
    private $clientId;

    /**
     * @var string
     * @Assert\NotBlank
     * @Assert\Type(type="string")
     */
    private $name;

    /**
     * @var bool|null
     * @Assert\Type(type="boolean")
     */
    private $isDeposit;

    /**
     * @var bool|null
     * @Assert\Type(type="boolean")
     */
    private $isPayout;

    public function __construct($clientId, $name, $isDeposit, $isPayout)
    {
        $this->clientId = $clientId;
        $this->name = $name;
        $this->isDeposit = $isDeposit;
        $this->isPayout = $isPayout;
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return (int)$this->clientId;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return bool|null
     */
    public function getIsDeposit(): ?bool
    {
        return $this->isDeposit;
    }

    /**
     * @return bool|null
     */
    public function getIsPayout(): ?bool
    {
        return $this->isPayout;
    }
}
