<?php
namespace WhiteLabelAdmin\Services;

use Skipper\Exceptions\InvalidArgumentException;
use White<PERSON>abelAdmin\Entities\Player;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;
use WhiteLabelAdmin\Repositories\PlayerRepositoryInterface;
use WhiteLabelAdmin\Requests\PlayerRequest;

final class PlayerService
{
    /**
     * @var PlayerRepositoryInterface
     */
    private PlayerRepositoryInterface $players;

    public function __construct(PlayerRepositoryInterface $players)
    {
        $this->players = $players;
    }

    /**
     * @param int $playerId
     * @return Player
     * @throws \Skipper\Repository\Exceptions\EntityNotFoundException
     */
    public function getPlayerById(int $playerId): Player
    {
        return $this->players->find($playerId);
    }

    /**
     * @param PlayerRequest $request
     * @return Player
     * @throws UnauthorizedException
     * @throws \Skipper\Repository\Exceptions\EntityNotFoundException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function updatePlayer(PlayerRequest $request): Player
    {
        $player = $this->getPlayerById($request->getId());
        if ($player->getClientId() !== $request->getClientId()) {
            throw new UnauthorizedException(new InvalidArgumentException('clientId', $request->getClientId()));
        }
        $toUpdate = [];
        if (null !== $request->getEmail()) {
            $toUpdate['email'] = $request->getEmail();
        }

        if (null !== $request->getPhone()) {
            $toUpdate['phone'] = $request->getPhone();
        }

        if (null !== $request->getPhoneCode()) {
            $toUpdate['phone_code'] = $request->getPhoneCode();
        }

        if (null !== $request->getComment()) {
            $toUpdate['comment'] = $request->getComment();
        }

        if (null !== $request->getIsSuspicious()) {
            $toUpdate['is_suspicious'] = $request->getIsSuspicious();
        }

        if (null !== $request->getIsFake()) {
            $toUpdate['is_fake'] = $request->getIsFake();
        }

        if (null !== $request->getFirstName()) {
            $toUpdate['first_name'] = $request->getFirstName();
        }

        if (null !== $request->getWager()) {
            $toUpdate['wager'] = $request->getWager();
        }

        if (null !== $request->getLastName()) {
            $toUpdate['last_name'] = $request->getLastName();
        }

        if (null !== $request->getBirthDate()) {
            $toUpdate['birth'] = $request->getBirthDate();
        }

        if (null !== $request->getCasinoAccess()) {
            $toUpdate['casino_access'] = $request->getCasinoAccess();
        }

        if (null !== $request->getBlocked()) {
            $toUpdate['blocked'] = $request->getBlocked();
        }

        if (null !== $request->getWithdrawLimit()) {
            $toUpdate['withdraw'] = $request->getWithdrawLimit();
        }

        $player->setUpdated($toUpdate);
        $this->players->save($player);

        return $player;
    }
}
