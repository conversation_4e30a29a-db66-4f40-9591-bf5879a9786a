<?php
namespace WhiteLabelAdmin\Services\Search;

use Skipper\Search\Contracts\FilterInterface;
use Skipper\Search\Contracts\MetaGeneratorInterface;
use Skipper\Search\Contracts\ValidatorInterface;
use Skipper\Search\Filters\CompoundFilter;
use Skipper\Search\Filters\LimitedStringFilter;
use Skipper\Search\Filters\PositiveIntegerFilter;
use Skipper\Search\Services\AbstractSeeker;
use WhiteLabelAdmin\Repositories\RoleRepositoryInterface;

final class RoleSeeker extends AbstractSeeker
{
    public const DEFAULT_LIMIT = 100;

    public function __construct(
        RoleRepositoryInterface $driver,
        ValidatorInterface $validator,
        MetaGeneratorInterface $metaGenerator
    ) {
        parent::__construct($driver, $validator, $metaGenerator);
    }

    /**
     * @return FilterInterface[]
     */
    protected function getFilters(): array
    {
        return [
            'client' => new PositiveIntegerFilter('clientId'),
            'name' => new LimitedStringFilter('name', 40),
            'has' => new CompoundFilter('permissions'),
        ];
    }
}
