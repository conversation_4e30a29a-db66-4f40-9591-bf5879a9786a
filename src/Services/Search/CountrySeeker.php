<?php
namespace WhiteLabelAdmin\Services\Search;

use Skipper\Search\Contracts\MetaGeneratorInterface;
use Skipper\Search\Contracts\ValidatorInterface;
use Skipper\Search\Filters\LimitedStringFilter;
use Skipper\Search\Filters\PositiveIntegerFilter;
use Skipper\Search\Services\AbstractSeeker;
use WhiteLabelAdmin\Repositories\CountryRepositoryInterface;

final class CountrySeeker extends AbstractSeeker
{
    public function __construct(
        CountryRepositoryInterface $driver,
        ValidatorInterface $validator,
        MetaGeneratorInterface $metaGenerator
    ) {
        parent::__construct($driver, $validator, $metaGenerator);
    }

    protected function getFilters(): array
    {
        return [
            'iso' => new LimitedStringFilter('iso', 2),
            'client' => new PositiveIntegerFilter('client'),
        ];
    }
}
