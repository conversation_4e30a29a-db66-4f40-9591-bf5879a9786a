<?php
namespace WhiteLabelAdmin\Services\Search;

use Skipper\Search\Contracts\MetaGeneratorInterface;
use Skipper\Search\Contracts\ValidatorInterface;
use Skipper\Search\Filters\BooleanFilter;
use Skipper\Search\Filters\LimitedStringFilter;
use Skipper\Search\Filters\PositiveIntegerFilter;
use Skipper\Search\Services\AbstractSeeker;
use WhiteLabelAdmin\Repositories\PaymentGatewayRepositoryInterface;

final class GatewaySeeker extends AbstractSeeker
{
    public function __construct(
        PaymentGatewayRepositoryInterface $driver,
        ValidatorInterface $validator,
        MetaGeneratorInterface $metaGenerator
    ) {
        parent::__construct($driver, $validator, $metaGenerator);
    }

    protected function getFilters(): array
    {
        return [
            'client' => new PositiveIntegerFilter('client'),
            'name' => new LimitedStringFilter('name'),
            'deposit' => new BooleanFilter('deposit'),
            'payout' => new BooleanFilter('payout'),
        ];
    }
}
