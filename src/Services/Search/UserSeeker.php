<?php
namespace WhiteLabelAdmin\Services\Search;

use Skipper\Search\Contracts\FilterInterface;
use Skipper\Search\Contracts\MetaGeneratorInterface;
use Skipper\Search\Contracts\ValidatorInterface;
use Skipper\Search\Filters\LimitedStringFilter;
use Skipper\Search\Filters\OrderByFilter;
use Skipper\Search\Filters\PositiveIntegerFilter;
use Skipper\Search\Services\AbstractSeeker;
use WhiteLabelAdmin\Repositories\UserRepositoryInterface;

final class UserSeeker extends AbstractSeeker
{
    public function __construct(
        UserRepositoryInterface $driver,
        ValidatorInterface $validator,
        MetaGeneratorInterface $metaGenerator
    ) {
        parent::__construct($driver, $validator, $metaGenerator);
    }

    /**
     * @return FilterInterface[]
     */
    protected function getFilters(): array
    {
        return [
            'client' => new PositiveIntegerFilter('clientId'),
            'role' => new LimitedStringFilter('roleName'),
            'login' => new LimitedStringFilter('login'),
            'email' => new LimitedStringFilter('email'),
            'desc' => new OrderByFilter('desc'),
            'asc' => new OrderByFilter('asc'),
        ];
    }
}
