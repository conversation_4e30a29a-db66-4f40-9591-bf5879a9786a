<?php
namespace WhiteLabelAdmin\Services\Search;

use Skipper\Search\Contracts\FilterInterface;
use Skipper\Search\Contracts\MetaGeneratorInterface;
use Skipper\Search\Contracts\ValidatorInterface;
use Skipper\Search\Filters\BooleanFilter;
use Skipper\Search\Filters\EnumFilter;
use Skipper\Search\Filters\LimitedStringFilter;
use Skipper\Search\Filters\OrderByFilter;
use Skipper\Search\Filters\PositiveIntegerFilter;
use Skipper\Search\Services\AbstractSeeker;
use WhiteLabelAdmin\Repositories\PlayerRepositoryInterface;

final class PlayerSeeker extends AbstractSeeker
{
    public function __construct(
        PlayerRepositoryInterface $driver,
        ValidatorInterface $validator,
        MetaGeneratorInterface $metaGenerator
    ) {
        parent::__construct($driver, $validator, $metaGenerator);
    }

    /**
     * @return FilterInterface[]
     */
    protected function getFilters(): array
    {
        return [
            'client' => new PositiveIntegerFilter('client'),
            'blocked' => new BooleanFilter('blocked'),
            'country' => new LimitedStringFilter('country', 2),
            'email' => new LimitedStringFilter('email', 40),
            'ip' => new LimitedStringFilter('ip', 40),
            'with' => new EnumFilter('with', ['balances']),
            'asc' => new OrderByFilter('asc'),
            'desc' => new OrderByFilter('desc'),
        ];
    }
}
