<?php
namespace WhiteLabelAdmin\Services\Search;

use Skipper\Search\Contracts\FilterInterface;
use Skipper\Search\Contracts\MetaGeneratorInterface;
use Skipper\Search\Contracts\ValidatorInterface;
use Skipper\Search\Filters\LimitedStringFilter;
use Skipper\Search\Filters\PositiveIntegerFilter;
use Skipper\Search\Services\AbstractSeeker;
use WhiteLabelAdmin\Repositories\CurrencyRepositoryInterface;

final class CurrencySeeker extends AbstractSeeker
{
    public function __construct(
        CurrencyRepositoryInterface $driver,
        ValidatorInterface $validator,
        MetaGeneratorInterface $metaGenerator
    ) {
        parent::__construct($driver, $validator, $metaGenerator);
    }

    /**
     * @return FilterInterface[]
     */
    protected function getFilters(): array
    {
        return [
            'iso' => new LimitedStringFilter('iso', 3),
            'client' => new PositiveIntegerFilter('client'),
        ];
    }
}
