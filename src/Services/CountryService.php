<?php
namespace WhiteLabelAdmin\Services;

use Skipper\Repository\Exceptions\EntityNotFoundException;
use WhiteLabelAdmin\Entities\Country;
use WhiteLabelAdmin\Repositories\CountryRepositoryInterface;
use WhiteLabelAdmin\Requests\CountryRequest;

final class CountryService
{
    /**
     * @var CountryRepositoryInterface
     */
    private CountryRepositoryInterface $countries;

    public function __construct(CountryRepositoryInterface $countries)
    {
        $this->countries = $countries;
    }

    /**
     * @param CountryRequest $request
     * @return Country
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function manageCountry(CountryRequest $request): Country
    {
        try {
            $country = $this->countries->findOneBy([
                'filter' => [
                    'iso' => [['value' => $request->getIsoCode()]],
                    'client' => [['value' => $request->getClientId()]],
                ],
            ]);
        } catch (EntityNotFoundException $e) {
            $country = new Country($request->getIsoCode(), $request->getClientId());
        }

        $country->setIsAccessible($request->isAvailable());
        $this->countries->save($country);

        return $country;
    }
}
