<?php
namespace WhiteLabelAdmin\Services;

use WhiteLabelAdmin\Entities\ClientSetting;
use White<PERSON>abelAdmin\Repositories\ClientSettingsRepositoryInterface;
use WhiteLabelAdmin\Requests\ClientSettingsRequest;

final class SettingsService
{
    /**
     * @var ClientSettingsRepositoryInterface
     */
    private ClientSettingsRepositoryInterface $settings;

    public function __construct(ClientSettingsRepositoryInterface $settings)
    {
        $this->settings = $settings;
    }

    /**
     * @param ClientSettingsRequest $request
     * @return ClientSetting
     * @throws \Skipper\Repository\Exceptions\EntityNotFoundException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function manageSettings(ClientSettingsRequest $request): ClientSetting
    {
        /** @var ClientSetting $setting */
        $setting = $this->settings->findOneBy([
            'filter' => [
                'client' => [['value' => $request->getClientId()]],
            ],
        ]);

        if (null !== $request->getName()) {
            $setting->setClientName($request->getName());
        }
        if (null !== $request->getSocialLinks()) {
            $setting->setSocialLinks(array_filter(array_merge($setting->getSocialLinks(), $request->getSocialLinks())));
        }

        $this->settings->save($setting);

        return $setting;
    }
}
