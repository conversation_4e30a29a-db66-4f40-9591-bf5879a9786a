<?php
namespace WhiteLabelAdmin\Services;

use Skipper\Repository\Exceptions\EntityNotFoundException;
use WhiteLabelAdmin\Entities\PaymentGateway;
use WhiteLabelAdmin\Repositories\PaymentGatewayRepositoryInterface;
use WhiteLabelAdmin\Requests\PaymentGatewayRequest;

final class PaymentGatewayService
{
    /**
     * @var PaymentGatewayRepositoryInterface
     */
    private PaymentGatewayRepositoryInterface $gateways;

    public function __construct(PaymentGatewayRepositoryInterface $gateways)
    {
        $this->gateways = $gateways;
    }

    /**
     * @param PaymentGatewayRequest $request
     * @return PaymentGateway
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function manage(PaymentGatewayRequest $request): PaymentGateway
    {
        try {
            $gateway = $this->gateways->getByClientIdAndName($request->getClientId(), $request->getName());
        } catch (EntityNotFoundException $e) {
            $gateway = new PaymentGateway($request->getClientId(), $request->getName());
        }

        if (null !== $request->getIsDeposit()) {
            $gateway->setIsDeposit($request->getIsDeposit());
        }

        if (null !== $request->getIsPayout()) {
            $gateway->setIsPayout($request->getIsPayout());
        }

        $this->gateways->save($gateway);

        return $gateway;
    }
}
