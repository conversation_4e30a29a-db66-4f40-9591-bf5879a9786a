<?php
namespace WhiteLabelAdmin\Services;

use Money\Currency;

final class CurrencyInfoService
{

    private array $currencies = [];

    /**
     * @param Currency $currency
     * @return string
     */
    public function getName(Currency $currency): string
    {
        $this->loadCurrencies();

        return $this->currencies[$currency->getCode()]['currency'] ?? 'Unknown currency';
    }

    private function loadCurrencies()
    {
        if (empty($this->currencies)) {
            $file = __DIR__ . '/../../vendor/moneyphp/money/resources/currency.php';
            if (file_exists($file)) {
                $this->currencies = require $file;
            }
        }
    }

}
