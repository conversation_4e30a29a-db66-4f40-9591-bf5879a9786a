<?php
namespace WhiteLabelAdmin\Services;

use Illuminate\Contracts\Hashing\Hasher;
use Skipper\Exceptions\InvalidArgumentException;
use WhiteLabelAdmin\Entities\Role;
use WhiteLabelAdmin\Entities\User;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;
use WhiteLabelAdmin\Repositories\RoleRepositoryInterface;
use WhiteLabelAdmin\Repositories\UserRepositoryInterface;
use WhiteLabelAdmin\Requests\UserRequest;

final class UserService
{
    /**
     * @var UserRepositoryInterface
     */
    private UserRepositoryInterface $users;

    /**
     * @var Hasher
     */
    private Hasher $hasher;

    /**
     * @var RoleRepositoryInterface
     */
    private RoleRepositoryInterface $roles;

    public function __construct(UserRepositoryInterface $users, Hasher $hasher, RoleRepositoryInterface $roles)
    {
        $this->users = $users;
        $this->hasher = $hasher;
        $this->roles = $roles;
    }

    /**
     * @param UserRequest $request
     * @return User
     * @throws InvalidArgumentException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function register(UserRequest $request): User
    {
        if ($this->users->exists([
            'filter' => [
                'email' => [['value' => $request->getEmail()]],
                'client' => [['value' => $request->getClientId()]],
                'trashed' => [['value' => true]],
            ],
        ])) {
            throw new InvalidArgumentException('email', $request->getEmail());
        }
        $user = new User($request->getClientId(), $request->getLogin(), $request->getEmail(), $this->hasher->make($request->getPassword()));
        $this->assignNewRole($user, $request->getRoleId());
        $this->users->save($user);

        return $user;
    }

    private function assignNewRole(User $user, ?int $roleId)
    {
        if (null === $roleId) {
            $user->setRole(null);
            return;
        }
        /** @var Role $role */
        $role = $this->roles->find($roleId);
        if ($role->getClientId() !== $user->getClientId()) {
            throw new InvalidArgumentException('clientId', $user->getClientId());
        }
        $user->setRole($role);
    }

    /**
     * @param UserRequest $request
     * @return User
     * @throws InvalidArgumentException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function updateUser(UserRequest $request): User
    {
        $user = $this->users->getUserByIdAndClientId($request->getId(), $request->getClientId());
        $this->assignNewRole($user, $request->getRoleId());
        $this->users->save($user);

        return $user;
    }

    /**
     * @param UserRequest $request
     * @return User
     * @throws UnauthorizedException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function changePassword(UserRequest $request): User
    {
        $user = $this->users->getUserByIdAndClientId($request->getId(), $request->getClientId());
//        if (!$this->hasher->check($request->getOldPassword(), $user->getPassword())) {
//            throw new UnauthorizedException(new InvalidArgumentException('oldPassword', 'old_password'));
//        }

        $user->setPassword($this->hasher->make($request->getPassword()));
        $this->users->save($user);

        return $user;
    }

    /**
     * @param UserRequest $request
     * @return User
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function deleteUser(UserRequest $request): User
    {
        $user = $this->users->getUserByIdAndClientId($request->getId(), $request->getClientId());
        $this->users->delete($user);

        return $user;
    }

    /**
     * @param UserRequest $request
     * @return User
     */
    public function getUser(UserRequest $request): User
    {
        return $this->users->getUserByIdAndClientId($request->getId(), $request->getClientId());
    }
}
