<?php
namespace WhiteLabelAdmin\Services;

use Skipper\Exceptions\InvalidArgumentException;
use WhiteLabelAdmin\Entities\Role;
use WhiteLabelAdmin\Exceptions\UnauthorizedException;
use WhiteLabelAdmin\Repositories\RoleRepositoryInterface;
use WhiteLabelAdmin\Requests\RoleRequest;

final class RoleService
{
    /**
     * @var RoleRepositoryInterface
     */
    private RoleRepositoryInterface $roles;

    public function __construct(RoleRepositoryInterface $roles)
    {
        $this->roles = $roles;
    }

    /**
     * @param RoleRequest $request
     * @return Role
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function createRole(RoleRequest $request): Role
    {
        if ($this->roles->exists([
            'filter' => [
                'name'   => [['value' => $request->getName()]],
                'client' => [['value' => $request->getClientId()]],
            ],
        ])) {
            throw new InvalidArgumentException('name', $request->getName());
        }

        $role = new Role($request->getClientId(), $request->getName(), $request->getPermissions());
        $this->roles->save($role);
        $this->roles->updateRolePermissions($role, $request->getPermissions());

        return $role;
    }

    /**
     * @param RoleRequest $request
     * @return Role
     * @throws UnauthorizedException
     * @throws \Skipper\Repository\Exceptions\EntityNotFoundException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function updatePermissions(RoleRequest $request): Role
    {
        /** @var Role $role */
        $role = $this->roles->find($request->getId());
        if ($role->getClientId() !== $request->getClientId()) {
            throw new UnauthorizedException(new InvalidArgumentException('clientId', $request->getClientId()));
        }
        if (null !== $request->getName()) {
            $role->setName($request->getName());
            $this->roles->save($role);
        }

        return $this->roles->updateRolePermissions($role, $request->getPermissions());
    }

    /**
     * @param RoleRequest $request
     * @return Role
     * @throws UnauthorizedException
     * @throws \Skipper\Repository\Exceptions\EntityNotFoundException
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function deleteRole(RoleRequest $request): Role
    {
        /** @var Role $role */
        $role = $this->roles->find($request->getId());
        if ($role->getClientId() !== $request->getClientId()) {
            throw new UnauthorizedException(new InvalidArgumentException('clientId', $request->getClientId()));
        }

        $this->roles->delete($role);

        return $role;
    }
}
