<?php
namespace WhiteLabelAdmin\Services;

use Skipper\Repository\Exceptions\EntityNotFoundException;
use WhiteLabelAdmin\Entities\SystemCurrency;
use WhiteLabelAdmin\Repositories\CurrencyRepositoryInterface;
use WhiteLabelAdmin\Requests\CurrencyRequest;

final class CurrencyService
{
    /**
     * @var CurrencyRepositoryInterface
     */
    private CurrencyRepositoryInterface $currencies;

    /**
     * @var CurrencyInfoService
     */
    private CurrencyInfoService $info;

    public function __construct(CurrencyRepositoryInterface $currencies, CurrencyInfoService $info)
    {
        $this->currencies = $currencies;
        $this->info = $info;
    }

    /**
     * @param CurrencyRequest $request
     * @return SystemCurrency
     * @throws \Skipper\Repository\Exceptions\StorageException
     */
    public function manageCurrency(CurrencyRequest $request): SystemCurrency
    {
        try {
            $currency = $this->currencies->findByIsoCodeAndClientId($request->getIsoCode(), $request->getClientId());
        } catch (EntityNotFoundException $e) {
            $currency = new SystemCurrency($request->getIsoCode(), $request->getClientId());
            $currency->setName($this->info->getName($currency->getCurrency()));
        }
        if (null !== $request->getIsRegisterAvailable()) {
            $currency->setIsAvailableForRegister($request->getIsRegisterAvailable());
        }
        if (null !== $request->getMinBet()) {
            $currency->setMinBet($request->getMinBet());
        }
        if (null !== $request->getMaxWin()) {
            $currency->setMaxWin($request->getMaxWin());
        }
        $this->currencies->save($currency);

        return $currency;
    }
}
