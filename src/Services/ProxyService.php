<?php
namespace WhiteLabelAdmin\Services;

use App\Services\PlayerAppClient;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use WhiteLabelAdmin\Requests\ProxyRequest;

final class ProxyService
{
    /**
     * @var PlayerAppClient
     */
    private PlayerAppClient $client;

    public function __construct(PlayerAppClient $client)
    {
        $this->client = $client;
    }

    /**
     * @param Request $request
     * @param ProxyRequest $request
     * @return array
     * @throws GuzzleException
     */
    public function proxy(ProxyRequest $proxyRequest): array
    {
        return $this->client->doRequest($proxyRequest->getMethod(), $proxyRequest->getUri(), $proxyRequest->getQuery(), $proxyRequest->getBody());
    }
}
