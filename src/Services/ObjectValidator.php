<?php
namespace WhiteLabelAdmin\Services;

use Skipper\Exceptions\SymfonyValidationException;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ObjectValidator
{
    /**
     * @var ValidatorInterface
     */
    private ValidatorInterface $validator;

    public function __construct(ValidatorInterface $validator)
    {
        $this->validator = $validator;
    }

    /**
     * @param $object
     * @param array $groups
     * @throws SymfonyValidationException
     */
    public function validate($object, array $groups = []): void
    {
        $errors = $this->validator->validate($object, null, array_merge(['Default'], $groups));
        if (0 !== $errors->count()) {
            throw new SymfonyValidationException($errors);
        }
    }
}
