<?php
namespace WhiteLabelAdmin\Entities;

use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\Utilities\HasId;

class ClientSetting implements Entity, \JsonSerializable
{
    use HasId;

    private int $clientId;
    private string $domain;
    private string $clientName;
    private array $socialLinks = [];

    public function __construct(int $clientId, string $clientName)
    {
        $this->clientId = $clientId;
        $this->clientName = $clientName;
        $this->setDomain($clientName);
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return string
     */
    public function getDomain(): string
    {
        return $this->domain;
    }

    /**
     * @return string
     */
    public function getClientName(): string
    {
        return $this->clientName;
    }

    /**
     * @return array
     */
    public function getSocialLinks(): array
    {
        return $this->socialLinks;
    }

    /**
     * @param string $domain
     * @return ClientSetting
     */
    public function setDomain(string $domain): ClientSetting
    {
        $this->domain = $domain;

        return $this;
    }

    /**
     * @param string $clientName
     * @return ClientSetting
     */
    public function setClientName(string $clientName): ClientSetting
    {
        $this->clientName = $clientName;

        return $this;
    }

    /**
     * @param array $socialLinks
     * @return ClientSetting
     */
    public function setSocialLinks(array $socialLinks): ClientSetting
    {
        $this->socialLinks = $socialLinks;

        return $this;
    }

    /**
     * @return mixed
     */
    public function jsonSerialize()
    {
        return [
            'client_id' => $this->getClientId(),
            'name' => $this->getClientName(),
            'domain' => $this->getDomain(),
            'links' => $this->getSocialLinks(),
        ];
    }
}
