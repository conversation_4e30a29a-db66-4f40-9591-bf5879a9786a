<?php
namespace WhiteLabelAdmin\Entities;

use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\Utilities\HasId;
use OpenApi\Annotations as OA;

class User implements Entity, \JsonSerializable
{
    use HasId;

    private int $clientId;
    private string $login;
    private string $email;
    private string $password;
    private ?string $lastSeenIp = null;
    private ?Role $role = null;

    public function __construct(int $clientId, string $login, string $email, string $password)
    {
        $this->clientId = $clientId;
        $this->login = $login;
        $this->password = $password;
        $this->email = $email;
    }

    /**
     * @param string $password
     * @return User
     */
    public function setPassword(string $password): User
    {
        $this->password = $password;

        return $this;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return string
     */
    public function getLogin(): string
    {
        return $this->login;
    }

    /**
     * @return string
     */
    public function getPassword(): string
    {
        return $this->password;
    }

    /**
     * @return string|null
     */
    public function getLastSeenIp(): ?string
    {
        return $this->lastSeenIp;
    }

    /**
     * @return Role|null
     */
    public function getRole(): ?Role
    {
        return $this->role;
    }

    /**
     * @param string|null $lastSeenIp
     * @return User
     */
    public function setLastSeenIp(?string $lastSeenIp): User
    {
        $this->lastSeenIp = $lastSeenIp;

        return $this;
    }

    /**
     * @param Role|null $role
     * @return User
     */
    public function setRole(?Role $role): User
    {
        $this->role = $role;

        return $this;
    }

    /**
     * @return array
     * @OA\Schema(schema="user", type="object", properties={
     *     @OA\Property(property="id", type="integer", description="user id"),
     *     @OA\Property(property="client_id", type="integer", description="user client id"),
     *     @OA\Property(property="login", type="string", description="user nickname"),
     *     @OA\Property(property="role", ref="#/components/schemas/role", nullable=true, description="user role"),
     *     @OA\Property(property="ip", type="string", format="ipv4", description="user last login ip"),
     *     @OA\Property(property="email", type="string", description="user email", format="email")
     * })
     */
    public function jsonSerialize()
    {
        return [
            'id' => $this->getId(),
            'client_id' => $this->getClientId(),
            'login' => $this->getLogin(),
            'role' => $this->getRole(),
            'ip' => $this->getLastSeenIp(),
            'email' => $this->getEmail(),
        ];
    }
}
