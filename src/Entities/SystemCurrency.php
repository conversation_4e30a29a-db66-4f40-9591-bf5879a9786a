<?php
namespace WhiteLabelAdmin\Entities;

use Money\Currency;
use Money\Money;
use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\Utilities\HasId;
use OpenApi\Annotations as OA;

final class SystemCurrency implements Entity, \JsonSerializable
{
    use HasId;

    private string $isoCode;
    private int $clientId;
    private bool $isAvailableForRegister;
    private Money $minBet;
    private Money $maxWin;
    private string $name;

    public function __construct(string $isoCode, int $clientId)
    {
        $this->isoCode = $isoCode;
        $this->clientId = $clientId;
        $this->setMinBet(1)
            ->setIsAvailableForRegister(true)
            ->setMaxWin(100000000); # 1 million
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return SystemCurrency
     */
    public function setName(string $name): SystemCurrency
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return array
     * @OA\Schema(schema="currency", description="currency object", type="object", properties={
     *     @OA\Property(property="iso_code", description="currency's iso code", type="string"),
     *     @OA\Property(property="name", description="currency's name", type="string"),
     *     @OA\Property(property="client_id", description="user client", type="integer"),
     *     @OA\Property(property="is_available", description="if currency available for register", type="boolean"),
     *     @OA\Property(property="min_bet", description="minimum bet", type="integer"),
     *     @OA\Property(property="max_win", description="maximum win amount", type="integer")
     * })
     */
    public function jsonSerialize()
    {
        return [
            'iso_code' => $this->getIsoCode(),
            'name' => $this->getName(),
            'client_id' => $this->getClientId(),
            'is_available' => $this->isAvailableForRegister(),
            'min_bet' => $this->getMinBet(),
            'max_win' => $this->getMaxWin(),
        ];
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return Currency
     */
    public function getCurrency(): Currency
    {
        return new Currency($this->getIsoCode());
    }

    /**
     * @return string
     */
    public function getIsoCode(): string
    {
        return $this->isoCode;
    }

    /**
     * @return bool
     */
    public function isAvailableForRegister(): bool
    {
        return $this->isAvailableForRegister;
    }

    /**
     * @return Money
     */
    public function getMinBet(): Money
    {
        return $this->minBet;
    }

    /**
     * @return Money
     */
    public function getMaxWin(): Money
    {
        return $this->maxWin;
    }

    /**
     * @param bool $isAvailableForRegister
     * @return SystemCurrency
     */
    public function setIsAvailableForRegister(bool $isAvailableForRegister): SystemCurrency
    {
        $this->isAvailableForRegister = $isAvailableForRegister;

        return $this;
    }

    /**
     * @param int $minBet
     * @return SystemCurrency
     */
    public function setMinBet(int $minBet): SystemCurrency
    {
        $this->minBet = new Money($minBet, $this->getCurrency());

        return $this;
    }

    /**
     * @param int $maxWin
     * @return SystemCurrency
     */
    public function setMaxWin(int $maxWin): SystemCurrency
    {
        $this->maxWin = new Money($maxWin, $this->getCurrency());

        return $this;
    }
}
