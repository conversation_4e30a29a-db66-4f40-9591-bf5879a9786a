<?php
namespace WhiteLabelAdmin\Entities;

use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\Utilities\HasId;
use OpenApi\Annotations as OA;

final class PaymentGateway implements Entity, \JsonSerializable
{
    use HasId;

    private int $clientId;
    private string $name;
    private bool $isDeposit;
    private bool $isPayout;

    public function __construct(int $clientId, string $name)
    {
        $this->clientId = $clientId;
        $this->name = $name;
        $this->setIsDeposit(true)
            ->setIsPayout(true);
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return bool
     */
    public function isDeposit(): bool
    {
        return $this->isDeposit;
    }

    /**
     * @return bool
     */
    public function isPayout(): bool
    {
        return $this->isPayout;
    }

    /**
     * @param bool $isDeposit
     * @return PaymentGateway
     */
    public function setIsDeposit(bool $isDeposit): PaymentGateway
    {
        $this->isDeposit = $isDeposit;

        return $this;
    }

    /**
     * @param bool $isPayout
     * @return PaymentGateway
     */
    public function setIsPayout(bool $isPayout): PaymentGateway
    {
        $this->isPayout = $isPayout;

        return $this;
    }

    /**
     * @return array
     * @OA\Schema(schema="gateway", type="object", properties={
     *     @OA\Property(property="name", type="string"),
     *     @OA\Property(property="client_id", type="integer"),
     *     @OA\Property(property="deposit", type="boolean"),
     *     @OA\Property(property="payout", type="boolean")
     * })
     */
    public function jsonSerialize()
    {
        return [
            'name' => $this->getName(),
            'client_id' => $this->getClientId(),
            'deposit' => $this->isDeposit(),
            'payout' => $this->isPayout(),
        ];
    }
}
