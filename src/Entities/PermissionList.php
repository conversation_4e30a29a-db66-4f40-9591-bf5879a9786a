<?php

namespace WhiteLabelAdmin\Entities;

interface PermissionList
{
    public const MENU_DROPDOWN_USERS = 'menu-dropdown-users';
    public const MENU_DROPDOWN_PAYOUTS ='menu-dropdown-payouts';
    public const MENU_DROPDOWN_CASINO = 'menu-dropdown-casino';
    public const MENU_DROPDOWN_SEO = 'menu-dropdown-seo';
    public const MENU_DROPDOWN_STATISTICS = 'menu-dropdown-statistics';
    public const MENU_DROPDOWN_CONTENT = 'menu-dropdown-content';
    public const MENU_DROPDOWN_SETTINGS = 'menu-dropdown-settings';
    public const MENU_DROPDOWN_BETS = 'menu-dropdown-bets';
    public const MENU_DROPDOWN_BONUSES = 'menu-dropdown-bonuses';
    public const MENU_DROPDOWN_BIG_WINS = 'menu-dropdown-big-wins';
    public const MENU_DROPDOWN_UNSUCCESSFUL_DEPOSITS = 'menu-dropdown-unsuccessful-deposits';

    public const BANNERS_LIST = 'banners-list';
    public const BANNERS_ADD = 'banners-add';
    public const BANNERS_EDIT = 'banners-edit';
    public const BANNERS_DELETE = 'banners-delete';
    public const BANNERS_SEARCH = 'banners-search';
    public const BANNERS_COL_ID = 'banners-col-id';
    public const BANNERS_COL_IMAGE = 'banners-col-image';
    public const BANNERS_COL_NAME = 'banners-col-name';
    public const BANNERS_COL_LANGUAGE = 'banners-col-language';
    public const BANNERS_COL_TYPE = 'banners-col-type';
    public const BANNERS_COL_URL = 'banners-col-url';
    public const BANNERS_COL_COUNTRY = 'banners-col-country';
    public const BANNERS_COL_PAGES = 'banners-col-pages';
    public const BANNERS_COL_WEIGHT = 'banners-col-weight';
    public const BANNERS_COL_START_DATE = 'banners-col-start_date';
    public const BANNERS_COL_END_DATE = 'banners-col-end_date';
    public const BANNERS_COL_STATUS = 'banners-col-status';
    public const BANNERS_COL_SMARTICO_SEGMENT_ID = 'banners-col-smartico-segment-id';

    public const BLACKLIST_REQUISITES_LIST = 'blacklist-requisites-list';
    public const BLACKLIST_REQUISITES_ADD = 'blacklist-requisites-add';
    public const BLACKLIST_REQUISITES_DELETE = 'blacklist-requisites-delete';
    public const BLACKLIST_REQUISITES_COL_CREATED_AT = 'blacklist-requisites-col-created_at';
    public const BLACKLIST_REQUISITES_COL_ID = 'blacklist-requisites-col-id';
    public const BLACKLIST_REQUISITES_COL_COMMENT = 'blacklist-requisites-col-comment';
    public const BLACKLIST_REQUISITES_COL_REQUISITE = 'blacklist-requisites-col-requisite';
    public const BLACKLIST_REQUISITES_COL_AUTHOR = 'blacklist-requisites-col-author';
    public const MODERATORS_LIST = 'moderators-list';
    public const MODERATORS_ADD = 'moderators-add';
    public const MODERATORS_EDIT = 'moderators-edit';
    public const MODERATORS_DELETE = 'moderators-delete';
    public const MODERATORS_SEARCH = 'moderators-search';
    public const MODERATORS_PASSWORD_CHANGE = 'moderators-password_change';
    public const MODERATORS_COL_ID = 'moderators-col-id';
    public const MODERATORS_COL_LOGIN = 'moderators-col-login';
    public const MODERATORS_COL_EMAIL = 'moderators-col-email';
    public const MODERATORS_COL_ROLE = 'moderators-col-role';
    public const MODERATORS_COL_IP = 'moderators-col-ip';

    public const ROLES_LIST = 'roles-list';
    public const ROLES_ADD = 'roles-add';
    public const ROLES_EDIT = 'roles-edit';
    public const ROLES_DELETE = 'roles-delete';
    public const ROLES_COL_ID = 'roles-col-id';
    public const ROLES_COL_NAME = 'roles-col-name';
    public const ROLES_COL_PERMISSIONS = 'roles-col-permissions';

    public const LANGUAGES_lIST = 'languages-list';
    public const LANGUAGES_EDIT = 'languages-edit';
    public const LANGUAGES_COL_NAME = 'languages-col-name';
    public const LANGUAGES_COL_CODE = 'languages-col-code';
    public const LANGUAGES_COL_STATUS = 'languages-col-status';

    public const EMAIL_CHANGE_lIST = 'email-change-list';
    public const EMAIL_CHANGE_ID_PLAYER = 'email-change-col-id-player';
    public const EMAIL_CHANGE_DATE_CHANGE = 'email-change-col-date-change';
    public const EMAIL_CHANGE_EMAIL_MODERATOR = 'email-change-col-email-moderator';
    public const EMAIL_CHANGE_OLD_EMAIL = 'email-change-col-old-email';
    public const EMAIL_CHANGE_NEW_EMAIL = 'email-change-col-new-email';
    public const EMAIL_CHANGE_FILTER = 'email-change-filter';

    public const SEO_LIST = 'seo-list';
    public const SEO_ADD = 'seo-add';
    public const SEO_EDIT = 'seo-edit';
    public const SEO_DELETE = 'seo-delete';
    public const SEO_SEARCH = 'seo-search';
    public const SEO_COL_ID = 'seo-col-id';
    public const SEO_COL_URL = 'seo-col-url';
    public const SEO_COL_TITLE = 'seo-col-title';
    public const SEO_COL_LANGUAGE = 'seo-col-language';
    public const SEO_COL_PAGE = 'seo-col-page';
    public const SEO_COL_CONTENT = 'seo-col-content';

    public const STATIC_LIST = 'static-list';
    public const STATIC_ADD = 'static-add';
    public const STATIC_EDIT = 'static-edit';
    public const STATIC_DELETE = 'static-delete';
    public const STATIC_SEARCH = 'static-search';
    public const STATIC_COL_ID = 'static-col-id';
    public const STATIC_COL_URL = 'static-col-url';
    public const STATIC_COL_TITLE = 'static-col-title';
    public const STATIC_COL_LANGUAGE = 'static-col-language';
    public const STATIC_COL_CONTENT = 'static-col-content';

    public const DEPOSITS_LIST = 'deposits-list';
    public const DEPOSITS_COL_AMOUNT = 'deposits-col-amount';
    public const DEPOSITS_COL_DETAILS = 'deposits-col-details';
    public const DEPOSITS_COL_ID = 'deposits-col-id';
    public const DEPOSITS_COL_HASH = 'deposits-col-id_hash';
    public const DEPOSITS_COL_DATE = 'deposits-col-date';
    public const DEPOSITS_COL_PLAYER = 'deposits-col-player_id';
    public const DEPOSITS_COL_AGGREGATOR = 'deposits-col-aggregator';
    public const DEPOSITS_COL_WALLET_TYPE = 'deposits-col-wallet_type';
    public const DEPOSITS_COL_TOKEN = 'deposits-col-token';
    public const DEPOSITS_FILTER = 'deposits-filter';
    public const DEPOSITS_SEARCH = 'deposits-search';
    public const DEPOSITS_COL_SUM = 'deposits-col-sum';
    public const DEPOSITS_COL_STATUS = 'deposits-col-status';
    public const DEPOSITS_COL_DATA = 'deposits-col-data';
    public const DEPOSITS_COL_UPDATE_STATUS = 'deposits-col-update-status';
    public const DEPOSITS_COL_FAILED_ATTEMPTS_AGGREGATOR = 'deposits-col-failed-attempts-aggregator';
    public const DEPOSITS_COL_FAILED_ATTEMPTS_AGGREGATOR_ID = 'deposits-col-failed-attempts-aggregator-id';
    public const DEPOSITS_COL_TRANSACTION_ID = 'deposits-col-transaction-id';


    public const COUNTRIES_LIST = 'countries-list';
    public const COUNTRIES_EDIT = 'countries-edit';

    public const CURRENCIES_LIST = 'currencies-list';
    public const CURRENCIES_EDIT = 'currencies-edit';

    public const USERS_LIST = 'users-list';
    public const USERS_SEARCH_DONT_SHOW_TABLE = 'users-search-dont-show-table';
    public const USERS_SEARCH_NAME = 'users-search-name';
    public const USERS_SEARCH_CURRENCY = 'users-search-currency';
    public const USERS_FILTER = 'users-filter';
    public const USERS_DETAILS = 'users-details';
    public const USERS_BONUSES = 'users-bonuses';
    public const USERS_EDIT = 'users-edit';
    public const USERS_FAKE = 'users-fake';
    public const USERS_NOT_FAKE = 'users-not-fake';
    public const USERS_VIEW_VIP = 'users-view-vip';
    public const USERS_CHANGE_VIP = 'users-change-vip';
    public const USERS_PAYOUT_WITHOUT_MODERATION = 'users-payout-without-moderation';
    public const USERS_SUSPECT = 'users-suspect';
    public const USERS_DISABLE_USER = 'users-disable-user';
    public const USERS_DISABLE_CASINO = 'users-disable-casino';
    public const USERS_SET_STATUS = 'users-set-status';
    public const USERS_COL_ID = 'users-col-id';
    public const USERS_COL_INFO = 'users-col-info';
    public const USERS_COL_META = 'users-col-meta';
    public const USERS_COL_BALANCE = 'users-col-balance';
    public const USERS_COL_UUID = 'users-col-uuid';
    public const USERS_COL_LOGIN = 'users-col-login';
    public const USERS_COL_IP = 'users-col-ip';
    public const USERS_COL_EMAIL = 'users-col-email';
    public const USERS_COL_EMAIL_CONFIRMED = 'users-col-email_confirmed';
    public const USERS_COL_BIRTH = 'users-col-birth';
    public const USERS_COL_FIRSTNAME = 'users-col-first_name';
    public const USERS_COL_LASTNAME = 'users-col-last_name';
    public const USERS_COL_PHONE = 'users-col-phone';
    public const USERS_COL_GENDER = 'users-col-gender';
    public const USERS_COL_CURRENCY = 'users-col-currency';
    public const USERS_COL_COUNTRY = 'users-col-country';
    public const USERS_COL_CITY = 'users-col-city';
    public const USERS_COL_LANGUAGE = 'users-col-language';
    public const USERS_COL_IS_READY_BONUS = 'users-col-is_ready_bonus';
    public const USERS_COL_WAGER = 'users-col-wager';
    public const USERS_COL_DEPOSIT = 'users-col-deposit';
    public const USERS_COL_WITHDRAWAL = 'users-col-withdrawal';
    public const USERS_COL_WITHDRAWAL_DAY = 'users-col-withdrawal_day';
    public const ONE_WITHDRAWAL_PER_DAY = 'users-one-withdrawal-per-day';
    public const THREE_WITHDRAWAL_PER_DAY = 'users-three-withdrawal-per-day';
    public const FIVE_WITHDRAWAL_PER_DAY = 'users-five-withdrawal-per-day';

    public const USERS_COL_TURNOVER = 'users-col-turnover';
    public const USERS_COL_PROFIT = 'users-col-profit';
    public const USERS_COL_STATUSES = 'users-col-statuses';
    public const USERS_COL_REGISTRATION_DATE = 'users-col-registration_date';
    public const USERS_COL_LAST_ACTIVITY = 'users-col-last_activity';
    public const USERS_COL_COMMENT = 'users-col-comment';
    public const USERS_COL_IS_FAKE = 'users-col-is_fake';
    public const USERS_COL_IS_SUSPICIOUS = 'users-col-is_suspicious';
    public const USERS_COL_BLOCKED = 'users-col-blocked';
    public const USERS_COL_CASINO_ACCESS = 'users-col-casino_access';
    public const USERS_COL_VERIFICATION_FORM_ID = 'users-col-verification_form_id';
    public const USERS_COL_VERIFICATION_FORM_URL = 'users-col-verification_form_url';
    public const USERS_COL_VERIFICATION_FORM_NAME = 'users-col-verification_form_name';
    public const USERS_COL_VERIFICATION_STATUS = 'users-col-verification_status';
    public const USERS_COL_COMPLETED_DATA_PROFILE = 'users-col-completed_data_profile';
    public const USERS_COL_CONFIRMED_EMAIL = 'users-col-confirmed_email';
    public const USERS_COL_CONFIRMED_PHONE = 'users-col-confirmed_phone';
    public const USERS_COL_VERIFICATION_PASSED = 'users-col-verification_passed';
    public const USERS_VERIFICATIONS_CREATE = 'users-verifications-create';
    public const USERS_VERIFICATIONS_CANCEL = 'users-verifications-cancel';
    public const USERS_COMMENTS_LIST = 'users-comments-list';
    public const USERS_COMMENTS_COL_DATE = 'users-comments-col-date';
    public const USERS_COMMENTS_COL_COMMENT = 'users-comments-col-comment';
    public const USERS_COMMENTS_COL_AUTHOR = 'users-comments-col-author';
    public const DETERMINING_USER_ID = 'determining-user-id';

    public const PAYOUTS_GET = 'payouts-get';
    public const PAYOUTS_LIST = 'payouts-list';
    public const PAYOUTS_EDIT = 'payouts-edit';
    public const PAYOUTS_IMPS = 'payouts-imps';
    public const PAYOUTS_BLACKLIST = 'payouts-blacklist';
    public const PAYOUTS_NEW_USERS = 'payouts-new_users';
    public const PAYOUTS_A_B_TEST = 'payouts-a-b-test';
    public const PAYOUTS_SUSPICIOUS = 'payouts-suspicious';
    public const PAYOUTS_VIP = 'payouts-vip';
    public const PAYOUTS_ALL = 'payouts-all';
    public const PAYOUTS_STATISTICS = 'payouts-statistics';
    public const PAYOUTS_COL_ID = 'payouts-col-id';
    public const PAYOUTS_COL_DETAILS = 'payouts-col-details';
    public const PAYOUTS_COL_REQUISITES = 'payouts-col-requisites';
    public const PAYOUTS_COL_EMAIL = 'payouts-col-email';
    public const PAYOUTS_COL_PLAYER = 'payouts-col-player';
    public const PAYOUTS_COL_WITHDRAWAL = 'payouts-col-withdrawal';
    public const PAYOUTS_COL_VERIFICATION_FORM_ID = 'payouts-col-verification_form_id';
    public const PAYOUTS_COL_VERIFICATION_FORM_URL = 'payouts-col-verification_form_url';
    public const PAYOUTS_COL_VERIFICATION_FORM_NAME = 'payouts-col-verification_form_name';
    public const PAYOUTS_COL_VERIFICATION_STATUS = 'payouts-col-verification_status';
    public const PAYOUTS_COL_VERIFICATION_PASSED = 'payouts-col-verification_passed';
    public const PAYOUTS_COL_DATE = 'payouts-col-date';
    public const PAYOUTS_COL_TIME = 'payouts-col-time';
    public const PAYOUTS_COL_PAYMENT_DATE = 'payouts-col-payment-date';
    public const PAYOUTS_COL_STATUS = 'payouts-col-status';
    public const PAYOUTS_COL_ADMIN_LAST_CHANGES = 'payouts-col-admin_last_changes';
    public const PAYOUTS_COL_GATEWAY = 'payouts-col-gateway';
    public const PAYOUTS_COL_AGGREGATOR = 'payouts-col-aggregator';
    public const PAYOUTS_COL_AGGREGATOR_DECRYPTED = 'payouts-col-aggregator-decrypted';
    public const PAYOUTS_COL_AGGREGATOR_ID = 'payouts-col-aggregator-id';
    public const PAYOUTS_COL_COMMENT = 'payouts-col-comment';
    public const PAYOUTS_COL_AMOUNT = 'payouts-col-amount';
    public const PAYOUTS_COL_CHANGED_BY = 'payouts-col-changed-by';
    public const PAYOUTS_COL_HASH = 'payouts-col-id_hash';
    public const PAYOUTS_COL_USER_INFO = 'payouts-col-user_info';
    public const PAYOUTS_COL_FINANCE = 'payouts-col-finance';
    public const PAYOUTS_RISK = 'payouts-risk';

    public const LOGS_LIST = 'logs-list';

    public const PRESETS_LIST = 'presets-list';
    public const PRESETS_COL_TOKEN = 'presets-col-token';
    public const PRESETS_COL_PAYMENT_SYSTEM = 'presets-col-payment-system';
    public const PRESETS_COL_IMAGE = 'presets-col-image';
    public const PRESETS_COL_MIN_DEPOSIT = 'presets-col-min-deposit';
    public const PRESETS_COL_MAX_DEPOSIT = 'presets-col-max-deposit';
    public const PRESETS_COL_ID = 'presets-col-id';
    public const PRESETS_COL_ACTIVE = 'presets-col-active';
    public const PRESETS_COL_ORDER = 'presets-col-order';
    public const PRESETS_COL_AGGREGATOR = 'presets-col-aggregator';
    public const PRESETS_BUTTON_PRESET = 'presets-button-preset';
    public const PRESETS_EDIT = 'presets-edit';
    public const PRESETS_FILTER_CURRENCY = 'presets-filter-currency';
    public const PRESETS_FILTER_ACTIVE = 'presets-filter-active';
    public const PRESETS_FILTER_AGGREGATOR = 'presets-filter-aggregator';
    public const PRESETS_FILTER_RISK = 'presets-filter-risk';

    public const PRESETS2_LIST = 'presets2-list';
    public const PRESETS2_COL_ID = 'presets2-col-id';
    public const PRESETS2_COL_TOKEN = 'presets2-col-token';
    public const PRESETS2_COL_NAME = 'presets2-col-name';
    public const PRESETS2_COL_CURRENCY = 'presets2-col-currency';
    public const PRESETS2_COL_ORDER = 'presets2-col-order';
    public const PRESETS2_COL_AGGREGATOR = 'presets2-col-aggregator';
    public const PRESETS2_COL_MIN_DEPOSIT = 'presets2-col-min-deposit';
    public const PRESETS2_COL_MAX_DEPOSIT = 'presets2-col-max-deposit';
    public const PRESETS2_COL_MIN_DEPOSIT_COUNT = 'presets2-col-min-deposit-count';
    public const PRESETS2_COL_MAX_DEPOSIT_COUNT = 'presets2-col-max-deposit-count';
    public const PRESETS2_COL_MIN_PAYOUT_COUNT = 'presets2-col-min-payout-count';
    public const PRESETS2_COL_MAX_PAYOUT_COUNT = 'presets2-col-max-payout-count';
    public const PRESETS2_COL_MIN_DEPOSIT_SUM = 'presets2-col-min-deposit-sum';
    public const PRESETS2_COL_MAX_DEPOSIT_SUM = 'presets2-col-max-deposit-sum';
    public const PRESETS2_COL_MIN_PAYOUT_SUM = 'presets2-col-min-payout-sum';
    public const PRESETS2_COL_MAX_PAYOUT_SUM = 'presets2-col-max-payout-sum';
    public const PRESETS2_COL_PAYMENT_SYSTEM = 'presets-col-payment-system';
    public const PRESETS2_COL_IMAGE = 'presets2-col-image';
    public const PRESETS2_COL_ACTIVE = 'presets2-col-active';
    public const PRESETS2_BUTTON_PRESET = 'presets2-button-preset';
    public const PRESETS2_ADD = 'presets2-add';
    public const PRESETS2_EDIT = 'presets2-edit';
    public const PRESETS2_DELETE = 'presets2-delete';
    public const PRESETS2_FILTER_CURRENCY = 'presets2-filter-currency';
    public const PRESETS2_FILTER_ACTIVE = 'presets2-filter-active';
    public const PRESETS2_FILTER_AGGREGATOR = 'presets2-filter-aggregator';
    public const PRESETS2_FILTER_PAYMENT_SYSTEM = 'presets2-filter-payment-system';
    public const PRESETS2_FILTER_MIN_DEPOSIT_COUNT = 'presets2-filter-min-deposit-count';
    public const PRESETS2_FILTER_MAX_DEPOSIT_COUNT = 'presets2-filter-max-deposit-count';
    public const PRESETS2_FILTER_MIN_PAYOUT_COUNT = 'presets2-filter-min-payout-count';
    public const PRESETS2_FILTER_MAX_PAYOUT_COUNT = 'presets2-filter-max-payout-count';
    public const PRESETS2_FILTER_MIN_DEPOSIT_SUM = 'presets2-filter-min-deposit-sum';
    public const PRESETS2_FILTER_MAX_DEPOSIT_SUM = 'presets2-filter-max-deposit-sum';
    public const PRESETS2_FILTER_MIN_PAYOUT_SUM = 'presets2-filter-min-payout-sum';
    public const PRESETS2_FILTER_MAX_PAYOUT_SUM = 'presets2-filter-max-payout-sum';
    public const PRESETS2_FILTER_MIN_DEPOSIT = 'presets2-filter-min-deposit';
    public const PRESETS2_FILTER_MAX_DEPOSIT = 'presets2-filter-max-deposit';
    public const PRESETS2_FILTER_RISK = 'presets2-filter-risk';
    public const PRESETS2_FILTER_TRAFFIC_TYPE = 'presets2-filter-traffic-type';
    public const PRESETS2_FILTER_OFFER_IDS = 'presets2-filter-offer-ids';
    public const PRESETS2_FILTER_PARTNER_IDS = 'presets2-filter-partner-ids';
    public const PRESETS2_FILTER_REGISTRATION_DATE = 'presets2-filter-registration-date';
    public const PRESETS2_FILTER_FIRST_DEPOSIT_DATE = 'presets2-filter-first-deposit-date';
    public const PRESETS2_DEPOSITS_LIMITED_DISPLAY = 'presets2-deposits-limited-display';

    public const PAYOUTS_SETTING_LIST = 'settings-payouts-list';
    public const PAYOUTS_SETTING_COL_TOKEN = 'settings-payouts-col-token';
    public const PAYOUTS_SETTING_COL_PAYMENT_SYSTEM = 'settings-payouts-col-payment-system';
    public const PAYOUTS_SETTING_COL_IMAGE = 'settings-payouts-col-image';
    public const PAYOUTS_SETTING_COL_MIN_DEPOSIT = 'settings-payouts-col-min-deposit';
    public const PAYOUTS_SETTING_COL_MAX_DEPOSIT = 'settings-payouts-col-max-deposit';
    public const PAYOUTS_SETTING_COL_MIN_SUBSEQUENT_DEPOSIT = 'settings-payouts-col-min-subsequent-deposit';
    public const PAYOUTS_SETTING_COL_MAX_SUBSEQUENT_DEPOSIT = 'settings-payouts-col-max-subsequent-deposit';
    public const PAYOUTS_SETTING_COL_FIRST_N_WITHDRAWALS = 'settings-payouts-col-first-n-withdrawals';
    public const PAYOUTS_SETTING_COL_ID = 'settings-payouts-col-id';
    public const PAYOUTS_SETTING_COL_ACTIVE = 'settings-payouts-col-active';
    public const PAYOUTS_SETTING_COL_ORDER = 'settings-payouts-col-order';
    public const PAYOUTS_SETTING_COL_AGGREGATOR = 'settings-payouts-col-aggregator';
    public const PAYOUTS_SETTING_EDIT = 'settings-payouts-edit';
    public const PAYOUTS_SETTING_FILTER_CURRENCY = 'settings-payouts-filter-currency';
    public const PAYOUTS_SETTING_FILTER_ACTIVE = 'settings-payouts-filter-active';
    public const PAYOUTS_SETTING_FILTER_AGGREGATOR = 'settings-payouts-filter-aggregator';
    public const PAYOUTS_SETTING_AUTO_PAYOUT_SWITCH = 'settings-payouts-auto-payout-switch';
    public const PAYOUTS_SETTING_LIMITED_DISPLAY = 'settings-payouts-limited-display';

    public const PAYMENTS_LIST = 'payments-list';
    public const PAYMENTS_COL_ID = 'payments-col-id';
    public const PAYMENTS_COL_AMOUNT = 'payments-col-amount';
    public const PAYMENTS_COL_PLAYER = 'payments-col-player';
    public const PAYMENTS_COL_SOURCE = 'payments-col-source';
    public const PAYMENTS_COL_STATUS = 'payments-col-status';
    public const PAYMENTS_COL_DIRECTION = 'payments-col-direction';
    public const PAYMENTS_COL_TYPE = 'payments-col-type';
    public const PAYMENTS_COL_DATE = 'payments-col-date';
    public const PAYMENTS_COL_DESC = 'payments-col-desc';
    public const PAYMENTS_COL_PLAYER_DEPOSITS_AGGREGATOR = 'payments-col-player-deposits-aggregator';
    public const PAYMENTS_COL_PLAYER_DEPOSITS_AGGREGATOR_ID = 'payments-col-player-deposits-aggregator-id';
    public const PAYMENTS_COL_PAYMENT_SYSTEM_NAME = 'payments-col-payment_system_name';
    public const PAYMENTS_COL_PAYMENT_SYSTEM_PROBABILITY = 'payments-col-payment_system_name_probability';
    public const PAYMENTS_AGGREGATOR_EDIT = 'payments-aggregator-edit';
    public const PAYMENTS_PAYMENT_SYSTEM_EDIT = 'payments-payment-system-edit';
    public const PAYMENTS_PAYMENT_SYSTEM_PROBABILITY_EDIT = 'payments-payment-system-probability-edit';
    public const PAYMENTS_PAYMENT_SYSTEMS_ADD = 'payments-payment-system-add';
    public const PAYMENTS_PAYMENT_SYSTEMS_DELETE = 'payments-payment-system-add';
    public const PAYMENTS_PAYMENT_SYSTEMS_DECRYPT = 'payments-payment-system-decrypt';

    public const BETS_LIST = 'bets-list';
    public const BETS_COL_ID = 'bets-col-id';
    public const BETS_COL_HASH = 'bets-col-hash';
    public const BETS_COL_DESC = 'bets-col-desc';
    public const BETS_COL_AMOUNT = 'bets-col-amount';
    public const BETS_COL_STATUS = 'bets-col-status';
    public const BETS_COL_PROFIT = 'bets-col-profit';
    public const BETS_COL_COEF = 'bets-col-coef';
    public const BETS_COL_BET_DATE = 'bets-col-bet-date';
    public const BETS_COL_CALC_DATE = 'bets-col-calc-date';

    public const PROVIDERS_LIST = 'providers-list';
    public const PROVIDERS_COL_ID = 'providers-col-id';
    public const PROVIDERS_COL_COUNT = 'providers-col-count';
    public const PROVIDERS_COL_PROVIDER = 'providers-col-provider';
    public const PROVIDERS_COL_NAME = 'providers-col-name';
    public const PROVIDERS_COL_SUSPENDED = 'providers-col-suspended';
    public const PROVIDERS_COL_SECTION_ID = 'providers-col-section_id';
    public const PROVIDERS_COL_IMAGE = 'providers-col-image';
    public const PROVIDERS_COL_SECTION_TV_GAMES = 'providers-col-tv-games';
    public const PROVIDERS_COL_SECTION_CASINO = 'providers-col-casino';
    public const PROVIDERS_COL_SECTION_LIVE_DEALERS = 'providers-col-live-dealers';
    public const PROVIDERS_COL_SECTION_VIRTUAL_SPORTS = 'providers-col-virtual-sports';

    public const SLOT_PROVIDERS_LOCATION = 'slot-providers-location';

    public const SLOTS_LIST = 'slots-list';
    public const SLOTS_SEARCH_SUBSCRIPTIONS = 'slots-search-subscriptions';
    public const SLOTS_SEARCH_NAME = 'slots-search-name';
    public const SLOTS_COL_ID = 'slots-col-id';
    public const SLOTS_COL_WIN = 'slots-col-win';
    public const SLOTS_COL_LOSS = 'slots-col-loss';
    public const SLOTS_COL_GAME_ID = 'slots-col-game-id';
    public const SLOTS_COL_TRANSACTION = 'slots-col-transaction';
    public const SLOTS_COL_DATA = 'slots-col-data';
    public const SLOTS_COL_PROVIDER = 'slots-col-provider';
    public const SLOTS_COL_EXTERNAL_PROVIDER = 'slots-col-external_provider';
    public const SLOTS_COL_EXTERNAL_ID = 'slots-col-external_id';
    public const SLOTS_COL_INTERNAL_ID = 'slots-col-internal_id';
    public const SLOTS_COL_SLUG = 'slots-col-slug';
    public const SLOTS_COL_IMAGE = 'slots-col-image';
    public const SLOTS_COL_NAME = 'slots-col-name';
    public const SLOTS_COL_CATEGORIES = 'slots-col-categories';
    public const SLOTS_COL_DESCRIPTION = 'slots-col-description';
    public const SLOTS_COL_IS_DESKTOP = 'slots-col-is_desktop';
    public const SLOTS_COL_IS_MOBILE = 'slots-col-is_mobile';
    public const SLOTS_COL_SUSPENDED = 'slots-col-suspended';
    public const SLOTS_COL_ENABLED = 'slots-col-enabled';
    public const SLOTS_COL_SECTION_TV_GAMES = 'slots-col-tv-games';
    public const SLOTS_COL_SECTION_CASINO = 'slots-col-casino';
    public const SLOTS_COL_FRIENDLY_URL = 'slots-col-friendly_url';
    public const SLOTS_SECTION_LIVE_DEALERS = 'slots-col-live-dealers';
    public const SLOTS_SECTION_VIRTUAL_SPORTS = 'slots-col-virtual-sports';


    public const MIRRORS_LIST = 'mirrors-list';
    public const MIRRORS_ADD = 'mirrors-add';
    public const MIRRORS_EDIT = 'mirrors-edit';
    public const MIRRORS_DELETE = 'mirrors-delete';
    public const MIRRORS_COL_URL = 'mirrors-col-url';
    public const MIRRORS_COL_DOMAIN = 'mirrors-col-domain';
    public const MIRRORS_COL_GTM = 'mirrors-col-gtm';
    public const MIRRORS_COL_IS_REDIRECT = 'mirrors-col-is_redirect';
    public const MIRRORS_COL_IS_ACTIVE = 'mirrors-col-is_active';
    public const MIRRORS_COL_WEB_PUSH = 'mirrors-col-web_push';

    public const PROMOCODES_LIST = 'promocodes-list';
    public const PROMOCODES_ADD = 'promocodes-add';
    public const PROMOCODES_EDIT = 'promocodes-edit';
    public const PROMOCODES_COMMENT = 'promocodes-comment';
    public const PROMOCODES_SWITCH = 'promocodes-switch';
    public const PROMOCODES_COL_ID = 'promocodes-col-id';
    public const PROMOCODES_COL_NAME = 'promocodes-col-name';
    public const PROMOCODES_COL_CODE = 'promocodes-col-code';
    public const PROMOCODES_COL_USES = 'promocodes-col-uses';
    public const PROMOCODES_COL_LIMIT = 'promocodes-col-limit';
    public const PROMOCODES_COL_BONUS_BALANCE_COUNT = 'promocodes-col-bonus_balance_count';
    public const PROMOCODES_COL_REAL_BONUS_BALANCE_COUNT = 'promocodes-col-bonus_real_balance_count';
    public const PROMOCODES_COL_BONUS_BALANCE_AMOUNT = 'promocodes-col-bonus_balance_amount';
    public const PROMOCODES_COL_BONUS_REAL_BALANCE_AMOUNT = 'promocodes-col-bonus_real_balance_amount';
    public const PROMOCODES_COL_START_AT = 'promocodes-col-start_at';
    public const PROMOCODES_COL_END_AT = 'promocodes-col-end_at';
    public const PROMOCODES_COL_ACTIVE = 'promocodes-col-active';
    public const PROMOCODES_COL_TYPE = 'promocodes-col-type';

    public const DETERMINING_LIST = 'determining-list';

    public const GET_BONUSES = 'get-bonuses';
    public const UPDATE_BONUSES = 'update-bonuses';

    public const BIG_WINS_PAGE = 'big-wins-page';
    public const BIG_WINS_CASINO = 'big-wins-casino';
    public const BIG_WINS_BETS = 'big-wins-bets';
    public const BIG_WINS_COL_USER_ID = 'big-wins-col-user_id';
    public const BIG_WINS_COL_USER_ID_LINK = 'big-wins-col-user_id-link';
    public const BIG_WINS_COL_GAME_ID = 'big-wins-col-game_id';
    public const BIG_WINS_COL_BET_ID = 'big-wins-col-bet_id';
    public const BIG_WINS_COL_BET_ID_LINK = 'big-wins-col-bet_id-link';
    public const BIG_WINS_COL_SESSION_ID_LINK = 'big-wins-col-session_id-link';
    public const BIG_WINS_COL_SESSION_ID = 'big-wins-col-session_id';
    public const BIG_WINS_COL_DATE = 'big-wins-col-date';
    public const BIG_WINS_COL_BET = 'big-wins-col-bet';
    public const BIG_WINS_COL_WIN = 'big-wins-col-win';
    public const BIG_WINS_COL_CURRENCY = 'big-wins-col-currency';
    public const BIG_WINS_COL_FACTOR = 'big-wins-col-factor';
    public const BIG_WINS_COL_STATUS = 'big-wins-col-status';
    public const BIG_WINS_COL_SUSPENDED = 'big-wins-col-suspended';
    public const BIG_WINS_CLOSE = 'big-wins-col-close';
    public const BIG_WINS_DETAILS = 'big-wins-col-details';
    public const BIG_WINS_PARAMETER = 'big-wins-col-parameter';

    public const FREE_SPINS_PAGE = 'free-spins-page';
    public const FREE_SPINS_ADD = 'free-spins-add';
    public const FREE_SPINS_ACTIONS = 'free-spins-actions';
    public const FREE_SPINS_RESEND = 'free-spins-resend';
    public const FREE_SPINS_RESEND_ALL = 'free-spins-resend-all';
    public const FREE_SPINS_EDIT = 'free-spins-edit';
    public const FREE_SPINS_DELETE = 'free-spins-delete';
    public const FREE_SPINS_COL_ID = 'free-spins-col-id';
    public const FREE_SPINS_COL_NAME = 'free-spins-col-name';
    public const FREE_SPINS_COL_TYPE = 'free-spins-col-type';
    public const FREE_SPINS_COL_PROVIDER = 'free-spins-col-provider';
    public const FREE_SPINS_COL_GAME = 'free-spins-col-game';
    public const FREE_SPINS_COL_COUNT = 'free-spins-col-count';
    public const FREE_SPINS_COL_BET = 'free-spins-col-bet';
    public const FREE_SPINS_COL_CURRENCY = 'free-spins-col-currency';
    public const FREE_SPINS_COL_OPTION = 'free-spins-col-option';
    public const FREE_SPINS_COL_START = 'free-spins-col-start';
    public const FREE_SPINS_COL_EXPIRED = 'free-spins-col-expired';
    public const FREE_SPINS_COL_CREATED = 'free-spins-col-created';
    public const FREE_SPINS_COL_UPDATED = 'free-spins-col-updated';
    public const FREE_SPINS_COL_AUTHOR = 'free-spins-col-author';
    public const FREE_SPINS_COL_UPDATED_BY = 'free-spins-col-updated-by';
    public const FREE_SPINS_COL_STATUS = 'free-spins-col-status';
    public const FREE_SPINS_COL_AUTO_CANCEL_WITHDRAWAL = 'free-spins-col-auto-cancel-withdrawal';

    public const INFO_BONUSES_PAGE = 'info-bonuses-page';
    public const INFO_BONUSES_ADD = 'info-bonuses-add';
    public const INFO_BONUSES_EDIT = 'info-bonuses-edit';
    public const INFO_BONUSES_CLONE = 'info-bonuses-clone';
    public const INFO_BONUSES_SHOW = 'info-bonuses-show';
    public const INFO_BONUSES_ACTIONS = 'info-bonuses-actions';
    public const INFO_BONUSES_COL_ID = 'info-bonuses-col-id';
    public const INFO_BONUSES_COL_NAME = 'info-bonuses-col-name';
    public const INFO_BONUSES_COL_CHAPTER = 'info-bonuses-col-chapter';
    public const INFO_BONUSES_COL_CURRENCY = 'info-bonuses-col-currency';
    public const INFO_BONUSES_COL_BUTTON = 'info-bonuses-col-button';
    public const INFO_BONUSES_COL_SUBSEQUENCE = 'info-bonuses-col-subsequence';
    public const INFO_BONUSES_COL_CREATED = 'info-bonuses-col-created';
    public const INFO_BONUSES_COL_UPDATED = 'info-bonuses-col-updated';
    public const INFO_BONUSES_COL_VISIBLE_FROM = 'info-bonuses-col-visible-from';
    public const INFO_BONUSES_COL_VISIBLE_TO = 'info-bonuses-col-visible-to';
    public const INFO_BONUSES_COL_ACTIVE = 'info-bonuses-col-active';

    public const UNSUCCESSFUL_DEPOSITS_PAGE = 'unsuccessful-deposits-page';
    public const UNSUCCESSFUL_DEPOSITS_COL_CREATED_AT = 'unsuccessful-deposits-col-created_at';
    public const UNSUCCESSFUL_DEPOSITS_COL_UUID = 'unsuccessful-deposits-col-uuid';
    public const UNSUCCESSFUL_DEPOSITS_COL_SUCCESSFUL_DEPOSITS = 'unsuccessful-deposits-col-successful_deposits';
    public const UNSUCCESSFUL_DEPOSITS_COL_UNSUCCESSFUL_DEPOSITS = 'unsuccessful-deposits-col-unsuccessful_deposits';
    public const UNSUCCESSFUL_DEPOSITS_COL_REGISTRATION_DATE = 'unsuccessful-deposits-col-registration_date';
    public const UNSUCCESSFUL_DEPOSITS_COL_PAYMENT_SYSTEM = 'unsuccessful-deposits-col-payment_system';
    public const UNSUCCESSFUL_DEPOSITS_COL_PAYMENT_METHOD = 'unsuccessful-deposits-col-payment_method';
    public const UNSUCCESSFUL_DEPOSITS_COL_AMOUNT = 'unsuccessful-deposits-col-amount';
    public const UNSUCCESSFUL_DEPOSITS_COL_PHONE = 'unsuccessful-deposits-col-phone';
    public const UNSUCCESSFUL_DEPOSITS_COL_EMAIL = 'unsuccessful-deposits-col-email';
    public const UNSUCCESSFUL_DEPOSITS_COL_TAG = 'unsuccessful-deposits-col-tag';
    public const UNSUCCESSFUL_DEPOSITS_COL_IS_LAST = 'unsuccessful-deposits-col-is_last';
    public const UNSUCCESSFUL_DEPOSITS_COL_IS_ACTUAL = 'unsuccessful-deposits-col-is_actual';
    public const UNSUCCESSFUL_DEPOSITS_COL_ACTUAL_BUTTON = 'unsuccessful-deposits-button-actual_button';
    public const UNSUCCESSFUL_DEPOSITS_COL_METHOD = 'unsuccessful-deposits-col-method';
    public const UNSUCCESSFUL_DEPOSITS_FILTER_SHOW_WITH = 'unsuccessful-deposits-filter-show_with';
    public const UNSUCCESSFUL_DEPOSITS_FILTER_TRIGGER = 'unsuccessful-deposits-filter-trigger';
    public const UNSUCCESSFUL_DEPOSITS_COL_TRIGGER = 'unsuccessful-deposits-col-trigger';
    public const UNSUCCESSFUL_DEPOSITS_FILTER_PAYMENT_SYSTEM = 'unsuccessful-deposits-filter-payment_system';
    public const UNSUCCESSFUL_DEPOSITS_FILTER_PAYMENT_METHOD = 'unsuccessful-deposits-filter-payment_method';
    public const UNSUCCESSFUL_DEPOSITS_FILTER_METHOD = 'unsuccessful-deposits-filter-method';
    public const UNSUCCESSFUL_DEPOSITS_FILTER_TAGS = 'unsuccessful-deposits-filter-tags';
    public const UNSUCCESSFUL_DEPOSITS_FILTER_IS_ACTUAL = 'unsuccessful-deposits-filter-is_actual';

    public const BALANCE_GROWTH_PAGE = 'balance-growth-page';
    public const BALANCE_GROWTH_CASINO = 'balance-growth-casino';
    public const BALANCE_GROWTH_BETS = 'balance-growth-bets';
    public const BALANCE_GROWTH_PARAMETER = 'balance-growth-parameter';
    public const BALANCE_GROWTH_COL_USER_ID = 'balance-growth-col-user_id';
    public const BALANCE_GROWTH_USER_ID_LINK = 'balance-growth-col-user_id-link';
    public const BALANCE_GROWTH_COL_CURRENCY = 'balance-growth-col-currency';
    public const BALANCE_GROWTH_COL_STATUS = 'balance-growth-col-status';
    public const BALANCE_GROWTH_COL_SUSPENDED = 'balance-growth-col-suspended';
    public const BALANCE_GROWTH_CLOSE = 'balance-growth-col-close';
    public const BALANCE_GROWTH_DETAILS = 'balance-growth-col-details';
    public const BALANCE_GROWTH_COL_LAST_DEP = 'balance-growth-col-last-dep';
    public const BALANCE_GROWTH_COL_LAST_DEP_SUM = 'balance-growth-col-last-dep-sum';
    public const BALANCE_GROWTH_COL_TRIGGER = 'balance-growth-col-trigger';
    public const BALANCE_GROWTH_COL_TRIGGER_PARAMETER = 'balance-growth-col-trigger-parameter';
    public const BALANCE_GROWTH_COL_TRIGGER_BALANCE = 'balance-growth-col-trigger-balance';
    public const BALANCE_GROWTH_COL_BALANCE = 'balance-growth-col-balance';


    public const BONUSES_LIST = 'bonuses-list';
    public const BONUSES_ADD = 'bonuses-add';
    public const BONUSES_EDIT = 'bonuses-edit';
    public const BONUSES_FILTER = 'bonuses-filter';
    public const BONUSES_CLONE = 'bonuses-clone';
    public const BONUSES_DETAILS = 'bonuses-details';
    public const BONUSES_SEARCH = 'bonuses-search';
    public const BONUSES_COL_ID = 'bonuses-col-id';
    public const BONUSES_COL_NAME = 'bonuses-col-name';
    public const BONUSES_COL_MAX_REAL_BALANCE = 'bonuses-col-max_real_balance';
    public const BONUSES_COL_IMAGE = 'bonuses-col-image';
    public const BONUSES_COL_MAX_TRANSFER = 'bonuses-col-max_transfer';
    public const BONUSES_COL_MAX_BONUS = 'bonuses-col-max_bonus';
    public const BONUSES_COL_MIN_BET = 'bonuses-col-min_bet';
    public const BONUSES_COL_CURRENCY = 'bonuses-col-currency';
    public const BONUSES_COL_WAGER = 'bonuses-col-wager';
    public const BONUSES_COL_MIN_FACTOR = 'bonuses-col-min_factor';
    public const BONUSES_COL_START_AT = 'bonuses-col-start_at';
    public const BONUSES_COL_END_AT = 'bonuses-col-end_at';
    public const BONUSES_COL_DURATION = 'bonuses-col-duration';
    public const BONUSES_COL_ACTIVE = 'bonuses-col-active';
    public const BONUSES_COL_BONUS = 'bonuses-col-bonus';
    public const BONUSES_COL_PROVIDERS = 'bonuses-col-providers';
    public const BONUSES_COL_SLOTS = 'bonuses-col-slots';
    public const BONUSES_COL_AUTO_CANCEL_WITHDRAWAL = 'bonuses-col-auto-cancel-withdrawal';
    public const BONUSES_VIEW_CONTRIBUTIONS = 'bonuses-view-contributions';
    public const BONUSES_EDIT_CONTRIBUTIONS = 'bonuses-edit-contributions';
    public const BONUSES_BULK_EDIT_CONTRIBUTIONS = 'bonuses-bulk-edit-contributions';

    public const STATISTICS_PAGE = 'statistics-page';
    public const STATISTICS_CASINO_PAGE = 'statistics-casino-page';
    public const STATISTICS_SEARCH = 'statistics-search';
    public const STATISTICS_EXPORT = 'statistics-export';
    public const STATISTICS_COL_UUID = 'statistics-col-uuid';
    public const STATISTICS_COL_PLAYER_ID = 'statistics-col-player_id';
    public const STATISTICS_COL_TOTAL_BET = 'statistics-col-total_bet';
    public const STATISTICS_COL_TOTAL_WINS = 'statistics-col-total_wins';
    public const STATISTICS_COL_PROFITABILITY = 'statistics-col-profitability';
    public const STATISTICS_COL_CURRENCY = 'statistics-col-currency';

    public const PROFILE_MODERATORS_LIST = 'profile-moderators-page';
    public const PROFILE_MODERATORS_EDIT = 'profile-moderators-edit';
    public const PROFILE_MODERATORS_ID = 'profile-moderators-id';
    public const PROFILE_MODERATORS_NAME = 'profile-moderators-name';
    public const PROFILE_MODERATORS_SURNAME = 'profile-moderators-surname';
    public const PROFILE_MODERATORS_BIRTHDAY = 'profile-moderators-birthday';
    public const PROFILE_MODERATORS_GENDER = 'profile-moderators-gender';
    public const PROFILE_MODERATORS_COUNTRY = 'profile-moderators-country';
    public const PROFILE_MODERATORS_CITY = 'profile-moderators-city';
    public const PROFILE_MODERATORS_PHONE = 'profile-moderators-phone';
    public const PROFILE_MODERATORS_STATUS = 'profile-moderators-status';
    public const PROFILE_MODERATORS_MODERATOR = 'profile-moderators-moderator';
    public const PROFILE_MODERATORS_CREATED_AT = 'profile-moderators-created_at';
    public const PROFILE_MODERATORS_ACTION_AT = 'profile-moderators-action_at';

    public const GET_SLOTS = 'get-slots';
    public const UPDATE_SLOTS = 'update-slots';

    public const COMMENT_ASSIGN = 'comment-player';
    public const GET_PROMOCODE = 'get-promocode';
    public const UPDATE_PROMOCODE = 'update-promocode';
    public const GET_GATEWAY = 'get-gateway';
    public const UPDATE_GATEWAY = 'update-gateway';
    public const ADMIN = 'admin';

    public const NOTIFICATIONS_LIST = 'notifications-list';
    public const NOTIFICATIONS_ADD = 'notifications-add';
    public const NOTIFICATIONS_EDIT = 'notifications-edit';
    public const NOTIFICATIONS_DELETE = 'notifications-delete';

    public const SEGMENTS_LIST = 'segments-list';
    public const CORE_CLUSTERS_LIST = 'core-clusters-list';
    public const CORE_CLUSTERS_SELECT = 'core-clusters-select';

    public const SLOTS_BETS_HISTORY_ADMIN = 'slots-bets-admin';

    public const SMART_LINK_ADMIN_UPDATE_VIEW = 'smart-links-admin-update-view';
    public const SMART_LINK_ADMIN_HISTORY = 'smart-links-admin-history-view';
    public const SMART_LINK_ADMIN_MENU_DROPDOWN = 'smart-links-admin-menu-dropdown';
    public const SMART_LINK_ADMIN_PAGE_VIEW = 'smart-links-admin-page-view';

//    public const GET_ROLE = 'get-role';
//    public const CREATE_ROLE = 'create-role';
//    public const UPDATE_ROLE = 'update-role';
//    public const DELETE_ROLE = 'delete-role';
//    public const GET_BANNERS = 'get-banner';
//    public const CREATE_BANNERS = 'create-banner';
//    public const UPDATE_BANNERS = 'update-banner';
//    public const DELETE_BANNERS = 'delete-banner';
//    public const GET_PAGE = 'get-page';
//    public const CREATE_PAGE = 'create-page';
//    public const UPDATE_PAGE = 'update-page';
//    public const DELETE_PAGE = 'delete-page';
//    public const GET_PLAYER = 'get-player';
//    public const SEARCH_PLAYER = 'search-player';
//    public const UPDATE_PLAYER = 'update-player';
//    public const GET_USER = 'get-user';
//    public const CREATE_USER = 'create-user';
//    public const UPDATE_USER = 'update-user';
//    public const DELETE_USER = 'delete-user';
//    public const GET_LANGUAGES = 'get-language';
//    public const UPDATE_LANGUAGES = 'update-language';
//    public const GET_DEPOSIT = 'get-deposit';
//    public const UPDATE_COUNTRY = 'update-country';
//    public const GET_CURRENCIES = 'get-currency';
//    public const UPDATE_CURRENCY = 'update-currency';
//    public const GET_PAYOUT = 'get-payout';
//    public const UPDATE_PAYOUT = 'update-payout';
//    public const GET_PAYMENTS = 'get-payment';
//    public const GET_BONUSES = 'get-bonuses';
//    public const UPDATE_BONUSES = 'update-bonuses';
//    public const GET_SLOTS = 'get-slots';
//    public const UPDATE_SLOTS = 'update-slots';
}
