<?php
namespace WhiteLabelAdmin\Entities;

use <PERSON>pper\Repository\Contracts\Entity;
use Skipper\Repository\Utilities\HasId;
use OpenApi\Annotations as OA;

class Role implements Entity, \JsonSerializable
{
    use HasId;

    private string $name;
    private int $clientId;
    private array $permissions;

    public function __construct(int $clientId, string $name, array $permissions)
    {
        $this->name = $name;
        $this->permissions = $permissions;
        $this->clientId = $clientId;
    }

    /**
     * @param string $name
     * @return Role
     */
    public function setName(string $name): Role
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string[]
     */
    public function getPermissions(): array
    {
        return $this->permissions;
    }

    /**
     * @param string $permission
     * @return bool
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->getPermissions());
    }

    /**
     * @return array
     * @OA\Schema(schema="role", type="object", properties={
     *     @OA\Property(property="id", type="integer"),
     *     @OA\Property(property="name", type="string", description="role name"),
     *     @OA\Property(property="permissions", type="array", description="role permissions", @OA\Items(type="string")),
     * })
     */
    public function jsonSerialize()
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'permissions' => $this->getPermissions(),
        ];
    }
}
