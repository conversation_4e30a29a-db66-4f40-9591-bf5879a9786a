<?php
namespace WhiteLabelAdmin\Entities;

use <PERSON>pper\Repository\Contracts\Entity;
use Skipper\Repository\Utilities\HasId;
use OpenApi\Annotations as OA;

class Player implements Entity, \JsonSerializable
{
    use HasId;

    private array $data;
    private int $clientId;
    private array $updated;

    public function __construct(array $data)
    {
        $this->data = $data;
        $this->clientId = $data['client_id'];
        $this
            ->setUpdated([])
            ->setId($data['id']);
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @param array $data
     * @return Player
     */
    public function setNewData(array $data): Player
    {
        $this->data = $data;

        return $this;
    }

    /**
     * @return array
     */
    public function getUpdated(): array
    {
        return $this->updated;
    }

    /**
     * @param array $updated
     * @return Player
     */
    public function setUpdated(array $updated): Player
    {
        $this->updated = $updated;

        return $this;
    }

    /**
     * @return array
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * @return array
     * @OA\Schema(schema="money", description="money object", type="object", properties={
     *     @OA\Property(property="amount", description="money amount in cents", type="integer"),
     *     @OA\Property(property="currency", description="currency iso2 code", type="string")
     * })
     * @OA\Schema(schema="player", description="player object", type="object", properties={
     *     @OA\Property(property="id", type="integer", minimum=1, description="player's id"),
     *     @OA\Property(property="client_id", type="integer", minimum=1, description="player's client id"),
     *     @OA\Property(property="info", properties={
     *          @OA\Property(property="first_name", description="player's first name", type="string"),
     *          @OA\Property(property="last_name", description="player's last name", type="string", nullable=true),
     *          @OA\Property(property="email", description="player's email", type="string", format="email"),
     *          @OA\Property(property="phone", description="player's phone", type="string", nullable=true),
     *          @OA\Property(property="birth", description="player's birthday", type="string", nullable=true, format="date"),
     *          @OA\Property(property="country", description="player's country", type="string", nullable=true),
     *          @OA\Property(property="city", description="player's city", type="string", nullable=true),
     *          @OA\Property(property="gender", description="player's gender", type="string", nullable=true, enum={"male", "female"}),
     *     }),
     *     @OA\Property(property="balance", nullable=true, properties={
     *          @OA\Property(property="balance", description="player's current balance", ref="#/components/schemas/money"),
     *          @OA\Property(property="total_deposit", description="player's total deposit amount", ref="#/components/schemas/money"),
     *          @OA\Property(property="wager", description="player's wager balance", ref="#/components/schemas/money"),
     *          @OA\Property(property="withdraw_limit", description="player's withdraw limit", ref="#/components/schemas/money"),
     *          @OA\Property(property="in_game", description="how much money does player have in bets", ref="#/components/schemas/money"),
     *          @OA\Property(property="total_payout", description="player's payouts of all time", ref="#/components/schemas/money"),
     *          @OA\Property(property="currency_payout", description="amount to be paid out", ref="#/components/schemas/money"),
     *          @OA\Property(property="total_bet", description="total bets amount", ref="#/components/schemas/money"),
     *          @OA\Property(property="total_profit", description="total money lost", ref="#/components/schemas/money")
     *     }),
     *     @OA\Property(property="meta", properties={
     *          @OA\Property(property="last_seen_ip", type="string", format="ipv4", description="player's ip during last login"),
     *          @OA\Property(property="blocked", type="boolean", description="is player's blocked"),
     *          @OA\Property(property="casino_access", type="boolean", description="is player's has access to casino"),
     *          @OA\Property(property="registered_at", type="integer", description="when player has joined"),
     *          @OA\Property(property="last_seen_at", type="integer", description="player's last action at"),
     *          @OA\Property(property="comment", type="string", description="player's comment assigned by system/admin"),
     *          @OA\Property(property="is_suspicious", type="boolean", description="is player suspicious"),
     *          @OA\Property(property="is_fake", type="boolean", description="is fake player"),
     *     }),
     * })
     */
    public function jsonSerialize()
    {
        return $this->getData();
    }
}
