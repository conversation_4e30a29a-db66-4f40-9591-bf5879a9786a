<?php
namespace WhiteLabelAdmin\Entities;

use Skipper\Repository\Contracts\Entity;
use Skipper\Repository\Utilities\HasId;

final class Country implements Entity, \JsonSerializable
{
    use HasId;

    private string $isoCode;
    private int $clientId;
    private bool $isAccessible;

    public function __construct(string $isoCode, int $clientId)
    {
        $this->isoCode = $isoCode;
        $this->clientId = $clientId;
    }

    /**
     * @return string
     */
    public function getIsoCode(): string
    {
        return $this->isoCode;
    }

    /**
     * @return int
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * @return bool
     */
    public function isAccessible(): bool
    {
        return $this->isAccessible;
    }

    /**
     * @param bool $isAccessible
     * @return Country
     */
    public function setIsAccessible(bool $isAccessible): Country
    {
        $this->isAccessible = $isAccessible;

        return $this;
    }

    /**
     * @return array
     * @OA\Schema(schema="country", description="country object", type="object", properties={
     *     @OA\Property(property="iso_code", description="country's iso code", type="string"),
     *     @OA\Property(property="client_id", description="user client", type="integer")
     * })
     */
    public function jsonSerialize()
    {
        return [
            'iso_code' => $this->getIsoCode(),
            'client_id' => $this->getClientId()
        ];
    }
}
