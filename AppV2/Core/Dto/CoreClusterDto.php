<?php

namespace AppV2\Core\Dto;

class CoreClusterDto
{
    private string $uuid;
    private string $name;

    public function __construct(
        string $slug,
        string $name
    ) {
        $this->uuid = $slug;
        $this->name = $name;
    }

    public function getSlug(): string
    {
        return $this->uuid;
    }

    public function getName(): string
    {
        return $this->name;
    }
}
