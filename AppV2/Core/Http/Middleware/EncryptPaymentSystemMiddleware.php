<?php declare(strict_types=1);

namespace AppV2\Core\Http\Middleware;

use AppV2\Core\Services\PaymentSystemEncryptService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use White<PERSON>abelAdmin\Entities\PermissionList;

class EncryptPaymentSystemMiddleware
{
    private const DEPOSITS_PATH = 'api/v2/api/v1/deposits';
    private const PAYOUTS_PATH = 'api/v2/api/v3/payouts';

    private const METHODS_TO_CHANGE_PATH = 'api/v1/admin/withdraw/orders/methods_to_change';
    private const FAILED_DEPOSIT_ATTEMPTS_PATH = 'api/v1/payment/deposits/_ID_/failed';

    private const ROUTE_PERMISSION_MAP = [
        self::DEPOSITS_PATH => ['name' => PermissionList::PAYMENTS_COL_PLAYER_DEPOSITS_AGGREGATOR, 'id' => PermissionList::PAYMENTS_COL_PLAYER_DEPOSITS_AGGREGATOR_ID],
        self::PAYOUTS_PATH => ['name' => PermissionList::PAYOUTS_COL_AGGREGATOR_DECRYPTED, 'id' => PermissionList::PAYOUTS_COL_AGGREGATOR_ID],
        self::METHODS_TO_CHANGE_PATH => ['name' => PermissionList::PAYOUTS_COL_AGGREGATOR_DECRYPTED, 'id' => PermissionList::PAYOUTS_COL_AGGREGATOR_ID],
        self::FAILED_DEPOSIT_ATTEMPTS_PATH => ['name' => PermissionList::DEPOSITS_COL_FAILED_ATTEMPTS_AGGREGATOR, 'id' => PermissionList::DEPOSITS_COL_FAILED_ATTEMPTS_AGGREGATOR_ID],
    ];

    private ?string $route = null;

    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        if ($response instanceof Response && $response->isOk()) {
            $requestPath = $request->path();
            $perms = $this->setupRouteAndReturnPermissions($requestPath);

            if ($perms) {
                $this->prepareResponse(
                    $response,
                    $request->user()->can($perms['name']),
                    $request->user()->can($perms['id']),
                );
            }
        }

        return $response;
    }

    private function prepareResponse(
        Response $response,
        bool $canViewAggregator,
        bool $canViewAggregatorId
    ): void {
        $content = json_decode($response->content(), true);

        $this->getHandler()($content, $canViewAggregator, $canViewAggregatorId);

        $response->setContent(json_encode($content));
    }

    private function getPaymentSystemEncryptService(): PaymentSystemEncryptService
    {
        return app(PaymentSystemEncryptService::class);
    }

    private function getHandler(): callable
    {
        switch ($this->route) {
            case self::DEPOSITS_PATH:
                $result = $this->getDepositsHandler();
                break;

            case self::PAYOUTS_PATH:
                $result = $this->getPayoutsHandler();
                break;

            case self::METHODS_TO_CHANGE_PATH:
                $result = $this->getMethodToChangeHandler();
                break;

            case self::FAILED_DEPOSIT_ATTEMPTS_PATH:
                $result = $this->getFailedDepositAttemptsHandler();
                break;

            default:
                $result = function (array &$content, bool $canViewAggregator, bool $canViewAggregatorId) {
                };
        }

        return $result;
    }

    private function getDepositsHandler(): callable
    {
        return function (array &$content, bool $canViewAggregator, bool $canViewAggregatorId) {
            foreach ($content['data'] as $i => $item) {
                $content['data'][$i]['details']['payment_system_id'] = '';

                if ($canViewAggregatorId) {
                    $aggregator = $content['data'][$i]['details']['payment_system'] ?? null;
                    if ($aggregator) {
                        $content['data'][$i]['details']['payment_system_id'] = $this->getPaymentSystemEncryptService()->encryptName(
                            $aggregator
                        );
                    }
                }

                if (!$canViewAggregator) {
                    $content['data'][$i]['details']['payment_system'] = '';
                }
            }
        };
    }

    private function getPayoutsHandler(): callable
    {
        return function (array &$content, bool $canViewAggregator, bool $canViewAggregatorId) {
            foreach ($content['data'] as $i => $item) {
                $content['data'][$i]['aggregator_id'] = '';

                if ($canViewAggregatorId) {
                    $aggregator = $content['data'][$i]['aggregator'] ?? null;
                    if ($aggregator) {
                        $content['data'][$i]['aggregator_id'] = $this->getPaymentSystemEncryptService()->encryptName(
                            $aggregator
                        );
                    }
                }

                if (!$canViewAggregator) {
                    $content['data'][$i]['aggregator'] = '';
                }
            }
        };
    }

     private function getMethodToChangeHandler(): callable
     {
         return function (array &$content, bool $canViewAggregator, bool $canViewAggregatorId) {
             foreach ($content as $i => $item) {
                 $content[$i]['aggregator_id'] = '';
                 $content[$i]['aggregator']['id'] = '';

                 if ($canViewAggregatorId) {
                     $aggregator = $content[$i]['aggregator']['name'] ?? null;
                     if ($aggregator) {
                         $aggregatorEncrypted = $this->getPaymentSystemEncryptService()->encryptName(
                             $aggregator
                         );
                         $content[$i]['aggregator_id'] = $aggregatorEncrypted;
                         $content[$i]['aggregator']['id'] = $aggregatorEncrypted;
                     }
                 }

                 if (!$canViewAggregator) {
                     $content[$i]['aggregator']['name'] = '';
                 }
             }
         };
     }

    private function getFailedDepositAttemptsHandler(): callable
    {
        return function (array &$content, bool $canViewAggregator, bool $canViewAggregatorId) {
            foreach ($content['data'] as $i => $item) {
                $content['data'][$i]['payment_aggregator_id'] = '';

                if ($canViewAggregatorId) {
                    $aggregator = $content['data'][$i]['payment_aggregator'] ?? null;
                    if ($aggregator) {
                        $content['data'][$i]['payment_aggregator_id'] = $this->getPaymentSystemEncryptService()->encryptName(
                            $aggregator
                        );
                    }
                }

                if (!$canViewAggregator) {
                    $content['data'][$i]['payment_aggregator'] = '';
                }
            }
        };
    }

    private function setupRouteAndReturnPermissions(string $requestPath): array
    {
        $perms = self::ROUTE_PERMISSION_MAP[$requestPath] ?? [];

        if (!$perms) {
            $parts = explode('_ID_', self::FAILED_DEPOSIT_ATTEMPTS_PATH);
            if (is_array($parts) && count($parts) === 2) {
                if (Str::startsWith($requestPath, $parts[0]) && Str::endsWith($requestPath, $parts[1])) {
                    $this->route = self::FAILED_DEPOSIT_ATTEMPTS_PATH;
                    $perms = self::ROUTE_PERMISSION_MAP[$this->route] ?? [];
                }
            }
        } else {
            $this->route = $requestPath;
        }

        return $perms;
    }
}
