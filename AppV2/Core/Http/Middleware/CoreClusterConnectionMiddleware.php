<?php declare(strict_types=1);

namespace AppV2\Core\Http\Middleware;

use AppV2\Core\Http\Auth\ClusterConnectionGrant;
use Closure;
use Illuminate\Http\Request;

class CoreClusterConnectionMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $request->merge(['grant_type' => ClusterConnectionGrant::NAME]);
        $request->attributes->add(['cluster_connection' => $request->get('cluster_connection')]);

        return $next($request);
    }
}
