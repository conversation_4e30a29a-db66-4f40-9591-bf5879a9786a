<?php

namespace AppV2\Core\Http\Api;

use App\Services\HttpService;
use AppV2\Core\Dto\CoreClusterDto;
use IntegratorCore\Services\Dto\ResponseOutputDto;
use Symfony\Component\HttpKernel\Exception\HttpException;

class CoreClusterGatewayApiClient extends HttpService
{
    protected bool $logging = true;

    /**
     * @return CoreClusterDto[]
     */
    public function getActiveClusters(): array
    {
        $response = $this->doGetRequest('/api/cluster/list');
        $clusters = [];
        foreach ($response->content['data'] as $cluster) {
            if (!$cluster['is_active']) {
                continue;
            }
            $clusters[] = new CoreClusterDto($cluster['slug'], $cluster['name']);
        }

        return $clusters;
    }

    protected function doRequest(string $requestType, string $url): ResponseOutputDto
    {
        $response = parent::doRequest($requestType, $url);
        if (!$response->isSuccess) {
            throw new HttpException($response->responseCode, $response->rawContent);
        }

        return $response;
    }
}
