<?php declare(strict_types=1);

namespace AppV2\Core\Http\Controllers;

use App\Http\Controllers\Controller;
use AppV2\Core\Http\Resources\CoreClusterResource;
use AppV2\Core\Services\ClusterConnectionService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

final class CoreClusterConnectionController extends Controller
{
    private ClusterConnectionService $clusterConnectionService;

    public function __construct(ClusterConnectionService $clusterConnectionService)
    {
        $this->clusterConnectionService = $clusterConnectionService;
    }

    public function getClusters(): AnonymousResourceCollection
    {
        return CoreClusterResource::collection($this->clusterConnectionService->getClusters());
    }
}
