<?php declare(strict_types=1);

namespace AppV2\Core\Http\Controllers;

use AppV2\Core\Http\Auth\ClusterConnectionGrant;
use AppV2\Core\Services\ClusterConnectionService;
use Dusterio\LumenPassport\Http\Controllers\AccessTokenController;
use Dusterio\LumenPassport\LumenPassport;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Laminas\Diactoros\Response as Psr7Response;
use <PERSON><PERSON>\Lumen\Routing\ProvidesConvenienceMethods;
use Laravel\Passport\Bridge\RefreshTokenRepository;
use Lara<PERSON>\Passport\Bridge\UserRepository;
use <PERSON><PERSON>\Passport\Passport;
use Lara<PERSON>\Passport\Token;
use Psr\Http\Message\ServerRequestInterface;

final class CoreClusterAccessTokenController extends AccessTokenController
{
    use ProvidesConvenienceMethods;

    public function issueTokenWithCluster(
        ServerRequestInterface $request,
        Request $validationRequest,
        ClusterConnectionService $clusterConnectionService
    ) {
        $this->validate(
            $validationRequest,
            [
                'cluster_connection' => [
                    'string',
                    'required',
                    Rule::in($clusterConnectionService->getClusterSlugList())
                ],
            ]
        );

        $response = $this->withErrorHandling(function () use ($request) {
            $input = (array) $request->getParsedBody();
            $clientId = isset($input['client_id']) ? $input['client_id'] : null;

            // Overwrite password grant at the last minute to add support for customized TTLs
            $this->server->enableGrantType(
                $this->makeCustomGrant(), LumenPassport::tokensExpireIn(null, $clientId)
            );

            return $this->server->respondToAccessTokenRequest($request, new Psr7Response);
        });

        if ($response->getStatusCode() < 200 || $response->getStatusCode() > 299) {
            return $response;
        }

        $payload = json_decode($response->getBody()->__toString(), true);

        if (isset($payload['access_token'])) {
            /* @deprecated the jwt property will be removed in a future Laravel Passport release */
            $token = $this->jwt->parse($payload['access_token']);
            if (method_exists($token, 'getClaim')) {
                $tokenId = $token->getClaim('jti');
            } else if (method_exists($token, 'claims')) {
                $tokenId = $token->claims()->get('jti');
            } else {
                throw new \RuntimeException('This package is not compatible with the Laravel Passport version used');
            }

            $token = $this->tokens->find($tokenId);
            if (!$token instanceof Token) {
                return $response;
            }

            if ($token->client->firstParty() && LumenPassport::$allowMultipleTokens) {
                // We keep previous tokens for password clients
            } else {
                $this->revokeOrDeleteAccessTokens($token, $tokenId);
            }
        }

        return $response;
    }

    private function makeCustomGrant(): ClusterConnectionGrant
    {
        $grant = new ClusterConnectionGrant(
            app()->make(UserRepository::class),
            app()->make(RefreshTokenRepository::class)
        );

        $grant->setRefreshTokenTTL(Passport::refreshTokensExpireIn());

        return $grant;
    }
}
