<?php

namespace AppV2\Core\Services;

use Illuminate\Contracts\Encryption\DecryptException;

class PaymentSystemEncryptService
{
    public function encryptName(string $name): string
    {
        return app_encode($name);
    }

    public function decryptName(string $encryptedName): string
    {
        $result = app_decode($encryptedName);

        if (!ctype_print($result)) {
            throw new DecryptException();
        }

        return $result;
    }
}
