<?php

namespace AppV2\Core\Services;

use AppV2\Core\Dto\CoreClusterDto;
use AppV2\Core\Http\Api\CoreClusterGatewayApiClient;
use Illuminate\Support\Facades\Cache;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Parser;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Token\Plain;

class ClusterConnectionService
{
    public const CLUSTER_CONNECTION_HEADER = 'X-CLUSTER-CONNECTION';
    private const ACTIVE_CLUSTERS_CACHE_KEY = 'clusters_active';
    private const CLUSTERS_CACHE_TTL = 60 * 60;

    private CoreClusterGatewayApiClient $coreClusterGatewayApiClient;
    private bool $slugSet = false;
    private ?string $slug;
    private Parser $jwtParser;

    public function __construct(
        CoreClusterGatewayApiClient $coreClusterGatewayApiClient,
        Parser $jwtParser
    ) {
        $this->coreClusterGatewayApiClient = $coreClusterGatewayApiClient;
        $this->jwtParser = $jwtParser;
    }

    public function getClusters(bool $cache = true): array
    {
        return $this->retrieveClusters($cache);
    }

    public function getClusterSlugList(bool $cache = true): array
    {
        return $this->convertToSlugArray($this->retrieveClusters($cache));
    }

    public function getUserClusterConnection(): ?string
    {
        if ($this->slugSet) {
            return $this->slug;
        } else {
            $this->slugSet = true;
            $this->slug = null;
        }

        $request = request();
        $bearer = $request->bearerToken();
        if (!$bearer) {
            return $this->slug;
        }
        /** @var Plain $token */
        $token = $this->jwtParser->parse($bearer);
        $clusterSlug = $token->claims()->get('cc');
        if ($clusterSlug) {
            $this->slug = $clusterSlug;
        }

        return $this->slug;
    }

    private function retrieveClusters(bool $cache): array
    {
        if (!$cache) {
            return $this->coreClusterGatewayApiClient->getActiveClusters();
        }

        return Cache::remember(self::ACTIVE_CLUSTERS_CACHE_KEY, self::CLUSTERS_CACHE_TTL, function () {
            return $this->coreClusterGatewayApiClient->getActiveClusters();
        });
    }

    private function convertToSlugArray(array $clusters): array
    {
        return array_map(function (CoreClusterDto $dto) {
            return $dto->getSlug();
        }, $clusters);
    }
}
