<?php declare(strict_types=1);

namespace AppV2\Core\Traits;

use Illuminate\Http\UploadedFile;
use SplFileObject;

trait FileSeparator
{
    /**
     * @param UploadedFile $uploadedFile
     * @return string|null
     */
    public static function getSeparator(UploadedFile $uploadedFile): ?string
    {
        foreach ([';', ','] as $separator) {
            $result = static::getHeader($separator, $uploadedFile);
            if (count($result) > 1) {
                $successSeparator = $separator;
                break;
            }
        }

        return $successSeparator ?? null;
    }

    /**
     * @param string $separator
     * @param UploadedFile $uploadedFile
     * @return array|false|null
     */
    public static function getHeader(string $separator, UploadedFile $uploadedFile)
    {
        $file = new SplFileObject($uploadedFile->getRealPath());
        $file->setCsvControl();
        $file->setFlags(SplFileObject::READ_CSV);

        return $file->fgetcsv($separator);
    }
}
