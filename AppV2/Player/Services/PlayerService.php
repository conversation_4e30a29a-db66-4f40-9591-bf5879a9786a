<?php declare(strict_types=1);

namespace AppV2\Player\Services;

use AppV2\Player\Models\Player;
use AppV2\Player\Repositories\PlayerRepository;

class PlayerService
{
    private PlayerRepository $playerRepository;

    public function __construct(PlayerRepository $playerRepository)
    {
        $this->playerRepository = $playerRepository;
    }

    public function getPlayerByEmail(string $email): Player
    {
        return $this->playerRepository->getPlayerByEmail($email);
    }

    public function getPlayerByUUID(string $uuid): Player
    {
        return $this->playerRepository->getPlayerByUUID($uuid);
    }

    public function getPlayerByID(int $id): Player
    {
        return $this->playerRepository->getPlayerByID($id);
    }
}
