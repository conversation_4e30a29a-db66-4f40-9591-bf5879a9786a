<?php declare(strict_types=1);

namespace AppV2\Player\Http\Controllers;

use App\Http\Controllers\Controller;
use AppV2\Player\Http\ApiClients\VerificationApiClient;
use AppV2\Player\Http\Resources\VerificationTemplateResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

final class PlayerVerificationController extends Controller
{
    private VerificationApiClient $verificationApiClient;

    public function __construct(VerificationApiClient $verificationApiClient)
    {
        $this->verificationApiClient = $verificationApiClient;
    }

    public function getTemplates(Request $request): AnonymousResourceCollection
    {
        return VerificationTemplateResource::collection($this->verificationApiClient->getTemplates());
    }
}
