<?php declare(strict_types=1);
namespace AppV2\Player\Http\Resources;

use AppV2\Player\Dto\PlayerVerificationDto;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin PlayerVerificationDto
 */
class PlayerVerificationResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'player_uuid' => $this->getPlayerUUID(),
            'verification_id' => $this->getVerificationId(),
            'form_id' => $this->getFormId(),
            'form_url' => $this->getFormUrl(),
            'status' => $this->getStatus(),
            'verified' => $this->getVerified(),
            'verified_at' => $this->getVerifiedAt(),
        ];
    }
}
