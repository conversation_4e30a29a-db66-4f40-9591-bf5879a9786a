<?php declare(strict_types=1);
namespace AppV2\Player\Http\Resources;

use AppV2\Player\Dto\VerificationTemplateDto;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin VerificationTemplateDto
 */
class VerificationTemplateResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
        ];
    }
}
