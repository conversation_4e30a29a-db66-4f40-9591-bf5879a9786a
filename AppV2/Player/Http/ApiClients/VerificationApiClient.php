<?php

namespace AppV2\Player\Http\ApiClients;

use App\Services\HttpService;
use AppV2\Player\Dto\VerificationTemplateDto;
use IntegratorCore\Services\Dto\ResponseOutputDto;
use Symfony\Component\HttpKernel\Exception\HttpException;

class VerificationApiClient extends HttpService
{
    protected bool $logging = true;

    /**
     * @return VerificationTemplateDto[]
     */
    public function getTemplates(): array
    {
        $response = $this->doPostRequest('/api/v1/templates');

        return array_map(function (array $item) {
            return new VerificationTemplateDto($item['id'], $item['name']);
        }, $response->content);
    }

    protected function doRequest(string $requestType, string $url): ResponseOutputDto
    {
        $response = parent::doRequest($requestType, $url);
        if (!$response->isSuccess) {
            throw new HttpException($response->responseCode, $response->rawContent);
        }

        return $response;
    }
}
