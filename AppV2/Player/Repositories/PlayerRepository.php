<?php declare(strict_types=1);

namespace AppV2\Player\Repositories;

use AppV2\Player\Models\Player;

class PlayerRepository
{
    public function getPlayerByEmail(string $email): Player
    {
        return Player::where('client_id', config('common.whitelabel_client_id'))
            ->where('email', $email)
            ->firstOrFail();
    }

    public function getPlayerByUUID(string $uuid): Player
    {
        return Player::where('client_id', config('common.whitelabel_client_id'))
            ->where('uuid', $uuid)
            ->firstOrFail();
    }

    public function getPlayerByID(int $id): Player
    {
        return Player::where('client_id', config('common.whitelabel_client_id'))
            ->where('id', $id)
            ->firstOrFail();
    }
}
