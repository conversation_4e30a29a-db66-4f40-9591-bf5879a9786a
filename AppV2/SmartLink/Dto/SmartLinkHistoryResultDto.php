<?php

declare(strict_types=1);

namespace AppV2\SmartLink\Dto;

use Illuminate\Database\Eloquent\Collection;

class SmartLinkHistoryResultDto
{
    public int $page;
    public int $perPage;
    public int $totalItems;
    public Collection $items;

    public function __construct(int $page, int $perPage, int $totalItems, Collection $logs)
    {
        $this->page = $page;
        $this->perPage = $perPage;
        $this->totalItems = $totalItems;
        $this->items = $logs;
    }
}
