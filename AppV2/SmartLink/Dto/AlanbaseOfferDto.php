<?php

declare(strict_types=1);

namespace AppV2\SmartLink\Dto;

class AlanbaseOfferDto
{
    public int $id;
    public ?string $name;
    public ?string $poster;
    public int $status;
    public ?int $privacyLevel;
    public ?array $descriptions;
    public ?int $holdPeriod;
    public string $targetLink;
    public ?string $trafficbackUrl;
    public ?int $postclick;
    public ?array $postclickFrom;
    public ?array $product;
    public ?array $trackingDomain;
    public ?array $tags;
    public ?array $partners;
    public ?array $landings;
    public ?string $createdAt;
    public ?string $updatedAt;
    public ?array $conditions;
    public ?array $individualConditions;

    public function __construct(array $data)
    {
        $this->id = $data['id'];
        $this->name = $data['name'];
        $this->poster = $data['poster'];
        $this->status = $data['status'];
        $this->privacyLevel = $data['privacy_level'];
        $this->descriptions = $data['descriptions'];
        $this->holdPeriod = $data['hold_period'];
        $this->targetLink = $data['target_link'];
        $this->trafficbackUrl = $data['trafficback_url'];
        $this->postclick = $data['postclick'];
        $this->postclickFrom = $data['postclick_from'];
        $this->product = $data['product'];
        $this->trackingDomain = $data['tracking_domain'];
        $this->tags = $data['tags'];
        $this->partners = $data['partners'] ?? null;
        $this->landings = $data['landings'];
        $this->createdAt = $data['created_at'];
        $this->updatedAt = $data['updated_at'];
        $this->conditions = $data['conditions'];
        $this->individualConditions = $data['individual_conditions'];
    }
}
