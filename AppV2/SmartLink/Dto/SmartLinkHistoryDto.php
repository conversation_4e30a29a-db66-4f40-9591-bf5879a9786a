<?php

namespace AppV2\SmartLink\Dto;

class SmartLinkHistoryDto
{
    private int $offerId;
    private ?string $dateFrom;
    private ?string $dateTo;
    private int $page;

    public function __construct(
        int $offerId,
        ?string $dateFrom,
        ?string $dateTo,
        int $page
    ) {
        $this->offerId = $offerId;
        $this->dateFrom = $dateFrom;
        $this->dateTo = $dateTo;
        $this->page = $page;
    }

    public function getOfferId(): int
    {
        return $this->offerId;
    }

    public function getDateFrom(): ?string
    {
        return $this->dateFrom;
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public function getDateTo(): ?string
    {
        return $this->dateTo;
    }
}
