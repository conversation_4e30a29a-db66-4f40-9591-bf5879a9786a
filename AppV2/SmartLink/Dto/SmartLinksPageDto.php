<?php

namespace AppV2\SmartLink\Dto;
use Illuminate\Support\Collection;

class SmartLinksPageDto
{
    /**
     * @var Collection<SmartLinkItemDto>
     */
    private Collection $items;
    private int $page;
    private int $perPage;
    private int $total;
    private int $lastPage;

    public function __construct(
        int $page,
        int $perPage,
        int $total,
        int $lastPage,
        Collection $items
    ) {
        $this->page = $page;
        $this->perPage = $perPage;
        $this->total = $total;
        $this->lastPage = $lastPage;
        $this->items = $items;
    }

    public function getItems(): Collection
    {
        return $this->items;
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public function getTotal(): int
    {
        return $this->total;
    }

    public function getPerPage(): int
    {
        return $this->perPage;
    }

    public function getLastPage(): int
    {
        return $this->lastPage;
    }
}
