<?php

namespace AppV2\SmartLink\Dto;

class SmartLinkItemDto
{
    private string $id;
    private string $name;
    private string $targetLink;
    private string $domain;
    private int $trackingDomainId;
    private int $privacyLevel;

    public function __construct(
        string $id,
        string $name,
        string $targetLink,
        string $domain,
        int $trackingDomainId,
        int $privacyLevel
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->targetLink = $targetLink;
        $this->domain = $domain;
        $this->trackingDomainId = $trackingDomainId;
        $this->privacyLevel = $privacyLevel;
    }

    public function getDomain(): string
    {
        return $this->domain;
    }

    public function getTrackingDomainId(): int
    {
        return $this->trackingDomainId;
    }

    public function getPrivacyLevel(): int
    {
        return $this->privacyLevel;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getTargetlink(): string
    {
        return $this->targetLink;
    }
}
