<?php

namespace AppV2\SmartLink\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Services\SmartLinkService;
use AppV2\SmartLink\Dto\SmartLinkHistoryDto;
use AppV2\SmartLink\Exceptions\OfferNotFoundException;
use AppV2\SmartLink\Exceptions\SameDomainException;
use AppV2\SmartLink\Http\Resources\SmartLinkChangeLogResource;
use AppV2\SmartLink\Http\Resources\SmartLinkOfferResource;
use AppV2\SmartLink\Http\Resources\SmartLinkPageResource;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class SmartLinkController extends Controller
{
    private SmartLinkService $smartLinkService;

    public function __construct(SmartLinkService $smartLinkService)
    {
        $this->smartLinkService = $smartLinkService;
    }

    /**
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function page(Request $request): SmartLinkPageResource
    {
        $validated = $this->validate($request, [
            'search' => ['string', 'min:1', 'max:30'],
            'page' => ['required', 'integer', 'min:1'],
            'per_page' => ['required', 'integer', 'min:1', 'max:50']
        ]);

        if (!isset($validated['search'])) {
            $result = $this->smartLinkService->getPage(
                $validated['page'],
                $validated['per_page']
            );
        } else {
            $result = $this->smartLinkService->searchUsingCache(
                $validated['search'],
                $validated['page'],
                $validated['per_page']
            );
        }

        return new SmartLinkPageResource($result);
    }

    public function updateDomain(int $id, Request $request): SmartLinkOfferResource
    {
        $validated = $this->validate($request, [
            'domain' => ['required', 'string', 'min:3', 'max:50', 'regex:/^([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,6}$/'],
        ]);

        try {
            $offer = $this->smartLinkService->updateDomain($id, $validated['domain'], Auth::user()->email);

            return new SmartLinkOfferResource($offer);
        } catch (SameDomainException|OfferNotFoundException $e) {
            abort(400, $e->getMessage());
        }
    }

    /**
     * @throws ValidationException
     */
    public function history(Request $request): SmartLinkChangeLogResource
    {
        $validated = $this->validate($request, [
            'offer_id' => ['required', 'int'],
            'date_from' => ['date'],
            'date_to' => ['date', 'after_or_equal:date_from'],
            'page' => ['required', 'int'],
        ]);

        $historyDto = new SmartLinkHistoryDto(
            $validated['offer_id'],
            $validated['date_from'] ?? null,
            $validated['date_to'] ?? null,
            $validated['page'],
        );

        $result = $this->smartLinkService->history($historyDto);

        return new SmartLinkChangeLogResource($result);
    }
}
