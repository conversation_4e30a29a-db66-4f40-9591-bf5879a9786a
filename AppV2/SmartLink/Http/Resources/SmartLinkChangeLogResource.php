<?php

declare(strict_types=1);

namespace AppV2\SmartLink\Http\Resources;

use AppV2\SmartLink\Dto\SmartLinkHistoryResultDto;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property SmartLinkHistoryResultDto $resource
 */
class SmartLinkChangeLogResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'items' => $this->resource->items,
            'prev_page' => $this->getPrevPage(),
            'next_page' => $this->getNextPage(),
        ];
    }

    private function getPrevPage(): ?int
    {
        return $this->resource->page > 1 ? $this->resource->page - 1 : null;
    }

    private function getNextPage(): ?int
    {
        return $this->resource->totalItems > $this->resource->perPage * $this->resource->page ? $this->resource->page + 1 : null;
    }
}
