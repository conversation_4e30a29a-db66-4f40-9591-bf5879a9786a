<?php

declare(strict_types=1);

namespace AppV2\SmartLink\Http\Resources;


use App\Services\Utilities\UrlService;
use AppV2\SmartLink\Dto\AlanbaseOfferDto;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin AlanbaseOfferDto
 */
class SmartLinkOfferResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'target_link' => $this->targetLink,
            'domain' => (new UrlService)->getDomainFromUrl($this->targetLink),
            'tracking_domain_id' => $this->trackingDomain['id'],
            'privacy_level' => $this->privacyLevel,
        ];
    }
}
