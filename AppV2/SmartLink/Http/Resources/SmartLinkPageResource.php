<?php

namespace AppV2\SmartLink\Http\Resources;

use AppV2\SmartLink\Dto\SmartLinkItemDto;
use AppV2\SmartLink\Dto\SmartLinksPageDto;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

/**
 * Class PlayerResource
 * @package AppV3\Player\Http\Resources
 * @mixin SmartLinksPageDto
 */
class SmartLinkPageResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'items' => $this->showItems($this->getItems()),
            'page' => $this->getPage(),
            'per_page' => $this->getPerPage(),
            'total' => $this->getTotal(),
            'last_page' => $this->getLastPage(),
        ];
    }

    /**
     * @param Collection<SmartLinkItemDto> $items
     */
    private function showItems(Collection $items): Collection
    {
        return $items->map(function (SmartLinkItemDto $item) {
            return [
                'id' => $item->getId(),
                'name' => $item->getName(),
                'target_link' => $item->getTargetLink(),
                'domain' => $item->getDomain(),
                'tracking_domain_id' => $item->getTrackingDomainId(),
                'privacy_level' => $item->getPrivacyLevel(),
            ];
        });
    }
}
