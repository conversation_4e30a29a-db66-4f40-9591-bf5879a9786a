<?php
declare(strict_types=1);

/** @var Router $router */

use App\Http\Controllers\BonusBalanceController;
use App\Http\Controllers\Payment\PaymentController;
use AppV2\Core\Http\Controllers\CoreClusterAccessTokenController;
use AppV2\Core\Http\Controllers\CoreClusterConnectionController;
use AppV2\Player\Http\Controllers\PlayerVerificationController;
use AppV2\SmartLink\Http\Controllers\SmartLinkController;
use Laravel\Lumen\Routing\Router;
use WhiteLabelAdmin\Entities\PermissionList;

$router->group([
    'prefix' => '/api/v4',
    'middleware' => ['logRequest', 'users'],
], function (Router $router) {

    // Smart link
    $router->group([
        'prefix' => '/smart-links',
    ], function (Router $router) {
        $router->get('/', [
            'uses' => SmartLinkController::class . '@page',
            'as' => 'smart-links.page',
        ]);

        $router->get('/history', [
            'uses' => SmartLinkController::class . '@history',
            'as' => 'smart-links.history',
        ]);

        $router->patch('/{id:\d+}/domain', [
            'uses' => SmartLinkController::class . '@updateDomain',
            'as' => 'smart-links.update-domain',
        ]);
    });


    $router->group(['prefix' => '/player/verifications'], function (Router $router) {
        $router->get('/templates', ['uses' => PlayerVerificationController::class . '@getTemplates']);
    });

    $router->group(['prefix' => '/core-clusters'], function (Router $router) {
        $router->get('', [
            'uses' => CoreClusterConnectionController::class . '@getClusters',
            'middleware' => 'check:' . PermissionList::CORE_CLUSTERS_LIST,
        ]);
        $router->group(['middleware' => ['clusterGrant']], function (Router $router) {
            $router->post('/current', [
                'uses' => CoreClusterAccessTokenController::class . '@issueTokenWithCluster',
                'middleware' => 'check:' . PermissionList::CORE_CLUSTERS_SELECT,
            ]);
        });
    });

    $router->group([
        'prefix' => 'statistics',
    ], function (Router $router) {
        $router->group([
            'prefix' => 'player',
        ], function (Router $router) {
            $router->group([
                'prefix' => 'payments',
            ], function (Router $router) {
                $router->get('logs', [
                    'uses' => PaymentController::class . '@searchPaymentsLogs',
                    'as' => 'statistics-get-player-payments-logs',
                ]);
                $router->get('bets', [
                    'uses' => PaymentController::class . '@searchPaymentsStatisticForBets',
                    'as' => 'statistics-get-player-payments-statistic-for-bets',
                ]);
            });
        });
        $router->group(['prefix' => '/bonuses'], function (Router $router) {
            $router->get('/balances',[
                'as' => 'get_bonuses_balances',
                'uses' => BonusBalanceController::class . '@getBonusBalances',
                'middleware' => 'check:' . PermissionList::UPDATE_BONUSES,
            ]);
        });
    });
});
