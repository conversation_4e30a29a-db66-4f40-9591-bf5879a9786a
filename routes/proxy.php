<?php declare(strict_types=1);

/** @var Router $router */

use App\Services\RoutePermitter;
use <PERSON><PERSON>\Lumen\Routing\Router;
use WhiteLabelAdmin\Controllers\ProxyController;

/** @var RoutePermitter $proxyRoutes */
$proxyRoutes = app(RoutePermitter::class);

$routesPatern = [
    '/api/v1/players/{id:\d+}',
    '/api/v1/players/{id:\d+}/meta',
    '/api/v1/players',
    '/api/v1/players/data-moderation',
    '/api/v3/payouts'
];

$router->group([
    'prefix' => '/api/v2',
    'middleware' => ['users', 'adminCredential'],
], function (Router $router) use ($proxyRoutes, $routesPatern) {
    foreach ($proxyRoutes->getRoutes() as $route) {
        $middleware = $route->middleware;

        if (in_array($route->pattern, $routesPatern)){
            $middleware[] = 'hideDataForUser';
        }

        if ($route->permission) {
            $middleware[] = 'check:' . $route->permission;
        }

        $router->{$route->method}($route->pattern, [
            'as' => $route->getName(),
            'uses' => ProxyController::class . '@proxy',
            'middleware' => $middleware,
        ]);
    }
});
