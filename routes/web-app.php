<?php

use <PERSON><PERSON>\Lumen\Routing\Router;
use WhiteLabelAdmin\Entities\PermissionList;

/** @var Router $router */

$router->group([
    'prefix' => '/api/v1/oauth',
    'middleware' => ['logRequest'],
], function (Router $router) {
    $router->post('/login', [
        'uses' => 'AuthController@login',
        'middleware' => [throttler('login')]
    ]);
    $router->post('/logout', [
        'uses' => 'AuthController@logout',
        'middleware' => [throttler('logout')]
    ]);

    $router->post('/refresh', [
        'uses' => 'AuthController@refresh',
        'middleware' => [throttler('refresh')]
    ]);
});


$router->group([
    'prefix' => 'api/bonus-management',
    'middleware' => ['logRequest', 'users']
], function (Router $router) {
    $router->group(['prefix' => '/free-spins'], function (Router $router) {
        $router->get('',                            ['uses' => 'BonusManagerController@FreeSpinList']);
        $router->post('add',                        ['uses' => 'BonusManagerController@FreeSpinCreate']);
        $router->post('attach',                     ['uses' => 'BonusManagerController@FreeSpinAttach']);
        $router->post('resend',                     ['uses' => 'BonusManagerController@FreeSpinResend']);
        $router->get ('player-bound-history/{id}',  ['uses' => 'BonusManagerController@FreeSpinPlayerBoundHistory']);
        $router->get ('player-bound-active/{id}',   ['uses' => 'BonusManagerController@FreeSpinPlayerBoundActive']);

        $router->get('history-logs/{id}',           ['uses' => 'BonusManagerController@FreeSpinHistoryLogs']);

        $router->get ('show/{id}',   ['uses' => 'BonusManagerController@FreeSpinShow']);
        $router->post ('check-free-spin-name',   ['uses' => 'BonusManagerController@FreeSpinCheckName']);
        $router->get ('get-players/{id}',   ['uses' => 'BonusManagerController@FreeSpinGetPlayersById']);
        $router->put ('update/{id}',   ['uses' => 'BonusManagerController@FreeSpinUpdate']);
        $router->put ('delete/{id}',   ['uses' => 'BonusManagerController@FreeSpinCancel']);
    });

    $router->group(['prefix' => '/bonuses'], function (Router $router) {
        $router->post('add',            ['uses' => 'BonusManagerController@BonusCreate']);
        $router->put('/{id}',           ['uses' => 'BonusManagerController@BonusUpdate']);
        $router->get('history-logs/{id}',           ['uses' => 'BonusManagerController@BonusHistoryLogs']);
        $router->get('get-templates',   ['uses' => 'BonusManagerController@GetТemplates']);
        $router->get('',                ['uses' => 'BonusManagerController@GetBonusList']);
        $router->post('/issue-bonus',   ['uses' => 'BonusManagerController@massApplyBonus']);
        $router->get('/balances',       ['uses' => 'BonusManagerController@getBonusBalances']);
        $router->post('/issue-bonus/{bonusType}',            ['uses' => 'BonusManagerController@applyBonusForPlayer']);
        $router->get('/{bonusType}/details/{bonusId:\d+}',   ['uses' => 'BonusManagerController@bonusDetails']);
        $router->post('/add-betby-template-bonus/{id}',      ['uses' => 'BonusManagerController@createBetbyBonus']);
        $router->get('/{id}/slots',      ['uses' => 'BonusManagerController@getBonusSlots']);
        $router->put('/{id}/slots',      ['uses' => 'BonusManagerController@updateBonusSlots']);

        $router->group(['prefix' => '/info'], function (Router $router) {
            $router->post('',             ['uses' => 'BonusManagerController@BonusInfoCreate']);
            $router->put('/{id}',         ['uses' => 'BonusManagerController@BonusInfoUpdate']);
            $router->get('',              ['uses' => 'BonusManagerController@GetBonusInfoList']);
            $router->get('/enabled/{id}', ['uses' => 'BonusManagerController@GetBonusInfoEnabledListForPlayer']);
            $router->get('/enabled-all/{id}', ['uses' => 'BonusManagerController@GetBonusInfoEnabledFullListForPlayer']);
        });

    });

    $router->group(['prefix' => '/banners'], function (Router $router) {
        $router->post('add',                ['uses' => 'BannerManagerController@BannerCreate']);
        $router->put('/{id}',               ['uses' => 'BannerManagerController@BannerUpdate']);
        $router->put('/{id}/change-status', ['uses' => 'BannerManagerController@BannerChangeStatus']);
        $router->put('/change-status/many', ['uses' => 'BannerManagerController@BannerChangeStatusMany']);
        $router->get('',                    ['uses' => 'BannerManagerController@GetBanners']);
        $router->delete('/{id}',            ['uses' => 'BannerManagerController@DeleteBanners']);
    });

    $router->group(['prefix' => '/bonuses/promo'], function (Router $router) {
        $router->post('',          ['uses' => 'BonusManagerController@PromoCreate']);
        $router->put('/{id}',      ['uses' => 'BonusManagerController@PromoUpdate']);
        $router->get('',           ['uses' => 'BonusManagerController@GetPromos']);
    });
});

$router->group([
    'prefix' => '/api/v1',
    'middleware' => ['logRequest', 'users'],
], function (Router $router) {
    $router->group([
        'prefix' => '/free-spins',
    ], function (Router $router) {
        $router->get('available-games/{provider_id}', [
            'as' => 'available_games',
            'uses' => 'FreeSpins\FreeSpinsController@availableGames',
        ]);
        $router->get('games-additional-data', [
            'as' => 'games_additional_data',
            'uses' => 'FreeSpins\FreeSpinsController@gamesAdditionalData',
        ]);
        $router->get('available-providers', [
            'as' => 'available_providers',
            'uses' => 'FreeSpins\FreeSpinsController@availableProviders',
        ]);
        $router->get('available-suppliers', [
            'as' => 'available_suppliers',
            'uses' => 'FreeSpins\FreeSpinsController@availableSuppliers',
        ]);
        $router->get('{supplierId}/available-aggregators', [
            'as' => 'supplier_available_aggregators',
            'uses' => 'FreeSpins\FreeSpinsController@availableSupplierAggregators',
        ]);
        $router->get('available-aggregators', [
            'as' => 'available_aggregators',
            'uses' => 'FreeSpins\FreeSpinsController@availableAggregators',
        ]);
    });
    $router->group([
        'prefix' => '/payment/currencies',
    ], function (Router $router) {
        $router->get('getCurrencies', [
            'as' => 'currencies_get',
            'uses' => 'PaymentService\CurrencyController@getCurrencies',
            'middleware' => 'check:' . PermissionList::CURRENCIES_LIST
        ]);
    });
    $router->get('/payment-management/aggregators/available', [
        'as' => 'aggregators_available',
        'uses' => 'PaymentManagement\PaymentManagementController@getAvailablePaymentAggregators',
    ]);
    $router->group([
        'prefix' => '/payment-management/methods',
    ], function (Router $router) {
        $router->get('available', [
            'as' => 'methods_available',
            'uses' => 'PaymentManagement\PaymentManagementController@getAvailablePaymentMethods',
        ]);
        $router->post('', [
            'as' => 'create_payment_system',
            'uses' => 'PaymentManagement\PaymentManagementController@createPaymentSystem',
        ]);
        $router->post('duplicate/{id}', [
            'as' => 'duplicate_payment_system',
            'uses' => 'PaymentManagement\PaymentManagementController@createDuplicatePaymentSystem',
        ]);
        $router->post('{id}', [
            'as' => 'update_payment_system',
            'uses' => 'PaymentManagement\PaymentManagementController@updatePaymentSystem',
        ]);
        $router->get('presets/{id}', [
            'as' => 'get_payment_presets',
            'uses' => 'PaymentManagement\PaymentManagementController@getPaymentPresets',
        ]);
        $router->post('presets/{id}', [
            'as' => 'save_payment_presets',
            'uses' => 'PaymentManagement\PaymentManagementController@savePaymentPresets',
        ]);
        $router->get('list', [
            'as' => 'list_payment_method',
            'uses' => 'PaymentManagement\PaymentManagementController@listPaymentMethod',
        ]);
        $router->get('{id}', [
            'as' => 'get_payment_method',
            'uses' => 'PaymentManagement\PaymentManagementController@getPaymentMethod',
        ]);
        $router->delete('{id}', [
            'as' => 'delete_payment_method',
            'uses' => 'PaymentManagement\PaymentManagementController@deletePaymentMethod',
        ]);
    });
    $router->group([], function (Router $router) {
        $router->get('/payout/currencies', [
            'as' => 'payout_currencies_get',
            'uses' => 'PayoutService\CurrencyController@getCurrencies',
            'middleware' => 'check:' . PermissionList::CURRENCIES_LIST
        ]);
    });
    $router->group([
        'prefix' => '/payment/methods',
    ], function (Router $router) {
        $router->get('show/{iso}', [
            'as' => 'methods_get',
            'uses' => 'PaymentService\PaymentMethodsController@show',
        ]);
        $router->get('showAggregators/{iso}', [
            'as' => 'methods_aggregators_get',
            'uses' => 'PaymentService\PaymentMethodsController@showAggregatorsPayments'
        ]);
        $router->post('decrypt', [
            'uses' => 'PaymentService\PaymentMethodsController@decrypt',
            'middleware' => 'check:' . PermissionList::PAYMENTS_PAYMENT_SYSTEMS_DECRYPT,
        ]);
    });
    $router->group([
        'prefix' => '/admin',
    ], function (Router $router) {
        $router->get('/aggregators', [
            'as' => 'admin_aggregators',
            'uses' => 'PayoutService\PayoutMethodController@getAdminAggregators',
        ]);
        $router->get('/transaction/token', [
            'as' => 'admin_determining-user',
            'uses' => 'PaymentService\PaymentMethodsController@getDeterminingUser',
        ]);
        $router->post('/payouts', [
            'as' => 'admin_payouts',
            'uses' => 'PayoutService\PayoutMethodController@getAdminPayouts',
        ]);
    });
    $router->group([
        'prefix' => '/payout/method',
    ], function (Router $router) {
        $router->get('', [
            'as' => 'method_get',
            'uses' => 'PayoutService\PayoutMethodController@getMethods',
        ]);
        $router->post('update/{token}', [
            'as' => 'method_update',
            'uses' => 'PayoutService\PayoutMethodController@update',
        ]);
    });
    $router->group([
        'prefix' => '/payment/method',
    ], function (Router $router) {
        $router->get('show/{token}', [
            'as' => 'method_get',
            'uses' => 'PaymentService\PaymentMethodController@show',
        ]);
        $router->post('update/{token}', [
            'as' => 'method_update',
            'uses' => 'PaymentService\PaymentMethodController@update',
        ]);
    });
    $router->group([
        'prefix' => '/payment/methods/presets',
    ], function (Router $router) {
        $router->get('/{token}', [
            'as' => 'presets_get',
            'uses' => 'PaymentService\PresetController@show',
        ]);
        $router->post('/{token}', [
            'as' => 'preset_store',
            'uses' => 'PaymentService\PresetController@store',
        ]);
        $router->delete('/{token}', [
            'as' => 'preset_delete',
            'uses' => 'PaymentService\PresetController@destroy',
        ]);
    });
    $router->group([
        'prefix' => '/requisites/blacklist',
    ], function (Router $router) {
        $router->get('', [
            'as' => 'requisites_blacklist_get',
            'uses' => 'RequisitesBlacklist\RequisitesBlacklistController@get',
        ]);
        $router->post('', [
            'as' => 'requisites_blacklist_store',
            'uses' => 'RequisitesBlacklist\RequisitesBlacklistController@store',
        ]);
        $router->delete('/{id}', [
            'as' => 'requisites_blacklist_destroy',
            'uses' => 'RequisitesBlacklist\RequisitesBlacklistController@destroy',
        ]);
    });
    $router->group([
        'prefix' => '/payment/deposits',
    ], function (Router $router) {
        $router->get('{label}/failed', [
            'as' => 'deposits_failed',
            'uses' => 'PaymentService\DepositsController@showFailed',
            'middleware' => \App\Enums\MiddlewareEnum::ENCRYPT_PAYMENT_SYSTEM,
        ]);
    });
    $router->group([
        'prefix' => '/admin/withdraw',
    ], function (Router $router) {
        $router->post('order/retry', [
            'as' => 'order_retry',
            'uses' => 'PayoutService\WithdrawOrderController@orderRetry',
        ]);
        $router->post('order/close', [
            'as' => 'order_retry',
            'uses' => 'PayoutService\WithdrawOrderController@orderClose',
        ]);
        $router->post('orders/methods_to_change', [
            'as' => 'methods_to_change',
            'uses' => 'PayoutService\WithdrawOrderController@methodsToChange',
            'middleware' => 'encryptPaymentSystem',
        ]);
        $router->patch('orders', [
            'as' => 'methods_to_change_update',
            'uses' => 'PayoutService\WithdrawOrderController@methodsToChangeUpdate',
        ]);
    });

    $router->group([
        'prefix' => 'slot-provider',
    ], function (Router  $router) {
        $router->put('change-enable', [
            'uses' => 'SlotProviderController@changeEnable',
            'middleware' => 'check:' . PermissionList::UPDATE_SLOTS,
        ]);
    });

    $router->group([
        'prefix' => '/countries',
        'namespace' => '\App\Http\Controllers'
    ], function (Router $router) {
        $router->post('/bind-providers-to-country/{id}', [
            'as' => 'bind_providers_to_country',
            'uses' => 'CountryController@bindProvidersToCountry',
            'middleware' => 'check:' . PermissionList::SLOT_PROVIDERS_LOCATION,
        ]);
    });

    $router->group([
        'prefix' => 'casino',
        'namespace' => '\App\Http\Controllers'
    ], function (Router $router) {
        $router->delete('providers/geolocations/{id}', [
            'as' => 'delete_provider_location',
            'uses' => 'SlotProviderController@deleteProviderLocation',
            'middleware' => 'check:' . PermissionList::SLOT_PROVIDERS_LOCATION,
        ]);
    });

    $router->group([
        'prefix' => 'tests',
    ], function (Router  $router) {
        $router->post('initialize', [
            'uses' => 'InitializeController@initialize',
        ]);
        $router->get('whitelist-ip-address', [
            'uses' => 'InitializeController@getWhitelistedIpAddress',
        ]);
    });
});

