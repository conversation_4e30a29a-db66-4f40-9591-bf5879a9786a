<?php

/** @var Router $router */

use <PERSON><PERSON>\Lumen\Routing\Router;
use WhiteLabelAdmin\Entities\PermissionList;

$router->get('/', function () use ($router) {
    if (isProduction()) {
        return response('Forbidden', 403);
    }

    return view('swagger');
});

$router->get('/info', [
    'uses' => 'InfoController@getInfoAction',
    'as' => 'healthcheck',
    'middleware' => throttler('default')
]);

$router->get('/docs', ['uses' => 'InfoController@getJsonDocsAction', 'as' => 'swagger']);

$router->group([
    'prefix' => '/api/v1',
    'middleware' => [
        'clusterGrant',
        'logRequest',
        'users'
    ],
], function (Router $router) {
    $router->group([
        'prefix' => '/templates',
    ], function (Router $router) {
        $router->get('', [
            'as' => 'templates_list',
            'uses' => 'SearchTemplateController@list',
        ]);

        $router->post('', [
            'as' => 'create_template',
            'uses' => 'SearchTemplateController@createTemplate',
        ]);

        $router->put('{id}', [
            'as' => 'update_template',
            'uses' => 'SearchTemplateController@updateTemplate',
        ]);

        $router->delete('{id}', [
            'as' => 'delete_template',
            'uses' => 'SearchTemplateController@deleteTemplate',
        ]);
    });

    $router->group([
        'prefix' => '/users',
    ], function (Router $router) {
        $router->post('', [
            'as' => 'register',
            'uses' => 'UserController@register',
            'middleware' => 'check:' . PermissionList::MODERATORS_ADD,
        ]);

        $router->get('', [
            'as' => 'user_search',
            'uses' => 'UserController@searchUsers',
            'middleware' => 'check:' . PermissionList::MODERATORS_LIST,
        ]);

        $router->group([
            'prefix' => '/{userId:me|\d+}',
            'namespace' => '\App\Http\Controllers',
            # permissions is checking inside ProfileAwareController
        ], function (Router $router) {
            $router->put('', [
                'as' => 'update_user',
                'uses' => 'ProfileAwareController@update',
            ]);
            $router->post('', [
                'as' => 'update_password',
                'uses' => 'ProfileAwareController@password',
            ]);

            $router->get('', [
                'as' => 'get_user',
                'uses' => 'ProfileAwareController@get',
            ]);

            $router->delete('', [
                'as' => 'delete_user',
                'uses' => 'ProfileAwareController@delete',
            ]);
        });
    });

    $router->group([
        'prefix' => '/segments',
        'namespace' => '\App\Http\Controllers',
    ], function (Router $router) {
        $router->get('', [
            'as' => 'get_segments',
            'uses' => 'SegmentController@getAll',
            'middleware' => 'check:' . PermissionList::SEGMENTS_LIST
        ]);
    });

    $router->group([
        'prefix' => '/roles',
    ], function (Router $router) {
        $router->get('/permissions', [
            'as' => 'get_permissions',
            'uses' => 'PermissionController@getAllPermissions',
            'middleware' => 'check:' . PermissionList::ROLES_LIST
        ]);

        $router->get('', [
            'as' => 'search_role',
            'uses' => 'RoleController@searchRoles',
            'middleware' => 'check:' . PermissionList::ROLES_LIST,
        ]);

        $router->post('', [
            'as' => 'add_role',
            'uses' => 'RoleController@createRole',
            'middleware' => 'check:' . PermissionList::ROLES_ADD,
        ]);

        $router->put('/{roleId:\d+}', [
            'as' => 'update_role',
            'uses' => 'RoleController@updateRole',
            'middleware' => 'check:' . PermissionList::ROLES_EDIT,
        ]);

        $router->delete('/{roleId:\d+}', [
            'as' => 'delete_role',
            'uses' => 'RoleController@deleteRole',
            'middleware' => 'check:' . PermissionList::ROLES_DELETE,
        ]);
    });

    $router->group([
        'prefix' => '/players',
    ], function (Router $router) {
        $router->get('', [
            'as' => 'get_players',
            'uses' => 'PlayerController@searchPlayers',
            'middleware' => 'check:' . PermissionList::USERS_LIST,
        ]);

        $router->get('/search', [
            'as' => 'search_players',
            'uses' => 'PlayerController@searchPlayers',
            'middleware' => 'check:' . PermissionList::USERS_LIST,
        ]);

        $router->get('/{playerId:\d+}', [
            'as' => 'get_player',
            'uses' => 'PlayerController@getPlayer',
            'middleware' => 'check:' . PermissionList::USERS_LIST,
        ]);

        $router->put('/{playerId:\d+}', [
            'as' => 'update_player',
            'uses' => 'PlayerController@updatePlayer',
            'middleware' => 'check:' . PermissionList::USERS_EDIT,
        ]);
        $router->post('/comments', [
            'as' => 'comment_players',
            'uses' => 'DumbController@assignComment',
            'middleware' => 'check:' . PermissionList::COMMENT_ASSIGN,
        ]);
    });

    $router->group([
        'prefix' => '/currencies',
    ], function (Router $router) {
        $router->post('/{iso}', [
            'as' => 'update_currency',
            'uses' => 'CurrencyController@updateCurrency',
            'middleware' => 'check:' . PermissionList::CURRENCIES_EDIT,
        ]);

        $router->get('', [
            'as' => 'search_currencies',
            'uses' => 'CurrencyController@searchCurrencies',
            'middleware' => 'check:' . PermissionList::CURRENCIES_LIST,
        ]);
    });

    $router->group([
        'prefix' => '/countries',
    ], function (Router $router) {
        $router->post('/{iso}', [
            'as' => 'update_countries',
            'uses' => 'CountryController@addCountry',
            'middleware' => 'check:' . PermissionList::COUNTRIES_EDIT,
        ]);

        $router->get('', [
            'as' => 'search_countries',
            'uses' => 'CountryController@searchCountries',
            'middleware' => 'check:' . PermissionList::COUNTRIES_LIST,
        ]);
    });

    $router->group([
        'prefix' => '/countries',
        'namespace' => '\App\Http\Controllers'
    ], function (Router $router) {
        $router->post('/bind-providers-to-country/{id}', [
            'as' => 'bind_providers_to_country',
            'uses' => 'CountryController@bindProvidersToCountry',
        ]);
    });

    $router->group([
        'prefix' => '/casino/providers',
        'namespace' => '\App\Http\Controllers'
    ], function (Router $router) {
        $router->get('/geolocations', [
            'as' => 'get_countries',
            'uses' => 'CountryController@getCountries',
        ]);
    });

    $router->group([
        'prefix' => '/payments',
    ], function (Router $router) {
        $router->get('', [
            'as' => 'search_payments',
            'uses' => 'PaymentController@getPayments',
            'middleware' => 'check:' . PermissionList::PAYMENTS_LIST,
        ]);
    });

    $router->group(['prefix' => '/gateways'], function (Router $router) {
        $router->post('/{name}', [
            'as' => 'update_gateway',
            'uses' => 'PaymentGatewayController@manage',
            'middleware' => 'check:' . PermissionList::UPDATE_GATEWAY,
        ]);

        $router->get('/', [
            'as' => 'search_gateway',
            'uses' => 'PaymentGatewayController@searchAction',
        ]);
    });
});
