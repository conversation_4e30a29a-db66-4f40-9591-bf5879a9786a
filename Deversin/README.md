# Docker Deversin enveroment

### Containers 
* php >=7.4
* ext_pdo
* ext_opcache
* ext_openssl
* ext_mongo
* ext_bcmath
* the rest should be already in php
* `public` - document root

### infra
* nginx to route request 1.12.1, publish 80 port
* mysql >=5.7
* mongo db 4.4.1
* redis 5.0

## Prepare to run prod

* Copy `.env.example` to `.env` if it does not exist
* Make sure `.env` contains valid db creds (db should already be created)
* Run migrations `php artisan migrate`
* Run seeders `CLIENT_ID={id} php artisan db:seed` (seed countries, languages, etc)
* Set up oauth keys `php artisan passport:keys --force`
* Create admin client `php artisan passport:client --client`
* Change `ADMIN_CLIENT_ID` with newly created client id in `.env` 
* Create a client for a back/front-end `php artisan passport:client --password`
* add to crontab `php artisan schedule:run`

profit

## cli 
* `convert-legacy-pwd` - not used 
* `db:seed --class=CurrencySeeder` - seeds currencies
* `db:seed --class=CountrySeeder` - seeds countries
* `subscription:create` - creates new subscription
***
* `db:seed --class=LanguageSeeder` - seeds languages
* `actualize:db` - enables all countries, languages and currencies
* `initial:setup {client}` - DELETES all bonuses, banners and bonus balances and creates again
* `migration:after` - run balances sync


