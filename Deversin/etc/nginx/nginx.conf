user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    log_format pageview_json escape=json '{ "time": "$time_iso8601", '
                             '"remote_addr": "$remote_addr", '
                             '"body_bytes_sent": "$body_bytes_sent", '
                             '"request_time": "$request_time", '
                             '"status": "$status", '
                             '"request": "$request", '
                             '"request_method": "$request_method", '
                             '"request_uri": "$request_uri", '
                             '"sci": "$cookie_sci",'
                             '"user_id": "$cookie_uid",'
                             '"user_token": "$cookie_lsvtkn",'
                             '"prev_user_token": "$cookie_lspvtkn",'
                             '"http_referer": "$http_referer", '
                             '"http_user_agent": "$http_user_agent" }';

    sendfile        on;
    access_log  /var/log/nginx/access.log  main;
    #tcp_nopush     on;

    keepalive_timeout  65;
    client_max_body_size 8m;

    #gzip  on;

    include /etc/nginx/conf.d/*.conf;
}

