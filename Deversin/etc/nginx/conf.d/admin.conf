server {
    listen 80;
    server_name admin.whitelabel.dd;

    root '/var/www/html/admin-backend/public';
    index index.php index.html;

    error_log  /var/log/nginx/error.admin.log;
    access_log /var/log/nginx/access.admin.log;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location /favicon.ico {
        log_not_found off;
        access_log off;
    }

    location ~ \.php$ {
        root '/var/www/html/public';

        include fastcgi_params;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass admin-backend:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/$fastcgi_script_name;
    }

    location ~* \.(js|css|png|jpg|jpeg|gif|ico)$ {
        expires max;
        access_log off;
        log_not_found off;
    }
}
