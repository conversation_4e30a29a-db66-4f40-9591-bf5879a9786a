version: "3"

services:

  app-backend:
    container_name: app-backend
    build: build/php-app-backend
    restart: always
    extra_hosts:
      - "app.whitelabel.dd:**********"
      - "bonus-management.whitelabel.dd:**********"
      - "banner-management.whitelabel.dd:**********"
      - "promocode-management.whitelabel.dd:**********"
      - "admin.whitelabel.dd:**********"
      - "host.docker.internal:host-gateway"
    volumes:
      - ../../app-backend:/var/www/html:rw
      - ./etc/php/conf.d/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    depends_on:
      - redis
    environment:
      PHP_IDE_CONFIG: "serverName=app.whitelabel.dd"
    networks:
      - default
    #  - kafka_default # Comment this network if you don't use Kafka in this container
    #  - elk_default #  Comment this network if you don't use ELK in this container

  bonus-management:
    container_name: bonus-management
    build: build/php-bonus-management
    restart: always
    extra_hosts:
      - "app.whitelabel.dd:**********"
      - "bonus-management.whitelabel.dd:**********"
      - "banner-management.whitelabel.dd:**********"
      - "promocode-management.whitelabel.dd:**********"
      - "admin.whitelabel.dd:**********"
      - "host.docker.internal:host-gateway"
    volumes:
      - ../../bonus-management:/var/www/html:rw
      - ./etc/php/conf.d/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./etc/php/conf.d/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    depends_on:
      - redis
    environment:
      PHP_IDE_CONFIG: "serverName=bonus-management.whitelabel.dd"
    networks:
      - default
      - kafka_default # Comment this network if you don't use Kafka in this container
 #     - elk_default #  Comment this network if you don't use ELK in this container


  banner-management:
    container_name: banner-management
    build: build/php-banner-management
    restart: always
    extra_hosts:
      - "app.whitelabel.dd:**********"
      - "bonus-management.whitelabel.dd:**********"
      - "banner-management.whitelabel.dd:**********"
      - "promocode-management.whitelabel.dd:**********"
      - "admin.whitelabel.dd:**********"
      - "host.docker.internal:host-gateway"
    volumes:
      - ../../banner-management:/var/www/html:rw
      - ./etc/php/conf.d/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./etc/php/conf.d/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    #      - ./etc/php/conf.d/opcache.ini:/usr/local/etc/php/conf.d/opcache.ini
    depends_on:
      - redis
    environment:
      PHP_IDE_CONFIG: "serverName=banner-management.whitelabel.dd"
    networks:
      - default
#      - kafka_default # Comment this network if you don't use Kafka in this container

  promocode-management:
    container_name: promocode-management
    build: build/php-bonus-management
    restart: always
    extra_hosts:
      - "app.whitelabel.dd:**********"
      - "bonus-management.whitelabel.dd:**********"
      - "banner-management.whitelabel.dd:**********"
      - "promocode-management.whitelabel.dd:**********"
      - "admin.whitelabel.dd:**********"
      - "host.docker.internal:host-gateway"
    volumes:
      - ../../promocode-management:/var/www/html:rw
      - ./etc/php/conf.d/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./etc/php/conf.d/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    #      - ./etc/php/conf.d/opcache.ini:/usr/local/etc/php/conf.d/opcache.ini
    depends_on:
      - redis
    environment:
      PHP_IDE_CONFIG: "serverName=promocode-management.whitelabel.dd"
    networks:
      - default
      - kafka_default # Comment this network if you don't use Kafka in this container
#     - elk_default # Comment this network if you don't use ELK in this container

  admin-backend:
    container_name: admin-backend
    build: build/php-admin-backend
    restart: always
    extra_hosts:
      - "app.whitelabel.dd:**********"
      - "bonus-management.whitelabel.dd:**********"
      - "banner-management.whitelabel.dd:**********"
      - "promocode-management.whitelabel.dd:**********"
      - "admin.whitelabel.dd:**********"
      - "host.docker.internal:host-gateway"
    volumes:
      - ../../admin-backend:/var/www/html:rw
      - ./etc/php/conf.d/xdebug.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    depends_on:
      - redis
    environment:
      PHP_IDE_CONFIG: "serverName=admin.whitelabel.dd"
    networks:
      - default
#      - kafka_default # Comment this network if you don't use Kafka in this container
#      - elk_default # Comment this network if you don't use ELK in this container

  redis:
    container_name: redis
    image: redis:7.2.5-alpine
    restart: always
    ports:
      - "6379:6379"
    networks:
      - default

  nginx:
    container_name: nginx
    image: nginx:1.12.1-alpine
    restart: always
    platform: linux/x86_64
    ports:
      - "80:80"
    volumes:
      - ./etc/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./etc/nginx/conf.d:/etc/nginx/conf.d
      - ./etc/nginx/log:/var/log/nginx
      - ../../app-backend/public:/var/www/html/app-backend/public:rw
      - ../../admin-backend/public:/var/www/html/admin-backend/public:rw
      - ../../bonus-management/public:/var/www/html/bonus-management/public:rw
      - ../../banner-management/public:/var/www/html/banner-management/public:rw
      - ../../promocode-management/public:/var/www/html/promocode-management/public:rw
    depends_on:
      - app-backend
      - admin-backend
      - bonus-management
      - banner-management
      - promocode-management
    networks:
      - default

  mysql:
    container_name: mysql
    build:
      context: ./docker/mysql
      args:
        HOST_UID: 1001
    command: mysqld --sql_mode=""
    restart: always
    volumes:
      - ./docker/runtime/mysql:/var/lib/mysql
      - ./docker/db_archive:/in_docker_db_archive
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_DATABASE: portal
      MYSQL_USER: docker
      MYSQL_PASSWORD: secret
    networks:
      - default

networks:
  default:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/24
          gateway: **********
  kafka_default: # Comment this network if you don't use Kafka in al least one container
    external: true
  elk_default: # Comment this network if you don't use ELK in al least one container
    external: true
