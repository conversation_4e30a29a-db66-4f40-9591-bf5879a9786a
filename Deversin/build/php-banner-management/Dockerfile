
FROM php:8.3-fpm

WORKDIR /var/www/html

RUN apt-get update \
    && apt-get install -y wget \
    && apt-get install -y git mc curl supervisor libcurl3-dev \
    libcurl4-openssl-dev zlib1g-dev libicu-dev g++ libpng-dev libmaxminddb-dev \
    libmcrypt-dev libxslt-dev libzmq5-dev openssl libssl-dev nano \
    && php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');" \
    && php composer-setup.php --install-dir=/usr/bin --filename=composer \
    && php -r "unlink('composer-setup.php');"

RUN docker-php-ext-install mysqli pdo_mysql
RUN docker-php-ext-install gd exif xsl
RUN docker-php-ext-install sysvsem && docker-php-ext-enable sysvsem
RUN docker-php-ext-install opcache && docker-php-ext-enable opcache

RUN pecl channel-update pecl.php.net
RUN pecl install redis && docker-php-ext-enable redis
RUN pecl install maxminddb
RUN pecl install mongodb && echo "extension=mongodb.so" > /usr/local/etc/php/conf.d/mongo.ini

#xdebug
RUN pecl install xdebug-3.4.1 && docker-php-ext-enable xdebug \
    && echo "date.timezone = Europe/Kiev" > /usr/local/etc/php/conf.d/timezone.ini

RUN curl -sL https://deb.nodesource.com/setup_12.x | bash - \
    && apt-get update \
    && apt-get install nodejs -y

RUN apt-get autoremove -y && rm -rf /var/lib/apt/lists/*

EXPOSE 9000

CMD ["php-fpm"]