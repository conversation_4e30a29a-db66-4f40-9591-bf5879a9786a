FROM php:7.4-fpm

RUN apt-get update \
    && apt-get install -y wget \
    && apt-get install -y git mc curl supervisor libcurl3-dev \
    libcurl4-openssl-dev zlib1g-dev libicu-dev g++ libpng-dev libmaxminddb-dev \
    libmcrypt-dev libxslt-dev libzmq5-dev openssl libssl-dev nano\
    && php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');" \
    && php composer-setup.php --install-dir=/usr/bin --filename=composer \
    && php -r "unlink('composer-setup.php');"

RUN docker-php-ext-install mysqli pdo_mysql
RUN docker-php-ext-install opcache gd exif xsl
RUN docker-php-ext-install sysvsem && docker-php-ext-enable sysvsem

RUN pecl install redis && docker-php-ext-enable redis
RUN pecl install maxminddb
#RUN pecl install mongodb && echo "extension=mongodb.so" > /usr/local/etc/php/conf.d/mongo.ini
RUN pecl install mongodb-1.12.0 && echo "extension=mongodb.so" > /usr/local/etc/php/conf.d/mongo.ini

# kafka
RUN cd /tmp \
    && mkdir librdkafka \
    && cd librdkafka \
    && git clone https://github.com/edenhill/librdkafka.git . \
    && ./configure \
    && make \
    && make install \
    && pecl install rdkafka \
    && echo "extension=rdkafka.so" > /usr/local/etc/php/conf.d/rdkafka.ini

#xdebug
RUN pecl install xdebug-3.1.6 && docker-php-ext-enable xdebug \
    && echo "date.timezone = Europe/Kiev" > /usr/local/etc/php/conf.d/timezone.ini

#NewRelic
#ENV NEW_RELIC_AGENT_VERSION=10.6.0.318
#ENV NEW_RELIC_LICENSE_KEY=c0e57da22524dbfc9127e1da52ad055ea93fNRAL
#ENV NEW_RELIC_APPNAME=dashboard_ser
#RUN curl -L https://download.newrelic.com/php_agent/archive/${NEW_RELIC_AGENT_VERSION}/newrelic-php5-${NEW_RELIC_AGENT_VERSION}-linux.tar.gz | tar -C /tmp -zx \
#    && export NR_INSTALL_USE_CP_NOT_LN=1 \
#    && export NR_INSTALL_SILENT=1 \
#    && /tmp/newrelic-php5-${NEW_RELIC_AGENT_VERSION}-linux/newrelic-install install \
#    && rm -rf /tmp/newrelic-php5-* /tmp/nrinstall*
#
#RUN sed -i -e "s/REPLACE_WITH_REAL_KEY/${NEW_RELIC_LICENSE_KEY}/" \
#    -e "s/newrelic.appname[[:space:]]=[[:space:]].*/newrelic.appname=\"${NEW_RELIC_APPNAME}\"/" \
#    -e '$anewrelic.daemon.address="newrelic-php-daemon:31339"' \
#    $(php -r "echo(PHP_CONFIG_FILE_SCAN_DIR);")/newrelic.ini \

RUN curl -sL https://deb.nodesource.com/setup_12.x | bash - \
    && apt-get update \
    && apt-get install nodejs -y

RUN apt-get autoremove -y && rm -rf /var/lib/apt/lists/*

EXPOSE 9000

CMD ["php-fpm"]
