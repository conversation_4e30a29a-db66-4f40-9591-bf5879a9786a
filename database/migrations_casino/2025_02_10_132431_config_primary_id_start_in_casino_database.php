<?php

use AppV3\Core\Enums\DatabaseConnectionsEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class ConfigPrimaryIdStartInCasinoDatabase extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Increase these values if release will ba after 28.02.2025
        $startValueSlotsBets = 3_000_000_000;
        $startValueBalanceSessions = 400_000_000;

        if (app()->environment() === 'staging') {
            $startValueSlotsBets = 1_200_000_000;
            $startValueBalanceSessions = 11_000_000;
        }

        if (in_array(app()->environment(), ['dev', 'local'])) {
            $startValueSlotsBets = 240_000_000;
            $startValueBalanceSessions = 20_000;
        }

        $this->setStartingValue('slots_bets', $startValueSlotsBets);
        $this->setStartingValue('balance_sessions', $startValueBalanceSessions);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //do nothing
    }

    private function setStartingValue(string $table, int $startValue): void
    {
        DB::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->statement("ALTER TABLE $table AUTO_INCREMENT = $startValue");
    }
}
