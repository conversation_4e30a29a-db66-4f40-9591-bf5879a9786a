<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSlotBetIdColumnToUnrecognizedRefundTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('unrecognized_refund', function (Blueprint $table) {
            $table->unsignedBigInteger('slot_bet_id')->after('id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('unrecognized_refund', function (Blueprint $table) {
            $table->dropColumn('slot_bet_id');
        });
    }
}
