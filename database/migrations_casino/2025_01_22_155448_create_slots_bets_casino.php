<?php

use AppV3\Core\Enums\DatabaseConnectionsEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSlotsBetsCasino extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->create('slots_bets', function (Blueprint $table) {
            $table->id();
            $table->string('log_key')->nullable();                                              //todo delete useless column
            $table->unsignedBigInteger('player_id');
            $table->unsignedBigInteger('bonus_id')->nullable();
            $table->unsignedBigInteger('freespin_bound_id')->nullable();                        //todo delete useless column
            $table->unsignedBigInteger('session_id');
            $table->unsignedBigInteger('slot_id');
            $table->string('slot_external_id')->nullable();
            $table->string('external_id');
            $table->string('parent_external_id')->nullable();
            $table->integer('amount');
            $table->string('currency', 3);
            $table->integer('win_amount')->nullable();
            $table->string('status');
            $table->boolean('is_new_flow')-> nullable();                                        //todo delete useless column
            $table->bigInteger('wager_diff')->nullable();
            $table->timestamp('action_at')->useCurrent();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table->index('bonus_id');
            $table->index('player_id');
            $table->index('slot_id');
            $table->index('external_id');
            $table->index('parent_external_id');
            $table->index('session_id');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->dropIfExists('slots_bets');
    }
}
