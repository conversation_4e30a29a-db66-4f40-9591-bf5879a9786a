<?php

use AppV3\Core\Enums\DatabaseConnectionsEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSlotsBetsDatesTableCasino extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->create('slots_bets_dates', function (Blueprint $table) {
            $table->id();
            $table->integer('client_id');
            $table->string('date');
            $table->unsignedBigInteger('min_id')->nullable();
            $table->unsignedBigInteger('max_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->dropIfExists('slots_bets_dates');
    }
}
