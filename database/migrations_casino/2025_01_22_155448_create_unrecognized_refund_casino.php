<?php

use AppV3\Core\Enums\DatabaseConnectionsEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUnrecognizedRefundCasino extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->create('unrecognized_refund', function (Blueprint $table) {
            $table->id();
            $table->string('slot_id');
            $table->string('external_id');
            $table->string('parent_external_id')->nullable()->index();
            $table->string('status');
            $table->integer('amount');
            $table->string('currency', 3);
            $table->string('job_status')->nullable()->index();
            $table->string('reason');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->dropIfExists('unrecognized_refund');
    }
}
