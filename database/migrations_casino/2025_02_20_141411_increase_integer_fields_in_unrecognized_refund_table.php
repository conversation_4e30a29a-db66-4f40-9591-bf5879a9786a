<?php

use AppV3\Core\Enums\DatabaseConnectionsEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class IncreaseIntegerFieldsInUnrecognizedRefundTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->table('unrecognized_refund', function (Blueprint $table) {
            $table->bigInteger('amount')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->table('unrecognized_refund', function (Blueprint $table) {
            $table->integer('amount')->change();
        });
    }
}
