<?php

use AppV3\Core\Enums\DatabaseConnectionsEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBalanceSessionsCasino extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->create('balance_sessions', function (Blueprint $table) {
            $table->id();
            $table->integer('player_id')->index();
            $table->integer('balance_id')->nullable()->index();
            $table->integer('bonus_id')->nullable()->index();
            $table->string('token')->unique()->index();
            $table->string('flag')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(DatabaseConnectionsEnum::MYSQL_CASINO)->dropIfExists('balance_sessions');
    }
}
