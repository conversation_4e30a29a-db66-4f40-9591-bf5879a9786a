<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsNotifiableToFailedJobsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('queue.failed.database'))->table('failed_jobs', function (Blueprint $table) {
            $table->boolean('is_notifiable')->nullable()->default(false)->after('exception');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('queue.failed.database'))->table('failed_jobs', function (Blueprint $table) {
            $table->dropColumn('is_notifiable');
        });
    }
}
