<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeEmailToNullableInOnetimeInfoTable extends Migration
{
    public function up(): void
    {
        Schema::table('mass_onetime_bonus_info', function (Blueprint $table) {
            $table->string('email')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('mass_onetime_bonus_info', function (Blueprint $table) {
            $table->string('email')->nullable(false)->change();
        });
    }
}
