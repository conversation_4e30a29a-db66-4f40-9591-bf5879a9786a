<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddActionAtToPaymentsPlayerIdIndex extends Migration
{
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Drop index
            $table->dropForeign(['player_id']);
            $table->dropIndex(['player_id']);
            // Create the new index
            $table->index(['player_id', 'action_at']);
            $table->foreign('player_id')
                ->references('id')
                ->on('players')
                ->onDelete('RESTRICT')
                ->onUpdate('RESTRICT');
        });
    }

    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropForeign(['player_id']);
            $table->dropIndex(['player_id', 'action_at']);

            // Recreate the old index
            $table->index(['player_id']);
            $table->foreign('player_id')
                ->references('id')
                ->on('players')
                ->onDelete('RESTRICT')
                ->onUpdate('RESTRICT');
        });
    }
}
