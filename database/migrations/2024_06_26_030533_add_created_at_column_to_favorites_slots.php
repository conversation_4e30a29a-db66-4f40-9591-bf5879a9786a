<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddCreatedAtColumnToFavoritesSlots extends Migration
{
    public function up(): void
    {
        Schema::table('favorites_slots', function (Blueprint $table) {
            $table->dateTime('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
        });
    }

    public function down(): void
    {
        Schema::table('favorites_slots', function (Blueprint $table) {
            $table->dropColumn('created_at');
        });
    }
}
