<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsRequiredColumnToPlayerVerificationsTable extends Migration
{
    public function up(): void
    {
        Schema::table('player_verifications', function (Blueprint $table) {
            $table->tinyInteger('is_required')->default(1)->after('status');
        });
    }

    public function down(): void
    {
        Schema::table('player_verifications', function (Blueprint $table) {
            $table->dropColumn('is_required');
        });
    }
}
