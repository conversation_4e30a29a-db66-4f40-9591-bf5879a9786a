<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTablePlayerVipSubStatusChangeGgrField extends Migration
{
    public function up()
    {
        Schema::table('player_vip_sub_status', function (Blueprint $table) {
            $table->bigInteger('ggr')->change();
        });
    }

    public function down()
    {
        Schema::table('player_vip_sub_status', function (Blueprint $table) {
            $table->bigInteger('ggr', false, true)->change();
        });
    }
}
