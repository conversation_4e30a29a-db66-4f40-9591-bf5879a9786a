<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSlotsBetsFreeSpinBoundsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('slots_bets_free_spin_bounds', function (Blueprint $table) {
            $table->unsignedBigInteger('slot_bet_id');
            $table->unsignedBigInteger('free_spin_bound_id');

            $table->foreign('slot_bet_id')->references('id')->on('slots_bets');
            $table->foreign('free_spin_bound_id')->references('id')->on('free_spin_bounds');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('slots_bets_free_spin_bounds');
    }
}
