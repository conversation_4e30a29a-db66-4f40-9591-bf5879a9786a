<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStatusFieldInFreeSpinBoundsTable extends Migration
{
    public function up()
    {
        Schema::table('free_spin_bounds', function (Blueprint $table) {
            $table->string('status')->index()->nullable()->after('is_archived');
        });
    }

    public function down()
    {
        Schema::table('free_spin_bounds', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
}
