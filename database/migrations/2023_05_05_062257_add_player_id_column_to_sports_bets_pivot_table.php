<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPlayerIdColumnToSportsBetsPivotTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sports_bets_pivot', function (Blueprint $table) {
            $table->unsignedBigInteger('player_id')->nullable()->after('event_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sports_bets_pivot', function (Blueprint $table) {
            $table->dropColumn('player_id');
        });
    }
}
