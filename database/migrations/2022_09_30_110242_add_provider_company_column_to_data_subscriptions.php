<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddProviderCompanyColumnToDataSubscriptions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('data_subscriptions', function (Blueprint $table) {
            $table->string('provider_company')->after('provider')->nullable();
        });

        Schema::table('data_subscriptions', function (Blueprint $table) {
            $table->string('provider_company')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('data_subscriptions', function (Blueprint $table) {
            $table->dropColumn('provider_company');
        });
    }
}
