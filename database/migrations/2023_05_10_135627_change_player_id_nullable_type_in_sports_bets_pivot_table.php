<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangePlayerIdNullableTypeInSportsBetsPivotTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sports_bets_pivot', function (Blueprint $table) {
            $table->unsignedBigInteger('player_id')->nullable(false)->change();

            $table->foreign('player_id')
                ->references('id')
                ->on('players');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sports_bets_pivot', function (Blueprint $table) {
            $table->dropForeign(['player_id']);
            $table->unsignedBigInteger('player_id')->nullable()->change();
        });
    }
}
