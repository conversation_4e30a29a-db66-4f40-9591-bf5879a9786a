<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTotalWinColumnToFreeSpinBoundsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('free_spin_bounds', function (Blueprint $table) {
            $table->integer('total_win')
                ->default(0)
                ->after('bet');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('free_spin_bounds', function (Blueprint $table) {
            $table->dropColumn('total_win');
        });
    }
}
