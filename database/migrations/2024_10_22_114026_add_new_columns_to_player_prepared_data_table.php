<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewColumnsToPlayerPreparedDataTable extends Migration
{
    public function up(): void
    {
        Schema::table('player_prepared_data', function (Blueprint $table) {
            $table->unsignedInteger('deposits_count')->nullable()->after('payouts_count');
            $table->timestamp('first_deposit_date')->nullable()->after('deposits_count');
        });
    }

    public function down(): void
    {
        Schema::table('player_prepared_data', function (Blueprint $table) {
            $table->dropColumn('first_deposit_date');
            $table->dropColumn('deposits_count');
        });
    }
}
