<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewColumnsToSlotsProvidersTable extends Migration
{
    public function up(): void
    {
        Schema::table('slots_providers', function (Blueprint $table) {
            $table->string('group_name')->nullable()->after('uuid');
            $table->string('group_weight')->default(0)->after('group_name');
        });
    }

    public function down(): void
    {
        Schema::table('slots_providers', function (Blueprint $table) {
            $table->dropColumn('group_weight');
            $table->dropColumn('group_name');
        });
    }
}
