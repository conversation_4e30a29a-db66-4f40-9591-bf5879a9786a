<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPlayerFieldsFieldInPaykassmaPayments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('paykassma_payments', function (Blueprint $table) {
            $table->json('player_fields')->after('paid')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('paykassma_payments', function (Blueprint $table) {
            $table->dropColumn('player_fields');
        });
    }
}
