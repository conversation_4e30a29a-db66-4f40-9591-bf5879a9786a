<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentsRequisitesBlacklistTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payments_requisites_blacklist', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('player_id');
            $table->unsignedBigInteger('payout_id');
            $table->unsignedBigInteger('blacklist_id');

            $table->foreign('player_id')->references('id')->on('players');
            $table->foreign('payout_id')->references('id')->on('payouts');
            $table->foreign('blacklist_id')->references('id')->on('requisites_blacklist');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payments_requisites_blacklist');
    }
}
