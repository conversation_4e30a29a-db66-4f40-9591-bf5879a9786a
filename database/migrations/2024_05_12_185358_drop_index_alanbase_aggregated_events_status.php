<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropIndexAlanbaseAggregatedEventsStatus extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('alanbase_aggregated_events', static function (Blueprint $table) {
            $table->dropIndex('alanbase_aggregated_events_status_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('alanbase_aggregated_events', static function (Blueprint $table) {
            $table->index('status');
        });
    }
}
