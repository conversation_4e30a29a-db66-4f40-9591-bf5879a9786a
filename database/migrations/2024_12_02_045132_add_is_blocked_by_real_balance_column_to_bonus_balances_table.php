<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsBlockedByRealBalanceColumnToBonusBalancesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bonus_balances', function (Blueprint $table) {
            $table->boolean('is_blocked_by_real_balance')->nullable()->default(null)->after('bets');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bonus_balances', function (Blueprint $table) {
            $table->dropColumn('is_blocked_by_real_balance');
        });
    }
}
