<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPointsDailyToTournamentJetxTable extends Migration
{
    private const TABLE = 'tournament_jetx_2023';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table(self::TABLE, function (Blueprint $table) {
            $table->unsignedInteger('points_daily')->after('points');
            $table->index('points_daily');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tournament_jetx', function (Blueprint $table) {
            $table->dropIndex('points_daily');
            $table->dropColumn('points_daily');
        });
    }
}
