<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeIntToBigintInTournamentIpl2023Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tournament_ipl_2023', function (Blueprint $table) {
            $table->unsignedBigInteger('casino_sum')->change();
            $table->unsignedBigInteger('sport_sum')->change();
            $table->unsignedInteger('points')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tournament_ipl_2023', function (Blueprint $table) {
            //
        });
    }
}
