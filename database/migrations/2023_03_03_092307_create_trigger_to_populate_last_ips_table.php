<?php declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreateTriggerToPopulateLastIpsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        // Run manauly with user with properly permissions

//        DB::unprepared(<<<QUERY
//CREATE TRIGGER insert_last_ip
//    AFTER INSERT
//    ON players
//    FOR EACH ROW
//BEGIN
//    IF NEW.last_seen_ip IS NOT NULL
//    THEN
//        INSERT INTO last_ips (id, player_id, ip_address)
//            VALUE (NEW.id, NEW.id, NEW.last_seen_ip);
//    END IF;
//END;
//
//CREATE TRIGGER update_last_ip
//    AFTER UPDATE
//    ON players
//    FOR EACH ROW
//BEGIN
//    IF NEW.last_seen_ip IS NOT NULL
//    THEN
//        INSERT INTO last_ips (id, player_id, ip_address)
//            VALUE (NEW.id, NEW.id, NEW.last_seen_ip)
//        ON DUPLICATE KEY UPDATE ip_address = NEW.last_seen_ip;
//    END IF;
//END;
//QUERY
//        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Run manauly with user with properly permissions

//        DB::unprepared('drop trigger insert_last_ip;');
//        DB::unprepared('drop trigger update_last_ip;');
    }
}
