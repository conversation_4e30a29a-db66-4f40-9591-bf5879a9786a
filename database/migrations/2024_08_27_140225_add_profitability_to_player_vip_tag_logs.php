<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

final class AddProfitabilityToPlayerVipTagLogs extends Migration
{
    public function up(): void
    {
        Schema::table('player_vip_tag_logs', function (Blueprint $table) {
            $table->integer('profitability')
                ->nullable()
                ->after('player_id')
                ->comment('Player profitability when tag was set');
        });
    }

    public function down(): void
    {
        Schema::table('player_vip_tag_logs', function (Blueprint $table) {
            $table->dropColumn('profitability');
        });
    }
}
