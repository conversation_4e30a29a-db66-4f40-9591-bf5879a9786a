<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexToBonusPlayerCardBonusInfoIdColumn extends Migration
{
    public function up(): void
    {
        Schema::table('bonus_player_card', function (Blueprint $table) {
            $table->index('bonus_info_id');
        });
    }

    public function down(): void
    {
        Schema::table('bonus_player_card', function (Blueprint $table) {
            $table->dropIndex(['bonus_info_id']);
        });
    }
}
