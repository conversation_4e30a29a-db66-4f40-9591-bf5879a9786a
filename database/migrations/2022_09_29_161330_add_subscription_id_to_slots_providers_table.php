<?php

use App\Models\DataSubscription;
use App\Models\SlotGame;
use App\Models\SlotProvider;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Skipper\Repository\Exceptions\StorageException;
use WhiteLabelApp\Services\SlotServices\ProviderService;

class AddSubscriptionIdToSlotsProvidersTable extends Migration
{
    private Collection $subscriptions;

    /**
     * Run the migrations.
     *
     * @return void
     * @throws StorageException
     * @throws Exception
     */
    public function up()
    {
        Schema::table('slots_providers', function (Blueprint $table) {
            $table->unsignedBigInteger('subscription_id')->after('client_id')->nullable();
        });

        Schema::table('slots_providers', function (Blueprint $table) {
            $table->dropUnique(['client_id', 'name']);
            $table->unsignedBigInteger('subscription_id')->nullable(false)->change();
            $table->unique(['client_id', 'name', 'subscription_id']);
            $table->foreign('subscription_id')->references('id')->on('data_subscriptions');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //We need to back up tables and do rollback with load backups
    }
}
