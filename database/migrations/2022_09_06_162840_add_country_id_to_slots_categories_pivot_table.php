<?php

use App\Models\Country;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddCountryIdToSlotsCategoriesPivotTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('slots_categories_pivot', function (Blueprint $table) {
            $table->unsignedBigInteger('country_id')->after('category_id')->default(0);

            $table->dropForeign('slots_categories_pivot_category_id_foreign');
            $table->dropForeign('slots_categories_pivot_slot_id_foreign');

            $table->dropPrimary();
            $table->primary(['slot_id', 'category_id', 'country_id']);

            $table->foreign('slot_id')->references('id')->on('slots');
            $table->foreign('category_id')->references('id')->on('slots_categories');
            $table->foreign('country_id')->references('id')->on('countries');
        });

        Schema::table('slots_categories_pivot', function (Blueprint $table) {
            $table->unsignedBigInteger('country_id')->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('slots_categories_pivot', function (Blueprint $table) {
            $table->dropForeign('slots_categories_pivot_category_id_foreign');
            $table->dropForeign('slots_categories_pivot_slot_id_foreign');
            $table->dropForeign('slots_categories_pivot_country_id_foreign');

            $table->dropPrimary();
            $table->primary(['slot_id', 'category_id']);

            $table->dropColumn('country_id');

            $table->foreign('slot_id')->references('id')->on('slots');
            $table->foreign('category_id')->references('id')->on('slots_categories');
        });
    }
}
