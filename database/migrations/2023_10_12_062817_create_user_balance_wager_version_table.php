<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserBalanceWagerVersionTable extends Migration
{
    private const USER_BALANCES = 'user_balances';
    private const USER_BALANCE_WAGER_VERSIONS = 'balance_wager_versions';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create(self::USER_BALANCE_WAGER_VERSIONS, function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('player_balance_id');
            $table->unsignedTinyInteger('wager_version');
            $table->timestamps();

            $table->foreign('player_balance_id')->references('id')->on(self::USER_BALANCES);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists(self::USER_BALANCE_WAGER_VERSIONS);
    }
}
