<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPlayerIdColumnToFreebetBonusInfoTabel extends Migration
{
    public function up(): void
    {
        Schema::table('mass_freebet_bonus_info', function (Blueprint $table) {
            $table->bigInteger('player_id')->nullable()->index();
            $table->string('external_player_id')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('mass_freebet_bonus_info', function (Blueprint $table) {
            $table->dropColumn('player_id');
            $table->string('external_player_id')->nullable(false)->change();
        });
    }
}
