<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropIsOnetimeColumnInBonusInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bonus_info', function (Blueprint $table) {
            $table->dropColumn('is_onetime');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bonus_info', function (Blueprint $table) {
            $table->boolean('is_onetime')->after('is_welcome')->nullable();
        });
    }
}
