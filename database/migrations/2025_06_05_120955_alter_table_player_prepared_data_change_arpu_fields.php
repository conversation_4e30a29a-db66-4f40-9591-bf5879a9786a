<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTablePlayerPreparedDataChangeArpuFields extends Migration
{
    public function up()
    {
        Schema::table('player_prepared_data', function (Blueprint $table) {
            $table->integer('arpu_one_month')->nullable()->change();
            $table->integer('arpu_three_month')->nullable()->change();
        });
    }

    public function down()
    {
        //
    }
}
