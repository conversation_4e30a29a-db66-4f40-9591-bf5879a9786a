<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDescriptionAndConditionFieldsInFreeSpinsTable extends Migration
{
    public function up()
    {
        Schema::table('free_spins', function (Blueprint $table) {
            $table->json('condition')->nullable()->after('title');
            $table->json('description')->nullable()->after('title');
            $table->json('freespin_name')->nullable()->after('title');
            $table->string('image')->nullable()->after('type');
        });
    }

    public function down()
    {
        Schema::table('free_spins', function (Blueprint $table) {
            $table->dropColumn('condition');
            $table->dropColumn('description');
            $table->dropColumn('freespin_name');
            $table->dropColumn('image');
        });
    }
}
