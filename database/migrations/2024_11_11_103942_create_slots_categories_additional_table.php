<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSlotsCategoriesAdditionalTable extends Migration
{
    public function up(): void
    {
        Schema::create('slots_categories_additional', function (Blueprint $table) {
            $table->id();
            $table->integer('client_id');
            $table->string('internal_id')->nullable();
            $table->string('name');
            $table->string('slug');
            $table->unsignedBigInteger('section_id');
            $table->string('image')->nullable();
            $table->boolean('enabled');
            $table->integer('weight');
            $table->uuid('uuid')->unique();
            $table->timestamps();

            $table->unique(['slug', 'section_id']);
            $table->foreign('section_id')->references('id')->on('sections');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('slots_categories_additional');
    }
}
