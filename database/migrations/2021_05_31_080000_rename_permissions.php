<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class RenamePermissions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('permissions')->where('name', 'get-role')->update(['name' => 'roles-list']);
        DB::table('permissions')->where('name', 'create-role')->update(['name' => 'roles-add']);
        DB::table('permissions')->where('name', 'update-role')->update(['name' => 'roles-edit']);
        DB::table('permissions')->where('name', 'delete-role')->update(['name' => 'roles-delete']);

        DB::table('permissions')->where('name', 'get-banner')->update(['name' => 'banners-list']);
        DB::table('permissions')->where('name', 'create-banner')->update(['name' => 'banners-add']);
        DB::table('permissions')->where('name', 'update-banner')->update(['name' => 'banners-edit']);
        DB::table('permissions')->where('name', 'delete-banner')->update(['name' => 'banners-delete']);

        DB::table('permissions')->where('name', 'get-page')->update(['name' => 'seo-list']);
        DB::table('permissions')->where('name', 'create-page')->update(['name' => 'seo-add']);
        DB::table('permissions')->where('name', 'update-page')->update(['name' => 'seo-edit']);
        DB::table('permissions')->where('name', 'delete-page')->update(['name' => 'seo-delete']);

        DB::table('permissions')->where('name', 'get-player')->update(['name' => 'users-list']);
        DB::table('permissions')->where('name', 'search-player')->update(['name' => 'users-search']);
        DB::table('permissions')->where('name', 'update-player')->update(['name' => 'users-edit']);

        DB::table('permissions')->where('name', 'get-user')->update(['name' => 'moderators-list']);
        DB::table('permissions')->where('name', 'create-user')->update(['name' => 'moderators-add']);
        DB::table('permissions')->where('name', 'update-user')->update(['name' => 'moderators-edit']);
        DB::table('permissions')->where('name', 'delete-user')->update(['name' => 'moderators-delete']);

        DB::table('permissions')->where('name', 'get-language')->update(['name' => 'languages-list']);
        DB::table('permissions')->where('name', 'update-language')->update(['name' => 'languages-edit']);

        DB::table('permissions')->where('name', 'get-payment')->update(['name' => 'payments-list']);
        DB::table('permissions')->where('name', 'get-deposit')->update(['name' => 'deposits-list']);
        DB::table('permissions')->where('name', 'update-country')->update(['name' => 'countries-edit']);
        DB::table('permissions')->where('name', 'get-currency')->update(['name' => 'currencies-list']);
        DB::table('permissions')->where('name', 'update-currency')->update(['name' => 'currencies-edit']);
        DB::table('permissions')->where('name', 'get-payout')->update(['name' => 'payouts-list']);
        DB::table('permissions')->where('name', 'update-payout')->update(['name' => 'payouts-edit']);
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('permissions')->where('name', 'roles-list')->update(['name' => 'get-role']);
        DB::table('permissions')->where('name', 'roles-add')->update(['name' => 'create-role']);
        DB::table('permissions')->where('name', 'roles-edit')->update(['name' => 'update-role']);
        DB::table('permissions')->where('name', 'roles-delete')->update(['name' => 'delete-role']);

        DB::table('permissions')->where('name', 'banners-list')->update(['name' => 'get-banner']);
        DB::table('permissions')->where('name', 'banners-add')->update(['name' => 'create-banner']);
        DB::table('permissions')->where('name', 'banners-edit')->update(['name' => 'update-banner']);
        DB::table('permissions')->where('name', 'banners-delete')->update(['name' => 'delete-banner']);

        DB::table('permissions')->where('name', 'seo-list')->update(['name' => 'get-page']);
        DB::table('permissions')->where('name', 'seo-add')->update(['name' => 'create-page']);
        DB::table('permissions')->where('name', 'seo-edit')->update(['name' => 'update-page']);
        DB::table('permissions')->where('name', 'seo-delete')->update(['name' => 'delete-page']);

        DB::table('permissions')->where('name', 'users-list')->update(['name' => 'get-player']);
        DB::table('permissions')->where('name', 'users-search')->update(['name' => 'search-player']);
        DB::table('permissions')->where('name', 'users-edit')->update(['name' => 'update-player']);

        DB::table('permissions')->where('name', 'moderators-list')->update(['name' => 'get-user']);
        DB::table('permissions')->where('name', 'moderators-add')->update(['name' => 'create-user']);
        DB::table('permissions')->where('name', 'moderators-edit')->update(['name' => 'update-user']);
        DB::table('permissions')->where('name', 'moderators-delete')->update(['name' => 'delete-user']);

        DB::table('permissions')->where('name', 'languages-list')->update(['name' => 'get-language']);
        DB::table('permissions')->where('name', 'languages-edit')->update(['name' => 'update-language']);

        DB::table('permissions')->where('name', 'payments-list')->update(['name' => 'get-payment']);
        DB::table('permissions')->where('name', 'deposits-list')->update(['name' => 'get-deposit']);
        DB::table('permissions')->where('name', 'countries-edit')->update(['name' => 'update-country']);
        DB::table('permissions')->where('name', 'currencies-list')->update(['name' => 'get-currency']);
        DB::table('permissions')->where('name', 'currencies-edit')->update(['name' => 'update-currency']);
        DB::table('permissions')->where('name', 'payouts-list')->update(['name' => 'get-payout']);
        DB::table('permissions')->where('name', 'payouts-edit')->update(['name' => 'update-payout']);
    }
}
