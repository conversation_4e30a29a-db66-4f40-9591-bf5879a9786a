<?php declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameCoreSystemStatusToStatusInPayoutStatusesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $payoutStatuses = \App\Models\PayoutStatus::all();
        foreach ($payoutStatuses as $payoutStatus) {
            if (is_string($payoutStatus->payouts_system_status)) {
                $payoutStatus->core_system_status = $payoutStatus->payouts_system_status;
                $payoutStatus->payouts_system_status =  null;
                $payoutStatus->save();
            }
        }

        Schema::table('payout_statuses', static function (Blueprint $table) {
            $table->renameColumn('core_system_status', 'status');
            $table->integer('payouts_system_status')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('payout_statuses', static function (Blueprint $table) {
            $table->renameColumn('status', 'core_system_status');
            $table->string('payouts_system_status')->change();
        });
    }
}
