<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class GenerateUuidForBonusBalancesTable extends Migration
{

    protected int $chunkSize = 10000;
    protected string $tableName = 'bonus_balances';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        do {
            $affected = DB::update("
                UPDATE {$this->tableName}
                SET uuid = UUID()
                WHERE uuid IS NULL
                LIMIT {$this->chunkSize}
            ");

            if ($affected === 0) {
                break;
            }
        } while (true);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        do {
            $affected = DB::update("
                UPDATE {$this->tableName}
                SET uuid = null
                WHERE uuid IS NOT NULL
                LIMIT {$this->chunkSize}
            ");

            if ($affected === 0) {
                break;
            }
        } while (true);
    }
}
