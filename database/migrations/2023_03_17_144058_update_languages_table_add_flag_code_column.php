<?php declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateLanguagesTableAddFlagCodeColumn extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('languages', static function (Blueprint $table) {
            $table->string('flag_code', 2)->after('code')->nullable();

            $table->dropUnique(['client_id', 'code']);
            $table->unique(['client_id', 'code', 'flag_code']);
            $table->index(['client_id', 'code']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('languages', static function (Blueprint $table) {
            $table->unique(['client_id', 'code']);
            $table->dropUnique(['client_id', 'code', 'flag_code']);
            $table->dropIndex(['client_id', 'code']);

            $table->dropColumn('flag_code');
            $table->unique(['client_id', 'code']);
        });
    }
}
