<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCountrySlotsProvidersGroupsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('country_slots_providers_groups', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('country_id')->index();
            $table->string('slot_provider_group_name')->index();
            $table->smallInteger('weight')->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('country_slots_providers_groups');
    }
}
