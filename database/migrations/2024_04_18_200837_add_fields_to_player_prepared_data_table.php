<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToPlayerPreparedDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('player_prepared_data', function (Blueprint $table) {
            $table->integer('approved_payouts_count')->nullable()->after('payouts_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('player_prepared_data', function (Blueprint $table) {
            $table->dropColumn('approved_payouts_count');
        });
    }
}
