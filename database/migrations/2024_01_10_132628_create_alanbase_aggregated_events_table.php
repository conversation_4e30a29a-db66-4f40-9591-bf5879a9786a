<?php declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Listeners\AlanbaseSubscriber;

class CreateAlanbaseAggregatedEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('alanbase_aggregated_events', static function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('player_id');
            $table->unsignedBigInteger('min_slot_bet_id');
            $table->unsignedBigInteger('max_slot_bet_id');
            $table->string('currency', 3);
            $table->enum('status', [AlanbaseSubscriber::STATUS_PENDING, AlanbaseSubscriber::STATUS_CONFIRMED, AlanbaseSubscriber::STATUS_REJECTED])->index();
            $table->integer('spin_amount');
            $table->integer('spin_result_amount');
            $table->integer('count')->index();
            $table->timestamps();
            $table->index('updated_at');

            $table->foreign('player_id')->references('id')->on('players');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('alanbase_aggregated_events');
    }
}
