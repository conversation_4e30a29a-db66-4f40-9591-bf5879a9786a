<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropForeignKeysConnectedToSlotsBetsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('slots_bets_free_spin_bounds', function (Blueprint $table) {
            $table->dropForeign(['slot_bet_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('slots_bets_free_spin_bounds', function (Blueprint $table) {
            $table->foreign('slot_bet_id')->references('id')->on('free_spin_bounds');
        });
    }
}
