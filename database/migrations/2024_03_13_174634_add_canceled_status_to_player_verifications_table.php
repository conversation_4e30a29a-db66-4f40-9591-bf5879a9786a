<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddCanceledStatusToPlayerVerificationsTable extends Migration
{
    public function up(): void
    {
        DB::statement("ALTER TABLE player_verifications MODIFY COLUMN status ENUM('unused', 'pending', 'completed', 'canceled')");
    }

    public function down(): void
    {
        DB::statement("UPDATE player_verifications SET status = 'unused' WHERE status = 'canceled'");

        DB::statement("ALTER TABLE player_verifications MODIFY COLUMN status ENUM('unused', 'pending', 'completed')");
    }
}
