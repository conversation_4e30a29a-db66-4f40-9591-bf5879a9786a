<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsForSmarticoToBonusInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bonus_info', function (Blueprint $table) {
            $table->boolean('is_for_smartico')->default(false)->after('is_welcome');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bonus_info', function (Blueprint $table) {
            $table->dropColumn('is_for_smartico');
        });
    }
}
