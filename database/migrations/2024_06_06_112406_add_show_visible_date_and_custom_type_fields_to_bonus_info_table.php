<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddShowVisibleDateAndCustomTypeFieldsToBonusInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bonus_info', function (Blueprint $table) {
            $table->boolean('show_visible_date')->default(true)->after('active');
            $table->string('custom_type')->nullable()->after('name');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bonus_info', function (Blueprint $table) {
            $table->dropColumn('show_visible_date');
            $table->dropColumn('custom_type');
        });
    }
}
