<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropDepositBonusInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::drop('deposit_bonus_info');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('deposit_bonus_info', static function (Blueprint $table): void {
            $table->id();
            $table->bigInteger('bonus_id')->index();
            $table->bigInteger('smartico_bonus_id')->index();
            $table->bigInteger('player_id')->nullable()->index();
            $table->string('email')->nullable()->index();
            $table->bigInteger('amount')->nullable();
            $table->string('status')->default('in_process')->index();
            $table->text('text')->nullable();
            $table->timestamps();
        });
    }
}
