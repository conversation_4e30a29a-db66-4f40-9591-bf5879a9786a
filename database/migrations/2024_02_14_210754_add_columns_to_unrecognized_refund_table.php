<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsToUnrecognizedRefundTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('unrecognized_refund', function (Blueprint $table) {
            $table->string('parent_external_id')->nullable()->after('external_id')->index();
            $table->string('job_status')->nullable()->after('currency')->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('unrecognized_refund', function (Blueprint $table) {
//            $table->dropColumn('parent_external_id');
            $table->dropColumn('job_status');
        });
    }
}
