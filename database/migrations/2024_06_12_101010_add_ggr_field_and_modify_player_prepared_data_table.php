<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddGgrFieldAndModifyPlayerPreparedDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('player_prepared_data', function (Blueprint $table) {
            $table->integer('payouts_count')->nullable()->change();
            $table->bigInteger('ggr')->default(null)->nullable()->after('approved_payouts_count');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('player_prepared_data', function (Blueprint $table) {
            $table->integer('payouts_count')->default(0)->change();
            $table->dropColumn('ggr');
        });
    }
}
