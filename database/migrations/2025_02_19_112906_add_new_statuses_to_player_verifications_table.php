<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddNewStatusesToPlayerVerificationsTable extends Migration
{
    public function up(): void
    {
        Schema::table('player_verifications', function (Blueprint $table) {
            DB::statement("ALTER TABLE player_verifications MODIFY COLUMN status ENUM('unused','pending','completed','canceled', 'pending_recommended', 'self_verified', 'fail_recommended')");
        });
    }

    public function down(): void
    {
        DB::statement("ALTER TABLE player_verifications MODIFY COLUMN status ENUM('unused','pending','completed','canceled')");
    }
}
