<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCardColorsShortDescriptionColumnsToBonusesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('bonuses', static function (Blueprint $table) {
            $table->json('short_description')->nullable()->after('description');
            $table->json('colors')->nullable()->after('short_description');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('bonuses', static function (Blueprint $table) {
            $table->dropColumn('short_description');
            $table->dropColumn('colors');
        });
    }
}
