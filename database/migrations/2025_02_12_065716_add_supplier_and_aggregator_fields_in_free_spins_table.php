<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSupplierAndAggregatorFieldsInFreeSpinsTable extends Migration
{
    public function up()
    {
        Schema::table('free_spins', function (Blueprint $table) {
            $table->string('aggregator')->nullable()->after('id');
            $table->string('supplier')->default('slotegrator')->after('id');
            $table->string('title')->nullable()->after('name');
        });
    }

    public function down()
    {
        Schema::table('free_spins', function (Blueprint $table) {
            $table->dropColumn('supplier');
            $table->dropColumn('aggregator');
            $table->dropColumn('title');
        });
    }
}
