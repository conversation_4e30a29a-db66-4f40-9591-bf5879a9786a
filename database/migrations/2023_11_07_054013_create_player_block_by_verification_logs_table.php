<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlayerBlockByVerificationLogsTable extends Migration
{
    private const PLAYER_BLOCKED_BY_VERIFICATION_LOGS_TABLE = 'player_block_by_verification_logs';
    private const PLAYERS_TABLE = 'players';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create(self::PLAYER_BLOCKED_BY_VERIFICATION_LOGS_TABLE, function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('player_id');
            $table->enum('mode', ['manual', 'auto'])->default('auto');
            $table->boolean('value');
            $table->string('admin_name', 50)->nullable();
            $table->timestamp('created_at');

            $table->foreign('player_id')->on(self::PLAYERS_TABLE)->references('id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists(self::PLAYER_BLOCKED_BY_VERIFICATION_LOGS_TABLE);
    }
}
