<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMassFreebetBonusInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mass_freebet_bonus_info', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('bonus_id')->index();
            $table->bigInteger('template_id')->nullable()->index();
            $table->string('external_player_id')->index();
            $table->string('currency');
            $table->bigInteger('amount')->nullable();
            $table->string('status')->default('in_process')->index();
            $table->text('text')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mass_freebet_bonus_info');
    }
}
