<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSystemFieldToPaykassmaPayments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('paykassma_payments', function (Blueprint $table) {
            $table->string('payment_system')->index()->after('player_id')->default('paykassma');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('paykassma_payments', function (Blueprint $table) {
            $table->dropColumn('payment_system');
        });
    }
}
