<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDepositBonusInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('deposit_bonus_info', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('bonus_id')->index();
            $table->bigInteger('smartico_bonus_id')->index();
            $table->bigInteger('player_id')->nullable()->index();
            $table->string('email')->index();
            $table->bigInteger('amount')->nullable();
            $table->string('status')->default('in_process')->index();
            $table->text('text')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('deposit_bonus_info');
    }
}
