<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class RenameBalanceSessionsTableAndRecreateNew extends Migration
{
    public function up(): void
    {
        $this->dropForeignKeys('balance_sessions');

        Schema::rename('balance_sessions', 'balance_sessions_archive');

        Schema::create('balance_sessions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('player_id');
            $table->unsignedBigInteger('balance_id')->nullable();
            $table->unsignedBigInteger('bonus_id')->nullable();
            $table->string('token')->unique();
            $table->string('flag')->nullable();
            $table->timestamps();

            $table->index('created_at');
        });

        $this->createForeignKeys('balance_sessions');
    }

    public function down(): void
    {
        Schema::drop('balance_sessions');

        Schema::rename('balance_sessions_archive', 'balance_sessions');

        $this->createForeignKeys('balance_sessions');
    }

    private function dropForeignKeys(string $tableName): void
    {
        Schema::table($tableName, function (Blueprint $table) {
            $table->dropForeign(['player_id']);
            $table->dropForeign(['balance_id']);
            $table->dropForeign(['bonus_id']);
        });
    }

    private function createForeignKeys(string $tableName): void
    {
        Schema::table($tableName, function (Blueprint $table) {
            $table->foreign('player_id')
                ->references('id')
                ->on('players')
                ->onDelete('restrict')
                ->onUpdate('restrict');

            $table->foreign('balance_id')
                ->references('id')
                ->on('user_balances')
                ->onDelete('restrict')
                ->onUpdate('restrict');

            $table->foreign('bonus_id')
                ->references('id')
                ->on('bonus_balances')
                ->onDelete('restrict')
                ->onUpdate('restrict');
        });
    }
}
