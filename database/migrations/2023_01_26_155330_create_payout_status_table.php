<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePayoutStatusTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payout_statuses', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('payouts_id')->unique();
            $table->string('payouts_system_status')->nullable();
            $table->string('core_system_status')->default('in_process')->nullable();
            $table->string('transaction_token')->nullable();
            $table->bigInteger('player_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payout_statuses');
    }
}
