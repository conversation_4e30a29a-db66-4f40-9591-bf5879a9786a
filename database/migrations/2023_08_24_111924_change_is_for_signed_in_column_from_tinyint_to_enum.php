<?php

use App\Models\Enums\BannerSignedEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeIsForSignedInColumnFromTinyintToEnum extends Migration
{
    public function up(): void
    {
        Schema::table('banners', function (Blueprint $table) {
            $table->enum('is_for_signed_in_tmp', BannerSignedEnum::SIGNED_TYPES)->after('country');
        });

        Schema::table('banners', function (Blueprint $table) {
            $table->dropColumn('is_for_signed_in');
            $table->renameColumn('is_for_signed_in_tmp', 'is_for_signed_in');
        });
    }

    public function down(): void
    {
        Schema::table('banners', function (Blueprint $table) {
            $table->tinyInteger('is_for_signed_in_tmp')->nullable()->after('country');
        });

        Schema::table('banners', function (Blueprint $table) {
            $table->dropColumn('is_for_signed_in');
            $table->renameColumn('is_for_signed_in_tmp', 'is_for_signed_in');
        });
    }
}
