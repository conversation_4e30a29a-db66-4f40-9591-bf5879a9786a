<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableSmartLinkLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('smart_link_logs', function (Blueprint $table) {
            $table->id();
            $table->integer('smart_link_id')->unsigned();
            $table->string('name', 100);
            $table->string('old_value', 255);
            $table->string('new_value', 255);
            $table->string('old_domain', 255);
            $table->string('new_domain', 255);
            $table->string('admin_email', 255);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('smart_link_logs');
    }
}
