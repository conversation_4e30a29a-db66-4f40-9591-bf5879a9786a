<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStartAtExpireAtCanceledAtToFreeSpinBoundsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('free_spin_bounds', function (Blueprint $table) {
            $table->timestamp('start_at')->nullable()->after('message');
            $table->timestamp('expire_at')->nullable()->after('start_at');
            $table->timestamp('canceled_at')->nullable()->after('expire_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('free_spin_bounds', function (Blueprint $table) {
            $table->dropColumn('start_at');
            $table->dropColumn('expire_at');
            $table->dropColumn('canceled_at');
        });
    }
}
