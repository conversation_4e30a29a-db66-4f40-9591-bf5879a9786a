<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTournamentWorldChamp2023Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tournament_world_champ_2023', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('player_id');
            $table->string('currency', 3);
            $table->unsignedBigInteger('sport_sum');
            $table->unsignedMediumInteger('points');
            $table->timestamps();

            $table->foreign('player_id')->references('id')->on('players');
            $table->index('points');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tournament_world_champ_2023');
    }
}
