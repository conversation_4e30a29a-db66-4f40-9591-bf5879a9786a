<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGivenMassOnetimeBonusInfo extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('mass_onetime_bonus_info', static function (Blueprint $table) {
            $table->id();
            $table->bigInteger('bonus_id')->index();
            $table->bigInteger('player_id')->nullable()->index();
            $table->string('email')->index();
            $table->bigInteger('amount')->nullable();
            $table->string('status')->default('in_process')->index();
            $table->text('text')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('mass_onetime_bonus_info');
    }
}
