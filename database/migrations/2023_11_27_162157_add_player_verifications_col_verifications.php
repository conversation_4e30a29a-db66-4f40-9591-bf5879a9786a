<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPlayerVerificationsColVerifications extends Migration
{
    private const PLAYER_VERIFICATIONS_TABLE = 'player_verifications';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table(self::PLAYER_VERIFICATIONS_TABLE, function (Blueprint $table) {
            $table->json('verifications')->after('verified');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(self::PLAYER_VERIFICATIONS_TABLE, function (Blueprint $table) {
            $table->dropColumn('verifications');
        });
    }
}
