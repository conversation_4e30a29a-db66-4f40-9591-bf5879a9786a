<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldSlotExternalIdInFreeSpinsTable extends Migration
{
    public function up()
    {
        Schema::table('free_spins', function (Blueprint $table) {
            $table->string('slot_external_id')->nullable()->after('slot_id');
            $table->bigInteger('bet')->nullable()->change();
        });
    }

    public function down()
    {
        Schema::table('free_spins', function (Blueprint $table) {
            $table->dropColumn('slot_external_id');
            $table->bigInteger('bet')->nullable(false)->change();
        });
    }
}
