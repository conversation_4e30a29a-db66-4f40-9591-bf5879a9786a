<?php

use AppV3\Settings\Repositories\SettingRepository;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTablePlayerPreparedDataAddArpuFields extends Migration
{

    public function up()
    {
        Schema::table('player_prepared_data', function (Blueprint $table) {
            $table->integer('arpu_one_month')->after('ggr')->nullable(false)->default(0);
            $table->integer('arpu_three_month')->nullable()->after('arpu_one_month')->nullable(false)->default(0);
        });
    }


    public function down()
    {
        Schema::table('player_prepared_data', function (Blueprint $table) {
            $table->dropColumn('arpu_one_month');
            $table->dropColumn('arpu_three_month');
        });
    }
}
