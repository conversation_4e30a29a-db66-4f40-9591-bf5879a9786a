<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropMassFreebetBonusInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::drop('mass_freebet_bonus_info');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('mass_freebet_bonus_info', static function (Blueprint $table): void {
            $table->id();
            $table->bigInteger('bonus_id')->index();
            $table->string('smartico_bonus_id')->nullable()->index();
            $table->bigInteger('template_id')->nullable()->index();
            $table->string('external_player_id')->nullable()->index();
            $table->string('currency');
            $table->bigInteger('amount')->nullable();
            $table->string('status')->default('in_process')->index();
            $table->text('text')->nullable();
            $table->timestamps();
            $table->bigInteger('player_id')->nullable()->index();
        });
    }
}
