<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropMassOnetimeBonusInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::drop('mass_onetime_bonus_info');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::create('mass_onetime_bonus_info', static function (Blueprint $table): void {
            $table->id();
            $table->bigInteger('bonus_id')->index();
            $table->string('smartico_bonus_id')->nullable()->index();
            $table->bigInteger('player_id')->nullable()->index();
            $table->string('email')->nullable()->index();
            $table->bigInteger('amount')->nullable();
            $table->string('status')->default('in_process')->index();
            $table->text('text')->nullable();
            $table->timestamps();
        });
    }
}
