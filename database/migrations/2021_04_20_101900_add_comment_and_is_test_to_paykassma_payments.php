<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCommentAndIsTestToPaykassmaPayments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('paykassma_payments', function (Blueprint $table) {
            $table->string('comment')->after('paid')->nullable();
        });
        Schema::table('paykassma_payments', function (Blueprint $table) {
            $table->boolean('is_test')->after('comment')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('paykassma_payments', function (Blueprint $table) {
            $table->dropColumn('comment');
        });
        Schema::table('paykassma_payments', function (Blueprint $table) {
            $table->dropColumn('is_test');
        });
    }
}
