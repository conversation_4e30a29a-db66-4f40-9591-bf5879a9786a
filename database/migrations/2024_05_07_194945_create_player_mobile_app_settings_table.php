<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlayerMobileAppSettingsTable extends Migration
{

    public function up(): void
    {
        Schema::create('player_mobile_app_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('player_id');
            $table->boolean('is_push_enabled')->default(false);

            $table->index('player_id');
        });
    }


    public function down(): void
    {
        Schema::dropIfExists('player_mobile_app_settings');
    }
}
