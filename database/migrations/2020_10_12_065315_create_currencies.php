<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCurrencies extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('iso', 3);
            $table->integer('client_id');
            $table->boolean('is_available')->default(false);
            $table->integer('min_bet')->default(1);
            $table->integer('max_win')->default(100000000);
            $table->timestamps();

            $table->unique(['client_id', 'iso']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('currencies');
    }
}
