<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToPlayersPartnerInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('players_partner_info', function (Blueprint $table) {
            $table->integer('a_b_testing_new_password_logic')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('players_partner_info', function (Blueprint $table) {
            $table->dropColumn('a_b_testing_new_password_logic');
        });
    }
}
