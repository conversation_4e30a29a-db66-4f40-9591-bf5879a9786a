<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLandingIdToPlayersPartnerInfoTable extends Migration
{
    public function up(): void
    {
        if (Schema::hasColumn('players_partner_info', 'landing_id')) {
            return;
        }

        Schema::table('players_partner_info', function (Blueprint $table) {
            $table->bigInteger('landing_id')->after('partner_id')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('players_partner_info', function (Blueprint $table) {
            $table->dropColumn('landing_id');
        });
    }
}
