<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAggregatorToIndexInSlotsProvidersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('slots_providers', function (Blueprint $table) {
            $table->dropIndex('slots_providers_client_id_name_subscription_id_unique');

            $table->unique([
                'subscription_id',
                'name',
                'aggregator',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('slots_providers', function (Blueprint $table) {
            $table->dropIndex('slots_providers_subscription_id_name_aggregator_unique');

            $table->unique([
                'client_id',
                'name',
                'subscription_id',
            ]);
        });
    }
}
