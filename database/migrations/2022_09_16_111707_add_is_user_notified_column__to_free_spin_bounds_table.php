<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsUserNotifiedColumnToFreeSpinBoundsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('free_spin_bounds', function (Blueprint $table) {
            $table->boolean('is_user_notified')->after('is_active')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('free_spin_bounds', function (Blueprint $table) {
            $table->dropColumn('is_user_notified');
        });
    }
}
