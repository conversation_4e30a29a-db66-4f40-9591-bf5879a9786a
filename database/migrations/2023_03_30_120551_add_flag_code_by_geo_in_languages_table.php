<?php

use App\Models\Language;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFlagCodeByGeoInLanguagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     * @throws JsonException
     */
    public function up(): void
    {
        Schema::table('languages', function (Blueprint $table) {
            $table->json('flag_code_by_geo')->after('flag_code')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('languages', function (Blueprint $table) {
            $table->dropColumn('flag_code_by_geo');
        });
    }
}
