<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsBonusBarredToSlotsAndSlotsProvidersTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('slots', function (Blueprint $table) {
            $table->boolean('is_bonus_barred')->after('suspended')->default(false);
        });
        Schema::table('slots_providers', function (Blueprint $table) {
            $table->boolean('is_bonus_barred')->after('suspended')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('slots', function (Blueprint $table) {
            $table->dropColumn('is_bonus_barred');
        });
        Schema::table('slots_providers', function (Blueprint $table) {
            $table->dropColumn('is_bonus_barred');
        });
    }
}
