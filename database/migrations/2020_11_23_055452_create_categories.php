<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCategories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('categories')) {
            Schema::create('categories', function (Blueprint $table) {
                $table->id();
                $table->integer('client_id');
                $table->string('slug');
                $table->string('type');
                $table->string('name');
                $table->boolean('enabled')->default(true);
                $table->integer('weight')->default(0);
                $table->timestamps();
                $table->unique(['client_id', 'type', 'slug']);
            });
            Schema::dropIfExists('payment_gateways');
            Schema::dropIfExists('countries');
            Schema::dropIfExists('currencies');
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('categories');
        Schema::create('payment_gateways', function (Blueprint $table) {
            $table->id();
            $table->integer('client_id');
            $table->string('name');
            $table->boolean('deposit')->default(true);
            $table->boolean('payout')->default(true);
            $table->timestamps();
        });
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->integer('client_id');
            $table->string('iso_code', 2);
            $table->boolean('is_available')->default(true);
            $table->timestamps();
        });
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('iso', 3);
            $table->integer('client_id');
            $table->boolean('is_available')->default(false);
            $table->integer('min_bet')->default(1);
            $table->integer('max_win')->default(100000000);
            $table->timestamps();

            $table->unique(['client_id', 'iso']);
        });
    }
}
