<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSmarticoBonusIdColumnToMassOnetimeBonusInfoTable extends Migration
{
    public function up(): void
    {
        Schema::table('mass_onetime_bonus_info', function (Blueprint $table) {
            $table->string('smartico_bonus_id')->nullable()->after('bonus_id')->index();
        });
    }

    public function down(): void
    {
        Schema::table('mass_onetime_bonus_info', function (Blueprint $table) {
            $table->dropColumn('smartico_bonus_id');
        });
    }
}
