<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddUniqueConstraintOnCountryIdSlotProviderId extends Migration
{
    public function up(): void
    {
        // Check if the index already exists before adding it
        $indexExists = DB::connection('mysql-app')->select(
            "SHOW INDEX FROM `country_slots_providers` WHERE Key_name = 'unique_country_slot'"
        );

        if (empty($indexExists)) {
            DB::connection('mysql-app')->statement('
                ALTER TABLE `country_slots_providers` ADD CONSTRAINT `unique_country_slot` UNIQUE (`country_id`, `slot_provider_id`);
            ');
        }
    }

    public function down(): void
    {
        // Specify the "mysql-app" connection and rollback the migration if necessary
        DB::connection('mysql-app')->statement('
            ALTER TABLE `country_slots_providers` DROP INDEX `unique_country_slot`;
        ');
    }
}
