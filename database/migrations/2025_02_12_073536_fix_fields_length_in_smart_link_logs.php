<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class FixFieldsLengthInSmartLinkLogs extends Migration
{
    public function up(): void
    {
        Schema::table('smart_link_logs', function (Blueprint $table) {
            $table->string('name', 128)->change();
            $table->string('old_value', 2000)->change();
            $table->string('new_value', 2000)->change();
        });
    }

    public function down(): void
    {
        Schema::table('smart_link_logs', function (Blueprint $table) {
            $table->string('name', 100)->change();
            $table->string('old_value', 255)->change();
            $table->string('new_value', 255)->change();
        });
    }
}
