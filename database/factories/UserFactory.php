<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Ramsey\Uuid\Uuid;

class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'client_id' => $this->faker->numberBetween(1, 1000),
            'role_id' => $this->faker->numberBetween(1, 5),
            'login' => $this->faker->userName,
            'email' => $this->faker->unique()->safeEmail,
            'password' => Hash::make('password'),
            'last_seen_ip' => $this->faker->ipv4,
        ];
    }

    /**
     * Indicate that the user is an admin.
     *
     * @return \Database\Factories\UserFactory
     */
    public function admin()
    {
        return $this->state(function (array $attributes) {
            return [
                'role_id' => 1,
            ];
        });
    }

    public function withRole(int $roleId)
    {
        return $this->state(function (array $attributes) use ($roleId) {
            return [
                'role_id' => $roleId,
            ];
        });
    }
}
