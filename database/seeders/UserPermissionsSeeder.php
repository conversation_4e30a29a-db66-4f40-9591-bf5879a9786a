<?php
namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use App\Models\RolePermission;
use Illuminate\Database\Seeder;
use WhiteLabelAdmin\Entities\PermissionList;

final class UserPermissionsSeeder extends Seeder
{
    public function run()
    {
        $permissions = Permission::where('name', 'LIKE', '%-col-%')->get();
        $roles = Role::get();
        foreach ($roles as $role) {
            foreach ($permissions as $permission) {
                $rolePermission = new RolePermission();
                $rolePermission->role_id = $role->id;
                $rolePermission->permission_id = $permission->id;
                $rolePermission->save();
            }
        }
    }
}
