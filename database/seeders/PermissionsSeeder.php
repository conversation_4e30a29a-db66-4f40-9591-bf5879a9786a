<?php
namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;
use WhiteLabelAdmin\Entities\PermissionList;

final class PermissionsSeeder extends Seeder
{
    public function run()
    {
        $r = new \ReflectionClass(PermissionList::class);
        (new Permission)->newQuery()->upsert(array_map(fn(string $permission) => [
            'name' => $permission,
        ], $r->getConstants()), ['name'], []);
    }
}
