{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "424784854f4ac95be1d98c4342bd47fa", "packages": [{"name": "asm89/stack-cors", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "50f57105bad3d97a43ec4a485eb57daf347eafea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/50f57105bad3d97a43ec4a485eb57daf347eafea", "reference": "50f57105bad3d97a43ec4a485eb57daf347eafea", "shasum": ""}, "require": {"php": "^7.3|^8.0", "symfony/http-foundation": "^5.3|^6|^7", "symfony/http-kernel": "^5.3|^6|^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "support": {"issues": "https://github.com/asm89/stack-cors/issues", "source": "https://github.com/asm89/stack-cors/tree/v2.2.0"}, "time": "2023-11-14T13:51:46+00:00"}, {"name": "brick/math", "version": "0.9.3", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "ca57d18f028f84f777b2168cd1911b0dee2343ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/ca57d18f028f84f777b2168cd1911b0dee2343ae", "reference": "ca57d18f028f84f777b2168cd1911b0dee2343ae", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.0", "vimeo/psalm": "4.9.2"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.9.3"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/brick/math", "type": "tidelift"}], "time": "2021-08-15T20:50:18+00:00"}, {"name": "brick/phonenumber", "version": "0.4.1", "source": {"type": "git", "url": "https://github.com/brick/phonenumber.git", "reference": "31886072e6ebfe44f027c63dd29d53913fac802b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/phonenumber/zipball/31886072e6ebfe44f027c63dd29d53913fac802b", "reference": "31886072e6ebfe44f027c63dd29d53913fac802b", "shasum": ""}, "require": {"ext-json": "*", "giggsey/libphonenumber-for-php": "^7.0 || ^8.0", "php": "^7.1 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\PhoneNumber\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Phone number library", "keywords": ["brick", "phone", "phone number", "phonenumber"], "support": {"issues": "https://github.com/brick/phonenumber/issues", "source": "https://github.com/brick/phonenumber/tree/0.4.1"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-02-22T21:28:22+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"doctrine/dbal": "<3.7.0 || >=4.0.0"}, "require-dev": {"doctrine/dbal": "^3.7.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/2.1.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2023-12-11T17:09:12+00:00"}, {"name": "composer/ca-bundle", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "063d9aa8696582f5a41dffbbaf3c81024f0a604a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/063d9aa8696582f5a41dffbbaf3c81024f0a604a", "reference": "063d9aa8696582f5a41dffbbaf3c81024f0a604a", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-07-08T15:28:20+00:00"}, {"name": "defuse/php-encryption", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/defuse/php-encryption.git", "reference": "f53396c2d34225064647a05ca76c1da9d99e5828"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/f53396c2d34225064647a05ca76c1da9d99e5828", "reference": "f53396c2d34225064647a05ca76c1da9d99e5828", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5|^6|^7|^8|^9|^10", "yoast/phpunit-polyfills": "^2.0.0"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "support": {"issues": "https://github.com/defuse/php-encryption/issues", "source": "https://github.com/defuse/php-encryption/tree/v2.4.0"}, "time": "2023-06-19T06:10:36+00:00"}, {"name": "deversin/personal-responsecache", "version": "1.0.1", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/white-label/personal-responsecache.git", "reference": "85a18607607d6a6977748984a6a627d2726e423c"}, "require": {"illuminate/cache": "^7.13|^8.0", "illuminate/console": "^7.13|^8.0", "illuminate/container": "^7.13|^8.0", "illuminate/http": "^7.13|^8.0", "illuminate/support": "^7.13|^8.0", "nesbot/carbon": "^2.35", "php": "^7.4|^8.0", "spatie/laravel-package-tools": "^1.1"}, "require-dev": {"laravel/framework": "^7.13|^8.0", "mockery/mockery": "^1.4", "orchestra/testbench": "^5.2|^6.0", "phpunit/phpunit": "^9.1.5"}, "type": "library", "extra": {"laravel": {"providers": ["Deversin\\PersonalResponseCache\\ResponseCacheServiceProvider"], "aliases": {"PersonalResponseCache": "Deversin\\PersonalResponseCache\\Facades\\ResponseCache"}}}, "autoload": {"psr-4": {"Deversin\\PersonalResponseCache\\": "src"}}, "autoload-dev": {"psr-4": {"Deversin\\PersonalResponseCache\\Test\\": "tests"}}, "scripts": {"psalm": ["vendor/bin/psalm"], "test": ["vendor/bin/phpunit"]}, "authors": [{"name": "4rabet.com", "homepage": "https://4rabet.com", "role": "Developer"}], "description": "Speed up a Laravel application by caching the entire response", "time": "2024-01-03T21:43:30+00:00"}, {"name": "deversin/signature", "version": "1.2.2", "source": {"type": "git", "url": "https://token:<EMAIL>/white-label/wl-crypto-signature.git", "reference": "3e6806571c84db865bed5a098124d00b39284094"}, "require": {"ext-json": "*", "ext-openssl": "*", "illuminate/support": "^8.0|^9.0|^10.0", "php": "^7.4|^8.0"}, "require-dev": {"orchestra/testbench": "^6.0|^7.0", "phpunit/phpunit": "^8.0|^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["Deversin\\Signature\\SignatureServiceProvider"], "aliases": {"Signature": "Deversin\\Signature\\SignatureFacade"}}}, "autoload": {"psr-4": {"Deversin\\Signature\\": "src"}}, "autoload-dev": {"psr-4": {"Deversin\\Signature\\Tests\\": "tests"}}, "scripts": {"test": ["vendor/bin/phpunit"], "test-coverage": ["vendor/bin/phpunit --coverage-html coverage"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Signature for requests and responses", "homepage": "https://gitlab.deversin.com/einstein/signature", "keywords": ["deversin", "signature"], "time": "2025-06-26T09:41:24+00:00"}, {"name": "dflydev/dot-access-data", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/a23a2bf4f31d3518f3ecb38660c95715dfead60f", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.3"}, "time": "2024-07-08T12:26:09+00:00"}, {"name": "doctrine/annotations", "version": "1.14.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.3"}, "time": "2023-02-01T09:20:38+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/dbal", "version": "3.8.6", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "b7411825cf7efb7e51f9791dea19d86e43b399a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/b7411825cf7efb7e51f9791dea19d86e43b399a1", "reference": "b7411825cf7efb7e51f9791dea19d86e43b399a1", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.1", "phpstan/phpstan": "1.11.5", "phpstan/phpstan-strict-rules": "^1.6", "phpunit/phpunit": "9.6.19", "psalm/plugin-phpunit": "0.18.4", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.10.1", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/console": "^4.4|^5.4|^6.0|^7.0", "vimeo/psalm": "4.30.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.8.6"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2024-06-19T10:38:17+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-webmozart-assert": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.3"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2023-08-10T19:36:49+00:00"}, {"name": "dusterio/lumen-passport", "version": "0.3.8", "source": {"type": "git", "url": "https://github.com/dusterio/lumen-passport.git", "reference": "e4b81e968125b07083f07619bc7c739c2d58654f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dusterio/lumen-passport/zipball/e4b81e968125b07083f07619bc7c739c2d58654f", "reference": "e4b81e968125b07083f07619bc7c739c2d58654f", "shasum": ""}, "require": {"illuminate/database": "~5.3|~5.4|~5.5|~5.6|^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/support": "~5.3|~5.4|~5.5|~5.6|^6.0|^7.0|^8.0|^9.0|^10.0", "laminas/laminas-diactoros": "^2.4", "laravel/passport": ">=0.2.2", "php": ">=7.1"}, "require-dev": {"fakerphp/faker": "^1.19|^1.9.1", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Dusterio\\LumenPassport\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.mysenko.com"}], "description": "Making Laravel Passport work with <PERSON><PERSON>", "homepage": "https://github.com/dusterio/lumen-passport", "keywords": ["laravel passport", "lumen", "php"], "support": {"issues": "https://github.com/dusterio/lumen-passport/issues", "source": "https://github.com/dusterio/lumen-passport/tree/0.3.8"}, "time": "2023-08-10T21:53:30+00:00"}, {"name": "egulias/email-validator", "version": "2.1.25", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.25"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2020-12-29T14:50:06+00:00"}, {"name": "elastic/transport", "version": "v8.8.0", "source": {"type": "git", "url": "https://github.com/elastic/elastic-transport-php.git", "reference": "cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elastic-transport-php/zipball/cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b", "reference": "cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "php": "^7.4 || ^8.0", "php-http/discovery": "^1.14", "php-http/httplug": "^2.3", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"nyholm/psr7": "^1.5", "php-http/mock-client": "^1.5", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Elastic\\Transport\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "HTTP transport PHP library for Elastic products", "keywords": ["PSR_17", "elastic", "http", "psr-18", "psr-7", "transport"], "support": {"issues": "https://github.com/elastic/elastic-transport-php/issues", "source": "https://github.com/elastic/elastic-transport-php/tree/v8.8.0"}, "time": "2023-11-08T10:51:51+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v8.8.0", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "5c4d67870b92fedc06fcceec5be8c3be8563fe3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/5c4d67870b92fedc06fcceec5be8c3be8563fe3d", "reference": "5c4d67870b92fedc06fcceec5be8c3be8563fe3d", "shasum": ""}, "require": {"elastic/transport": "^8.7", "guzzlehttp/guzzle": "^7.0", "php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-message": "^1.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.5", "nyholm/psr7": "^1.5", "php-http/message-factory": "^1.1", "php-http/mock-client": "^1.5", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9.5", "symfony/finder": "~4.0", "symfony/http-client": "^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Elastic\\Elasticsearch\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elastic", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v8.8.0"}, "time": "2023-05-23T08:59:13+00:00"}, {"name": "enqueue/dsn", "version": "0.10.19", "source": {"type": "git", "url": "https://github.com/php-enqueue/dsn.git", "reference": "f4991fe46dd01477deb566727170341b255e8479"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/dsn/zipball/f4991fe46dd01477deb566727170341b255e8479", "reference": "f4991fe46dd01477deb566727170341b255e8479", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\Dsn\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Parse DSN", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["dsn", "parse"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2023-03-23T09:50:55+00:00"}, {"name": "enqueue/enqueue", "version": "0.10.20", "source": {"type": "git", "url": "https://github.com/php-enqueue/enqueue.git", "reference": "4b3c6e98215cbc67c79fe0bfb6af55ccaac0e8fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/enqueue/zipball/4b3c6e98215cbc67c79fe0bfb6af55ccaac0e8fe", "reference": "4b3c6e98215cbc67c79fe0bfb6af55ccaac0e8fe", "shasum": ""}, "require": {"enqueue/dsn": "^0.10", "enqueue/null": "^0.10", "php": "^7.4|^8.0", "psr/container": "^1.1 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "queue-interop/amqp-interop": "^0.8.2", "queue-interop/queue-interop": "^0.8", "ramsey/uuid": "^3.5|^4"}, "require-dev": {"empi89/php-amqp-stubs": "*@dev", "enqueue/amqp-bunny": "0.10.x-dev", "enqueue/amqp-ext": "0.10.x-dev", "enqueue/amqp-lib": "0.10.x-dev", "enqueue/dbal": "0.10.x-dev", "enqueue/dsn": "0.10.x-dev", "enqueue/fs": "0.10.x-dev", "enqueue/gearman": "0.10.x-dev", "enqueue/gps": "0.10.x-dev", "enqueue/mongodb": "0.10.x-dev", "enqueue/pheanstalk": "0.10.x-dev", "enqueue/rdkafka": "0.10.x-dev", "enqueue/redis": "0.10.x-dev", "enqueue/simple-client": "0.10.x-dev", "enqueue/sqs": "0.10.x-dev", "enqueue/stomp": "0.10.x-dev", "enqueue/test": "0.10.x-dev", "phpunit/phpunit": "^9.5", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.41|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0"}, "suggest": {"enqueue/amqp-ext": "AMQP transport (based on php extension)", "enqueue/dbal": "Doctrine DBAL transport", "enqueue/fs": "Filesystem transport", "enqueue/redis": "Redis transport", "enqueue/sqs": "Amazon AWS SQS transport", "enqueue/stomp": "STOMP transport", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0 If you want to use cli commands", "symfony/dependency-injection": "^5.4|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Message Queue Library", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["AMQP", "messaging", "queue", "rabbitmq"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2023-10-15T09:28:46+00:00"}, {"name": "enqueue/fs", "version": "0.10.19", "source": {"type": "git", "url": "https://github.com/php-enqueue/fs.git", "reference": "3784404541b82fb5d915da90927411f1e5a8465d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/fs/zipball/3784404541b82fb5d915da90927411f1e5a8465d", "reference": "3784404541b82fb5d915da90927411f1e5a8465d", "shasum": ""}, "require": {"enqueue/dsn": "^0.10", "makasim/temp-file": "^0.2@stable", "php": "^7.4|^8.0", "queue-interop/queue-interop": "^0.8", "symfony/filesystem": "^5.4|^6.0"}, "require-dev": {"enqueue/null": "0.10.x-dev", "enqueue/test": "0.10.x-dev", "phpunit/phpunit": "^9.5", "queue-interop/queue-spec": "^0.6.2", "symfony/dependency-injection": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\Fs\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Enqueue Filesystem based transport", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["filesystem", "local", "messaging", "queue"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2023-03-23T09:50:55+00:00"}, {"name": "enqueue/laravel-queue", "version": "0.10.18", "source": {"type": "git", "url": "https://github.com/php-enqueue/laravel-queue.git", "reference": "8360f59b2b8bf760866433c3f41dd8af54fbaa16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/laravel-queue/zipball/8360f59b2b8bf760866433c3f41dd8af54fbaa16", "reference": "8360f59b2b8bf760866433c3f41dd8af54fbaa16", "shasum": ""}, "require": {"enqueue/dsn": "^0.10", "enqueue/enqueue": "^0.10", "illuminate/queue": "^6.0|^7.0|^8.0|^9.0", "php": ">=7.1", "queue-interop/amqp-interop": "^0.8", "queue-interop/queue-interop": "^0.7|^0.8"}, "require-dev": {"enqueue/enqueue": "^0.10", "enqueue/null": "^0.10@dev", "enqueue/simple-client": "^0.10@dev", "enqueue/test": "^0.10@dev", "phpunit/phpunit": "~5.5"}, "suggest": {"enqueue/simple-client": "If you want to use enqueue client and cli commands"}, "type": "library", "extra": {"laravel": {"providers": ["Enqueue\\LaravelQueue\\EnqueueServiceProvider"]}, "branch-alias": {"dev-master": "0.9.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\LaravelQueue\\": "src/"}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Laravel Queue Extension. It uses Enqueue transports", "keywords": ["laravel", "messaging", "queue"], "support": {"source": "https://github.com/php-enqueue/laravel-queue/tree/0.10.18"}, "time": "2023-01-07T13:44:35+00:00"}, {"name": "enqueue/null", "version": "0.10.19", "source": {"type": "git", "url": "https://github.com/php-enqueue/null.git", "reference": "c7174da80c59b04391c5194cebe46b6f85d79fb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/null/zipball/c7174da80c59b04391c5194cebe46b6f85d79fb4", "reference": "c7174da80c59b04391c5194cebe46b6f85d79fb4", "shasum": ""}, "require": {"php": "^7.4|^8.0", "queue-interop/queue-interop": "^0.8"}, "require-dev": {"enqueue/test": "0.10.x-dev", "phpunit/phpunit": "^9.5", "queue-interop/queue-spec": "^0.6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\Null\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Enqueue Null transport", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["messaging", "queue", "testing"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2023-03-23T09:50:55+00:00"}, {"name": "enqueue/rdkafka", "version": "0.10.20", "source": {"type": "git", "url": "https://github.com/php-enqueue/rdkafka.git", "reference": "d1dfe0422a15f25ef4dfda5eff5cf29633079949"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/rdkafka/zipball/d1dfe0422a15f25ef4dfda5eff5cf29633079949", "reference": "d1dfe0422a15f25ef4dfda5eff5cf29633079949", "shasum": ""}, "require": {"ext-rdkafka": "^4.0|^5.0|^6.0", "php": "^7.4|^8.0", "queue-interop/queue-interop": "^0.8.1"}, "require-dev": {"enqueue/null": "0.10.x-dev", "enqueue/test": "0.10.x-dev", "kwn/php-rdkafka-stubs": "^2.0.3", "phpunit/phpunit": "^9.5", "queue-interop/queue-spec": "^0.6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\RdKafka\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Message Queue Kafka Transport", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["kafka", "messaging", "queue"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2023-08-08T20:59:25+00:00"}, {"name": "enqueue/simple-client", "version": "0.10.19", "source": {"type": "git", "url": "https://github.com/php-enqueue/simple-client.git", "reference": "f8a5e00d1648559748bfa79485168c10acb075be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-enqueue/simple-client/zipball/f8a5e00d1648559748bfa79485168c10acb075be", "reference": "f8a5e00d1648559748bfa79485168c10acb075be", "shasum": ""}, "require": {"enqueue/enqueue": "^0.10", "php": "^7.4|^8.0", "queue-interop/amqp-interop": "^0.8.2", "queue-interop/queue-interop": "^0.8", "symfony/config": "^5.4|^6.0"}, "require-dev": {"enqueue/amqp-ext": "0.10.x-dev", "enqueue/fs": "0.10.x-dev", "enqueue/null": "0.10.x-dev", "enqueue/test": "0.10.x-dev", "phpunit/phpunit": "^9.5", "symfony/yaml": "^5.4|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Enqueue\\SimpleClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Message Queue Simple Client", "homepage": "https://enqueue.forma-pro.com/", "keywords": ["AMQP", "messaging", "queue", "rabbitmq"], "support": {"docs": "https://github.com/php-enqueue/enqueue-dev/blob/master/docs/index.md", "email": "<EMAIL>", "forum": "https://gitter.im/php-enqueue/Lobby", "issues": "https://github.com/php-enqueue/enqueue-dev/issues", "source": "https://github.com/php-enqueue/enqueue-dev"}, "time": "2023-03-23T09:50:55+00:00"}, {"name": "firebase/php-jwt", "version": "v6.10.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "a49db6f0a5033aef5143295342f1c95521b075ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/a49db6f0a5033aef5143295342f1c95521b075ff", "reference": "a49db6f0a5033aef5143295342f1c95521b075ff", "shasum": ""}, "require": {"php": "^7.4||^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.5||^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^1.0||^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.10.0"}, "time": "2023-12-01T16:26:39+00:00"}, {"name": "fruitcake/laravel-cors", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/fruitcake/laravel-cors.git", "reference": "783a74f5e3431d7b9805be8afb60fd0a8f743534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/laravel-cors/zipball/783a74f5e3431d7b9805be8afb60fd0a8f743534", "reference": "783a74f5e3431d7b9805be8afb60fd0a8f743534", "shasum": ""}, "require": {"asm89/stack-cors": "^2.0.1", "illuminate/contracts": "^6|^7|^8|^9", "illuminate/support": "^6|^7|^8|^9", "php": ">=7.2"}, "require-dev": {"laravel/framework": "^6|^7.24|^8", "orchestra/testbench-dusk": "^4|^5|^6|^7", "phpunit/phpunit": "^6|^7|^8|^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}, "laravel": {"providers": ["Fruitcake\\Cors\\CorsServiceProvider"]}}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Laravel application", "keywords": ["api", "cors", "crossdomain", "laravel"], "support": {"issues": "https://github.com/fruitcake/laravel-cors/issues", "source": "https://github.com/fruitcake/laravel-cors/tree/v2.2.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "abandoned": true, "time": "2022-02-23T14:25:13+00:00"}, {"name": "geoip2/geoip2", "version": "v2.13.0", "source": {"type": "git", "url": "https://github.com/maxmind/GeoIP2-php.git", "reference": "6a41d8fbd6b90052bc34dff3b4252d0f88067b23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/GeoIP2-php/zipball/6a41d8fbd6b90052bc34dff3b4252d0f88067b23", "reference": "6a41d8fbd6b90052bc34dff3b4252d0f88067b23", "shasum": ""}, "require": {"ext-json": "*", "maxmind-db/reader": "~1.8", "maxmind/web-service-common": "~0.8", "php": ">=7.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "phpstan/phpstan": "*", "phpunit/phpunit": "^8.0 || ^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"GeoIp2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind GeoIP2 PHP API", "homepage": "https://github.com/maxmind/GeoIP2-php", "keywords": ["IP", "geoip", "geoip2", "geolocation", "maxmind"], "support": {"issues": "https://github.com/maxmind/GeoIP2-php/issues", "source": "https://github.com/maxmind/GeoIP2-php/tree/v2.13.0"}, "time": "2022-08-05T20:32:58+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.42", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "b7ee848bbd1958ff7464522d5c6e3688cca2a125"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/b7ee848bbd1958ff7464522d5c6e3688cca2a125", "reference": "b7ee848bbd1958ff7464522d5c6e3688cca2a125", "shasum": ""}, "require": {"giggsey/locale": "^1.7|^2.0", "php": ">=5.3.2", "symfony/polyfill-mbstring": "^1.17"}, "replace": {"giggsey/libphonenumber-for-php-lite": "self.version"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^1.0|^2.0", "symfony/console": "^2.8|^3.0|^v4.4|^v5.2", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2024-07-29T07:19:22+00:00"}, {"name": "giggsey/locale", "version": "2.6", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "37874fa473131247c348059fb7b8985efc18b5ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/37874fa473131247c348059fb7b8985efc18b5ea", "reference": "37874fa473131247c348059fb7b8985efc18b5ea", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"ext-json": "*", "pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^8.5|^9.5", "symfony/console": "^5.0|^6.0", "symfony/filesystem": "^5.0|^6.0", "symfony/finder": "^5.0|^6.0", "symfony/process": "^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.6"}, "time": "2024-04-18T19:31:19+00:00"}, {"name": "google/apiclient", "version": "v2.16.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client.git", "reference": "017400f609c1fb71ab5ad824c50eabd4c3eaf779"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client/zipball/017400f609c1fb71ab5ad824c50eabd4c3eaf779", "reference": "017400f609c1fb71ab5ad824c50eabd4c3eaf779", "shasum": ""}, "require": {"firebase/php-jwt": "~6.0", "google/apiclient-services": "~0.350", "google/auth": "^1.37", "guzzlehttp/guzzle": "^6.5.8||^7.4.5", "guzzlehttp/psr7": "^1.9.1||^2.2.1", "monolog/monolog": "^2.9||^3.0", "php": "^7.4|^8.0", "phpseclib/phpseclib": "^3.0.36"}, "require-dev": {"cache/filesystem-adapter": "^1.1", "composer/composer": "^1.10.23", "phpcompatibility/php-compatibility": "^9.2", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.8", "symfony/css-selector": "~2.1", "symfony/dom-crawler": "~2.1"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"files": ["src/aliases.php"], "psr-4": {"Google\\": "src/"}, "classmap": ["src/aliases.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client/issues", "source": "https://github.com/googleapis/google-api-php-client/tree/v2.16.0"}, "time": "2024-04-24T00:59:47+00:00"}, {"name": "google/apiclient-services", "version": "v0.355.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client-services.git", "reference": "235e6a45ecafd77accc102b5ab6d529aab54da23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client-services/zipball/235e6a45ecafd77accc102b5ab6d529aab54da23", "reference": "235e6a45ecafd77accc102b5ab6d529aab54da23", "shasum": ""}, "require": {"php": "^7.4||^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7||^8.5.13"}, "type": "library", "autoload": {"files": ["autoload.php"], "psr-4": {"Google\\Service\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client-services/issues", "source": "https://github.com/googleapis/google-api-php-client-services/tree/v0.355.0"}, "time": "2024-05-11T01:02:11+00:00"}, {"name": "google/auth", "version": "v1.37.1", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "1a7de77b72e6ac60dccf0e6478c4c1005bb0ff46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/1a7de77b72e6ac60dccf0e6478c4c1005bb0ff46", "reference": "1a7de77b72e6ac60dccf0e6478c4c1005bb0ff46", "shasum": ""}, "require": {"firebase/php-jwt": "^6.0", "guzzlehttp/guzzle": "^6.5.8||^7.4.5", "guzzlehttp/psr7": "^2.4.5", "php": "^7.4||^8.0", "psr/cache": "^1.0||^2.0||^3.0", "psr/http-message": "^1.1||^2.0"}, "require-dev": {"guzzlehttp/promises": "^2.0", "kelvinmo/simplejwt": "0.7.1", "phpseclib/phpseclib": "^3.0", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.0.0", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/main/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.37.1"}, "time": "2024-04-03T18:41:12+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2024-07-20T21:45:45+00:00"}, {"name": "graylog2/gelf-php", "version": "1.7.1", "source": {"type": "git", "url": "https://github.com/bzikarsky/gelf-php.git", "reference": "8dceab86227c184725479cc36ab5cae4da940f6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bzi<PERSON>sky/gelf-php/zipball/8dceab86227c184725479cc36ab5cae4da940f6e", "reference": "8dceab86227c184725479cc36ab5cae4da940f6e", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2", "php": ">=5.6", "psr/log": "^1.0|^2.0"}, "provide": {"psr/log-implementation": "~1.0"}, "require-dev": {"phpunit/phpunit": "^5.7|^6.5|^7.5", "squizlabs/php_codesniffer": "^3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Gelf\\": "src/Gelf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "gelf-php contributors", "homepage": "https://github.com/bzi<PERSON>sky/gelf-php/contributors"}], "description": "A php implementation to send log-messages to a GELF compatible backend like Graylog2.", "support": {"issues": "https://github.com/bzikarsky/gelf-php/issues", "source": "https://github.com/bzikarsky/gelf-php/tree/1.7.1"}, "time": "2021-08-20T09:39:08+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2024-07-24T11:22:20+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8", "reference": "6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2024-07-18T10:29:17+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2024-07-18T11:15:46+00:00"}, {"name": "hedii/laravel-gelf-logger", "version": "6.2.0", "source": {"type": "git", "url": "https://github.com/hedii/laravel-gelf-logger.git", "reference": "d4d4f44ce1155ff78ea90af0bc3f6f258785c90b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hedii/laravel-gelf-logger/zipball/d4d4f44ce1155ff78ea90af0bc3f6f258785c90b", "reference": "d4d4f44ce1155ff78ea90af0bc3f6f258785c90b", "shasum": ""}, "require": {"graylog2/gelf-php": "^1.6", "illuminate/log": "^8.12", "php": "^7.3|^8.0"}, "require-dev": {"orchestra/testbench": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Hedii\\LaravelGelfLogger\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "hedii", "homepage": "https://hedichaibi.com"}], "description": "A Laravel package to send logs to a gelf compatible backend like graylog", "keywords": ["gelf", "graylog", "graylog2", "hedii", "laravel", "laravel-gelf", "log", "logging", "php", "udp"], "support": {"issues": "https://github.com/hedii/laravel-gelf-logger/issues", "source": "https://github.com/hedii/laravel-gelf-logger"}, "time": "2022-01-16T10:51:20+00:00"}, {"name": "intouch/newrelic", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/In-Touch/newrelic.git", "reference": "20db6cb25a9eb88e504e16829077c56828c176cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/In-Touch/newrelic/zipball/20db6cb25a9eb88e504e16829077c56828c176cc", "reference": "20db6cb25a9eb88e504e16829077c56828c176cc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"Intouch\\Newrelic": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache 2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Namespaced Wrapper for NewRelic PHP Agent", "homepage": "http://github.com/In-Touch/newrelic/", "keywords": ["new relic", "newrelic"], "support": {"issues": "https://github.com/In-Touch/newrelic/issues", "source": "https://github.com/In-Touch/newrelic/tree/master"}, "time": "2014-08-18T12:08:45+00:00"}, {"name": "jarvis/integrator-core", "version": "2.0.50", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/microservices/integrator-core.git", "reference": "dd104cda7daf80963392c797be7efe0de83c62ae"}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^7.0", "hedii/laravel-gelf-logger": "^6.0|^7.0", "illuminate/support": "^8.6", "league/flysystem": "^1.0", "php": "^7.4|^8.0", "phpoption/phpoption": "^1.7", "vlucas/phpdotenv": "^5.2"}, "require-dev": {"fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.3", "roave/security-advisories": "dev-master"}, "type": "library", "autoload": {"psr-4": {"IntegratorCore\\": "src/", "IntegratorCore\\Tests\\": "tests/"}}, "autoload-dev": {"classmap": ["tests/"]}, "authors": [{"name": "jarvis", "email": "<EMAIL>"}], "description": "Core of integrator microservice", "time": "2025-03-07T14:03:46+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.119", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "275002e22b0333c15a7c6792fdae5d5deefc9ef0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/275002e22b0333c15a7c6792fdae5d5deefc9ef0", "reference": "275002e22b0333c15a7c6792fdae5d5deefc9ef0", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.2.119"}, "time": "2024-06-07T07:58:43+00:00"}, {"name": "jenssegers/agent", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/jenssegers/agent.git", "reference": "daa11c43729510b3700bc34d414664966b03bffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/agent/zipball/daa11c43729510b3700bc34d414664966b03bffe", "reference": "daa11c43729510b3700bc34d414664966b03bffe", "shasum": ""}, "require": {"jaybizzle/crawler-detect": "^1.2", "mobiledetect/mobiledetectlib": "^2.7.6", "php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.0|^6.0|^7.0"}, "suggest": {"illuminate/support": "Required for laravel service providers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Jenssegers\\Agent\\AgentServiceProvider"], "aliases": {"Agent": "Jenssegers\\Agent\\Facades\\Agent"}}}, "autoload": {"psr-4": {"Jenssegers\\Agent\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "Desktop/mobile user agent parser with support for <PERSON><PERSON>, based on Mobiledetect", "homepage": "https://github.com/jenssegers/agent", "keywords": ["Agent", "browser", "desktop", "laravel", "mobile", "platform", "user agent", "useragent"], "support": {"issues": "https://github.com/jenssegers/agent/issues", "source": "https://github.com/jenssegers/agent/tree/v2.6.4"}, "funding": [{"url": "https://github.com/jenssegers", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/jenssegers/agent", "type": "tidelift"}], "time": "2020-06-13T08:05:20+00:00"}, {"name": "laminas/laminas-diactoros", "version": "2.17.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-diactoros.git", "reference": "5b32597aa46b83c8b85bb1cf9a6ed4fe7dd980c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-diactoros/zipball/5b32597aa46b83c8b85bb1cf9a6ed4fe7dd980c5", "reference": "5b32597aa46b83c8b85bb1cf9a6ed4fe7dd980c5", "shasum": ""}, "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "conflict": {"zendframework/zend-diactoros": "*"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "ext-gd": "*", "ext-libxml": "*", "http-interop/http-factory-tests": "^0.9.0", "laminas/laminas-coding-standard": "^2.4.0", "php-http/psr7-integration-tests": "^1.1.1", "phpunit/phpunit": "^9.5.23", "psalm/plugin-phpunit": "^0.17.0", "vimeo/psalm": "^4.24.0"}, "type": "library", "extra": {"laminas": {"config-provider": "Laminas\\Diactoros\\ConfigProvider", "module": "Laminas\\Diactoros"}}, "autoload": {"files": ["src/functions/create_uploaded_file.php", "src/functions/marshal_headers_from_sapi.php", "src/functions/marshal_method_from_sapi.php", "src/functions/marshal_protocol_version_from_sapi.php", "src/functions/marshal_uri_from_sapi.php", "src/functions/normalize_server.php", "src/functions/normalize_uploaded_files.php", "src/functions/parse_cookie_header.php", "src/functions/create_uploaded_file.legacy.php", "src/functions/marshal_headers_from_sapi.legacy.php", "src/functions/marshal_method_from_sapi.legacy.php", "src/functions/marshal_protocol_version_from_sapi.legacy.php", "src/functions/marshal_uri_from_sapi.legacy.php", "src/functions/normalize_server.legacy.php", "src/functions/normalize_uploaded_files.legacy.php", "src/functions/parse_cookie_header.legacy.php"], "psr-4": {"Laminas\\Diactoros\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR HTTP Message implementations", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "psr", "psr-17", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-diactoros/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-diactoros/issues", "rss": "https://github.com/laminas/laminas-diactoros/releases.atom", "source": "https://github.com/laminas/laminas-diactoros"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2022-08-30T17:01:46+00:00"}, {"name": "laravel/framework", "version": "v8.83.27", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "e1afe088b4ca613fb96dc57e6d8dbcb8cc2c6b49"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/e1afe088b4ca613fb96dc57e6d8dbcb8cc2c6b49", "reference": "e1afe088b4ca613fb96dc57e6d8dbcb8cc2c6b49", "shasum": ""}, "require": {"doctrine/inflector": "^1.4|^2.0", "dragonmantank/cron-expression": "^3.0.2", "egulias/email-validator": "^2.1.10", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "laravel/serializable-closure": "^1.0", "league/commonmark": "^1.3|^2.0.2", "league/flysystem": "^1.1", "monolog/monolog": "^2.0", "nesbot/carbon": "^2.53.1", "opis/closure": "^3.6", "php": "^7.3|^8.0", "psr/container": "^1.0", "psr/log": "^1.0|^2.0", "psr/simple-cache": "^1.0", "ramsey/uuid": "^4.2.2", "swiftmailer/swiftmailer": "^6.3", "symfony/console": "^5.4", "symfony/error-handler": "^5.4", "symfony/finder": "^5.4", "symfony/http-foundation": "^5.4", "symfony/http-kernel": "^5.4", "symfony/mime": "^5.4", "symfony/process": "^5.4", "symfony/routing": "^5.4", "symfony/var-dumper": "^5.4", "tijsverkoyen/css-to-inline-styles": "^2.2.2", "vlucas/phpdotenv": "^5.4.1", "voku/portable-ascii": "^1.6.1"}, "conflict": {"tightenco/collect": "<5.5.33"}, "provide": {"psr/container-implementation": "1.0", "psr/simple-cache-implementation": "1.0"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/collections": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/macroable": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/testing": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.198.1", "doctrine/dbal": "^2.13.3|^3.1.4", "filp/whoops": "^2.14.3", "guzzlehttp/guzzle": "^6.5.5|^7.0.1", "league/flysystem-cached-adapter": "^1.0", "mockery/mockery": "^1.4.4", "orchestra/testbench-core": "^6.27", "pda/pheanstalk": "^4.0", "phpunit/phpunit": "^8.5.19|^9.5.8", "predis/predis": "^1.1.9", "symfony/cache": "^5.4"}, "suggest": {"ably/ably-php": "Required to use the Ably broadcast driver (^1.0).", "aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage and SES mail driver (^3.198.1).", "brianium/paratest": "Required to run tests in parallel (^6.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.13.3|^3.1.4).", "ext-bcmath": "Required to use the multiple_of validation rule.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.14.3).", "guzzlehttp/guzzle": "Required to use the HTTP Client, Mailgun mail driver and the ping methods on schedules (^6.5.5|^7.0.1).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0).", "mockery/mockery": "Required to use mocking (^1.4.4).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "phpunit/phpunit": "Required to use assertions and run tests (^8.5.19|^9.5.8).", "predis/predis": "Required to use the predis connector (^1.1.9).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^4.0|^5.0|^6.0|^7.0).", "symfony/cache": "Required to PSR-6 cache bridge (^5.4).", "symfony/filesystem": "Required to enable support for relative symbolic links (^5.4).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0).", "wildbit/swiftmailer-postmark": "Required to use Postmark mail driver (^3.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"files": ["src/Illuminate/Collections/helpers.php", "src/Illuminate/Events/functions.php", "src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/", "Illuminate\\Support\\": ["src/Illuminate/Macroable/", "src/Illuminate/Collections/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2022-12-08T15:28:55+00:00"}, {"name": "laravel/lumen-framework", "version": "v8.3.4", "source": {"type": "git", "url": "https://github.com/laravel/lumen-framework.git", "reference": "733d1199d3344be337743f11df31b4048ec7fd1c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/lumen-framework/zipball/733d1199d3344be337743f11df31b4048ec7fd1c", "reference": "733d1199d3344be337743f11df31b4048ec7fd1c", "shasum": ""}, "require": {"dragonmantank/cron-expression": "^3.0.2", "illuminate/auth": "^8.65", "illuminate/broadcasting": "^8.65", "illuminate/bus": "^8.65", "illuminate/cache": "^8.65", "illuminate/collections": "^8.65", "illuminate/config": "^8.65", "illuminate/console": "^8.65", "illuminate/container": "^8.65", "illuminate/contracts": "^8.65", "illuminate/database": "^8.65", "illuminate/encryption": "^8.65", "illuminate/events": "^8.65", "illuminate/filesystem": "^8.65", "illuminate/hashing": "^8.65", "illuminate/http": "^8.65", "illuminate/log": "^8.65", "illuminate/macroable": "^8.65", "illuminate/pagination": "^8.65", "illuminate/pipeline": "^8.65", "illuminate/queue": "^8.65", "illuminate/support": "^8.65", "illuminate/testing": "^8.65", "illuminate/translation": "^8.65", "illuminate/validation": "^8.65", "illuminate/view": "^8.65", "nikic/fast-route": "^1.3", "php": "^7.3|^8.0", "symfony/console": "^5.4", "symfony/error-handler": "^5.4", "symfony/http-foundation": "^5.4", "symfony/http-kernel": "^5.4", "symfony/mime": "^5.4", "symfony/var-dumper": "^5.4", "vlucas/phpdotenv": "^5.2"}, "require-dev": {"mockery/mockery": "^1.4.4", "phpunit/phpunit": "^8.5.19|^9.5.8"}, "suggest": {"laravel/tinker": "Required to use the tinker console command (^2.0).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Laravel\\Lumen\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Lumen Framework.", "homepage": "https://lumen.laravel.com", "keywords": ["framework", "laravel", "lumen"], "support": {"issues": "https://github.com/laravel/lumen-framework/issues", "source": "https://github.com/laravel/lumen-framework"}, "time": "2021-12-22T10:11:35+00:00"}, {"name": "laravel/passport", "version": "v10.4.2", "source": {"type": "git", "url": "https://github.com/laravel/passport.git", "reference": "4bfdb9610575a0c84a6810701f4fd45fb8ab3888"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/passport/zipball/4bfdb9610575a0c84a6810701f4fd45fb8ab3888", "reference": "4bfdb9610575a0c84a6810701f4fd45fb8ab3888", "shasum": ""}, "require": {"ext-json": "*", "firebase/php-jwt": "^6.0", "illuminate/auth": "^8.37|^9.0", "illuminate/console": "^8.37|^9.0", "illuminate/container": "^8.37|^9.0", "illuminate/contracts": "^8.37|^9.0", "illuminate/cookie": "^8.37|^9.0", "illuminate/database": "^8.37|^9.0", "illuminate/encryption": "^8.37|^9.0", "illuminate/http": "^8.37|^9.0", "illuminate/support": "^8.37|^9.0", "lcobucci/jwt": "^3.4|^4.0", "league/oauth2-server": "^8.2", "nyholm/psr7": "^1.3", "php": "^7.3|^8.0", "phpseclib/phpseclib": "^2.0|^3.0", "symfony/psr-http-message-bridge": "^2.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^6.0|^7.0", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "10.x-dev"}, "laravel": {"providers": ["Laravel\\Passport\\PassportServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Passport\\": "src/", "Laravel\\Passport\\Database\\Factories\\": "database/factories/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel Passport provides OAuth2 server support to Laravel.", "keywords": ["laravel", "o<PERSON>h", "passport"], "support": {"issues": "https://github.com/laravel/passport/issues", "source": "https://github.com/laravel/passport"}, "time": "2023-02-21T07:47:20+00:00"}, {"name": "laravel/serializable-closure", "version": "v1.3.3", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "3dbf8a8e914634c48d389c1234552666b3d43754"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/3dbf8a8e914634c48d389c1234552666b3d43754", "reference": "3dbf8a8e914634c48d389c1234552666b3d43754", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"nesbot/carbon": "^2.61", "pestphp/pest": "^1.21.3", "phpstan/phpstan": "^1.8.2", "symfony/var-dumper": "^5.4.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "time": "2023-11-08T14:08:06+00:00"}, {"name": "lcobucci/clock", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/lcobucci/clock.git", "reference": "353d83fe2e6ae95745b16b3d911813df6a05bfb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/clock/zipball/353d83fe2e6ae95745b16b3d911813df6a05bfb3", "reference": "353d83fe2e6ae95745b16b3d911813df6a05bfb3", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"infection/infection": "^0.17", "lcobucci/coding-standard": "^6.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/php-code-coverage": "9.1.4", "phpunit/phpunit": "9.3.7"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\Clock\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Yet another clock abstraction", "support": {"issues": "https://github.com/lcobucci/clock/issues", "source": "https://github.com/lcobucci/clock/tree/2.0.x"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2020-08-27T18:56:02+00:00"}, {"name": "lcobucci/jwt", "version": "4.3.0", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "4d7de2fe0d51a96418c0d04004986e410e87f6b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/4d7de2fe0d51a96418c0d04004986e410e87f6b4", "reference": "4d7de2fe0d51a96418c0d04004986e410e87f6b4", "shasum": ""}, "require": {"ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-sodium": "*", "lcobucci/clock": "^2.0 || ^3.0", "php": "^7.4 || ^8.0"}, "require-dev": {"infection/infection": "^0.21", "lcobucci/coding-standard": "^6.0", "mikey179/vfsstream": "^1.6.7", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.4", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/php-invoker": "^3.1", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/4.3.0"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2023-01-02T13:28:00+00:00"}, {"name": "league/commonmark", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "ac815920de0eff6de947eac0a6a94e5ed0fb147c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/ac815920de0eff6de947eac0a6a94e5ed0fb147c", "reference": "ac815920de0eff6de947eac0a6a94e5ed0fb147c", "shasum": ""}, "require": {"ext-mbstring": "*", "league/config": "^1.1.1", "php": "^7.4 || ^8.0", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"cebe/markdown": "^1.0", "commonmark/cmark": "0.31.0", "commonmark/commonmark.js": "0.31.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0 || ^7.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 || ^7.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}, "suggest": {"symfony/yaml": "v2.3+ required if using the Front Matter extension"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.6-dev"}}, "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and GitHub-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "forum": "https://github.com/thephpleague/commonmark/discussions", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2024-07-24T12:52:09+00:00"}, {"name": "league/config", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/config.git", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/config/zipball/754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3.0.1", "nette/schema": "^1.2", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.2-dev"}}, "autoload": {"psr-4": {"League\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Define configuration arrays with strict schemas and access values with dot notation", "homepage": "https://config.thephpleague.com", "keywords": ["array", "config", "configuration", "dot", "dot-access", "nested", "schema"], "support": {"docs": "https://config.thephpleague.com/", "issues": "https://github.com/thephpleague/config/issues", "rss": "https://github.com/thephpleague/config/releases.atom", "source": "https://github.com/thephpleague/config"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}], "time": "2022-12-11T20:36:23+00:00"}, {"name": "league/event", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/event.git", "reference": "d2cc124cf9a3fab2bb4ff963307f60361ce4d119"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/event/zipball/d2cc124cf9a3fab2bb4ff963307f60361ce4d119", "reference": "d2cc124cf9a3fab2bb4ff963307f60361ce4d119", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "~1.0.1", "phpspec/phpspec": "^2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"League\\Event\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Event package", "keywords": ["emitter", "event", "listener"], "support": {"issues": "https://github.com/thephpleague/event/issues", "source": "https://github.com/thephpleague/event/tree/master"}, "time": "2018-11-26T11:52:41+00:00"}, {"name": "league/flysystem", "version": "1.1.10", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2022-10-04T09:16:37+00:00"}, {"name": "league/mime-type-detection", "version": "1.15.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.15.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-01-28T23:22:08+00:00"}, {"name": "league/oauth2-client", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "160d6274b03562ebeb55ed18399281d8118b76c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/160d6274b03562ebeb55ed18399281d8118b76c8", "reference": "160d6274b03562ebeb55ed18399281d8118b76c8", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "paragonie/random_compat": "^1 || ^2 || ^9.99", "php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.3.5", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpunit/phpunit": "^5.7 || ^6.0 || ^9.5", "squizlabs/php_codesniffer": "^2.3 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.0.x-dev"}}, "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "support": {"issues": "https://github.com/thephpleague/oauth2-client/issues", "source": "https://github.com/thephpleague/oauth2-client/tree/2.7.0"}, "time": "2023-04-16T18:19:15+00:00"}, {"name": "league/oauth2-server", "version": "8.4.2", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-server.git", "reference": "007dc5f6c0151a73b133fec36c9686cc956209d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-server/zipball/007dc5f6c0151a73b133fec36c9686cc956209d3", "reference": "007dc5f6c0151a73b133fec36c9686cc956209d3", "shasum": ""}, "require": {"defuse/php-encryption": "^2.2.1", "ext-json": "*", "ext-openssl": "*", "lcobucci/jwt": "^3.4.6 || ^4.0.4", "league/event": "^2.2", "league/uri": "^6.4", "php": "^7.2 || ^8.0", "psr/http-message": "^1.0.1"}, "replace": {"league/oauth2server": "*", "lncd/oauth2": "*"}, "require-dev": {"laminas/laminas-diactoros": "^2.4.1", "phpstan/phpstan": "^0.12.57", "phpstan/phpstan-phpunit": "^0.12.16", "phpunit/phpunit": "^8.5.13", "roave/security-advisories": "dev-master"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.noexceptions.io", "role": "Developer"}], "description": "A lightweight and powerful OAuth 2.0 authorization and resource server library with support for all the core specification grants. This library will allow you to secure your API with OAuth and allow your applications users to approve apps that want to access their data from your API.", "homepage": "https://oauth2.thephpleague.com/", "keywords": ["Authentication", "api", "auth", "authorisation", "authorization", "o<PERSON>h", "oauth 2", "oauth 2.0", "oauth2", "protect", "resource", "secure", "server"], "support": {"issues": "https://github.com/thephpleague/oauth2-server/issues", "source": "https://github.com/thephpleague/oauth2-server/tree/8.4.2"}, "funding": [{"url": "https://github.com/sephster", "type": "github"}], "time": "2023-08-02T22:54:39+00:00"}, {"name": "league/uri", "version": "6.7.2", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "d3b50812dd51f3fbf176344cc2981db03d10fe06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/d3b50812dd51f3fbf176344cc2981db03d10fe06", "reference": "d3b50812dd51f3fbf176344cc2981db03d10fe06", "shasum": ""}, "require": {"ext-json": "*", "league/uri-interfaces": "^2.3", "php": "^7.4 || ^8.0", "psr/http-message": "^1.0"}, "conflict": {"league/uri-schemes": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.3.2", "nyholm/psr7": "^1.5", "php-http/psr7-integration-tests": "^1.1", "phpstan/phpstan": "^1.2.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0.0", "phpstan/phpstan-strict-rules": "^1.1.0", "phpunit/phpunit": "^9.5.10", "psr/http-factory": "^1.0"}, "suggest": {"ext-fileinfo": "Needed to create Data URI from a filepath", "ext-intl": "Needed to improve host validation", "league/uri-components": "Needed to easily manipulate URI objects", "psr/http-factory": "Needed to use the URI factory"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "uri-template", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri/issues", "source": "https://github.com/thephpleague/uri/tree/6.7.2"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2022-09-13T19:50:42+00:00"}, {"name": "league/uri-components", "version": "2.4.2", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-components.git", "reference": "c93837294fe9021d518fd3ea4c5f3fbba8b8ddeb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-components/zipball/c93837294fe9021d518fd3ea4c5f3fbba8b8ddeb", "reference": "c93837294fe9021d518fd3ea4c5f3fbba8b8ddeb", "shasum": ""}, "require": {"ext-json": "*", "league/uri-interfaces": "^2.3", "php": "^7.4 || ^8.0", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v3.22.0", "guzzlehttp/psr7": "^2.2", "laminas/laminas-diactoros": "^2.11", "league/uri": "^6.0", "phpstan/phpstan": "^1.10.28", "phpstan/phpstan-deprecation-rules": "^1.1.4", "phpstan/phpstan-phpunit": "^1.3.13", "phpstan/phpstan-strict-rules": "^1.5.1", "phpunit/phpunit": "^9.6.10"}, "suggest": {"ext-fileinfo": "Needed to create Data URI from a filepath", "ext-gmp": "to improve handle IPV4 parsing", "ext-intl": "to handle IDN host", "jeremykendall/php-domain-parser": "Public Suffix and Top Level Domain parsing implemented in PHP", "league/uri": "to allow manipulating URI objects", "php-64bit": "to improve handle IPV4 parsing", "psr/http-message-implementation": "to allow manipulating PSR-7 Uri objects"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI components manipulation library", "homepage": "http://uri.thephpleague.com", "keywords": ["authority", "components", "fragment", "host", "path", "port", "query", "rfc3986", "scheme", "uri", "url", "userinfo"], "support": {"source": "https://github.com/thephpleague/uri-components/tree/2.4.2"}, "funding": [{"url": "https://github.com/nyamsprod", "type": "github"}], "time": "2023-08-13T19:53:57+00:00"}, {"name": "league/uri-interfaces", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "00e7e2943f76d8cb50c7dfdc2f6dee356e15e383"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/00e7e2943f76d8cb50c7dfdc2f6dee356e15e383", "reference": "00e7e2943f76d8cb50c7dfdc2f6dee356e15e383", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19", "phpstan/phpstan": "^0.12.90", "phpstan/phpstan-phpunit": "^0.12.19", "phpstan/phpstan-strict-rules": "^0.12.9", "phpunit/phpunit": "^8.5.15 || ^9.5"}, "suggest": {"ext-intl": "to use the IDNA feature", "symfony/intl": "to use the IDNA feature via Symfony Polyfill"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interface for URI representation", "homepage": "http://github.com/thephpleague/uri-interfaces", "keywords": ["rfc3986", "rfc3987", "uri", "url"], "support": {"issues": "https://github.com/thephpleague/uri-interfaces/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/2.3.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2021-06-28T04:27:21+00:00"}, {"name": "makasim/temp-file", "version": "0.2.0", "source": {"type": "git", "url": "https://github.com/makasim/TempFile.git", "reference": "e9cfbfc61a23044aeb2fe0a3db49aeec2f84ca27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/makasim/TempFile/zipball/e9cfbfc61a23044aeb2fe0a3db49aeec2f84ca27", "reference": "e9cfbfc61a23044aeb2fe0a3db49aeec2f84ca27", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "autoload": {"psr-0": {"Makasim": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Temp file object", "homepage": "https://github.com/makasim/TempFile", "keywords": ["file", "temp", "temp file"], "support": {"issues": "https://github.com/makasim/TempFile/issues", "source": "https://github.com/makasim/TempFile/tree/master"}, "time": "2013-07-12T06:09:04+00:00"}, {"name": "maxmind-db/reader", "version": "v1.11.1", "source": {"type": "git", "url": "https://github.com/maxmind/MaxMind-DB-Reader-php.git", "reference": "1e66f73ffcf25e17c7a910a1317e9720a95497c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/MaxMind-DB-Reader-php/zipball/1e66f73ffcf25e17c7a910a1317e9720a95497c7", "reference": "1e66f73ffcf25e17c7a910a1317e9720a95497c7", "shasum": ""}, "require": {"php": ">=7.2"}, "conflict": {"ext-maxminddb": "<1.11.1,>=2.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "php-coveralls/php-coveralls": "^2.1", "phpstan/phpstan": "*", "phpunit/phpcov": ">=6.0.0", "phpunit/phpunit": ">=8.0.0,<10.0.0", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-bcmath": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-gmp": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-maxminddb": "A C-based database decoder that provides significantly faster lookups"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Db\\": "src/MaxMind/Db"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind DB Reader API", "homepage": "https://github.com/maxmind/MaxMind-DB-Reader-php", "keywords": ["database", "geoip", "geoip2", "geolocation", "maxmind"], "support": {"issues": "https://github.com/maxmind/MaxMind-DB-Reader-php/issues", "source": "https://github.com/maxmind/MaxMind-DB-Reader-php/tree/v1.11.1"}, "time": "2023-12-02T00:09:23+00:00"}, {"name": "maxmind/web-service-common", "version": "v0.9.0", "source": {"type": "git", "url": "https://github.com/maxmind/web-service-common-php.git", "reference": "4dc5a3e8df38aea4ca3b1096cee3a038094e9b53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/web-service-common-php/zipball/4dc5a3e8df38aea4ca3b1096cee3a038094e9b53", "reference": "4dc5a3e8df38aea4ca3b1096cee3a038094e9b53", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0.3", "ext-curl": "*", "ext-json": "*", "php": ">=7.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "phpstan/phpstan": "*", "phpunit/phpunit": "^8.0 || ^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Exception\\": "src/Exception", "MaxMind\\WebService\\": "src/WebService"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Internal MaxMind Web Service API", "homepage": "https://github.com/maxmind/web-service-common-php", "support": {"issues": "https://github.com/maxmind/web-service-common-php/issues", "source": "https://github.com/maxmind/web-service-common-php/tree/v0.9.0"}, "time": "2022-03-28T17:43:20+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.45", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/96aaebcf4f50d3d2692ab81d2c5132e425bca266", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.45"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "time": "2023-11-07T21:57:25+00:00"}, {"name": "moneyphp/money", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/moneyphp/money.git", "reference": "0dc40e3791c67e8793e3aa13fead8cf4661ec9cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/0dc40e3791c67e8793e3aa13fead8cf4661ec9cd", "reference": "0dc40e3791c67e8793e3aa13fead8cf4661ec9cd", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"cache/taggable-cache": "^0.4.0", "doctrine/instantiator": "^1.0.5", "ext-bcmath": "*", "ext-gmp": "*", "ext-intl": "*", "florianv/exchanger": "^1.0", "florianv/swap": "^3.0", "friends-of-phpspec/phpspec-code-coverage": "^3.1.1 || ^4.3", "moneyphp/iso-currencies": "^3.2.1", "php-http/message": "^1.4", "php-http/mock-client": "^1.0.0", "phpspec/phpspec": "^3.4.3", "phpunit/phpunit": "^5.7.27 || ^6.5.14 || ^7.5.18 || ^8.5", "psr/cache": "^1.0", "symfony/phpunit-bridge": "^4"}, "suggest": {"ext-bcmath": "Calculate without integer limits", "ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "support": {"issues": "https://github.com/moneyphp/money/issues", "source": "https://github.com/moneyphp/money/tree/v3.3.3"}, "time": "2022-09-21T07:43:36+00:00"}, {"name": "monolog/monolog", "version": "2.9.3", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "a30bfe2e142720dfa990d0a7e573997f5d884215"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/a30bfe2e142720dfa990d0a7e573997f5d884215", "reference": "a30bfe2e142720dfa990d0a7e573997f5d884215", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.38 || ^9.6.19", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.9.3"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2024-04-12T20:52:51+00:00"}, {"name": "nesbot/carbon", "version": "2.72.5", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "afd46589c216118ecd48ff2b95d77596af1e57ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/afd46589c216118ecd48ff2b95d77596af1e57ed", "reference": "afd46589c216118ecd48ff2b95d77596af1e57ed", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev", "dev-2.x": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2024-06-03T19:18:41+00:00"}, {"name": "nette/schema", "version": "v1.2.5", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/0462f0166e823aad657c9224d0f849ecac1ba10a", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a", "shasum": ""}, "require": {"nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0", "php": "7.1 - 8.3"}, "require-dev": {"nette/tester": "^2.3 || ^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.5"}, "time": "2023-10-05T20:37:59+00:00"}, {"name": "nette/utils", "version": "v3.2.10", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "a4175c62652f2300c8017fb7e640f9ccb11648d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/a4175c62652f2300c8017fb7e640f9ccb11648d2", "reference": "a4175c62652f2300c8017fb7e640f9ccb11648d2", "shasum": ""}, "require": {"php": ">=7.2 <8.4"}, "conflict": {"nette/di": "<3.0.6"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "~2.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.10"}, "time": "2023-07-30T15:38:18+00:00"}, {"name": "nikic/fast-route", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/nikic/FastRoute.git", "reference": "181d480e08d9476e61381e04a71b34dc0432e812"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/FastRoute/zipball/181d480e08d9476e61381e04a71b34dc0432e812", "reference": "181d480e08d9476e61381e04a71b34dc0432e812", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35|~5.7"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"FastRoute\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Fast request router for PHP", "keywords": ["router", "routing"], "support": {"issues": "https://github.com/nikic/FastRoute/issues", "source": "https://github.com/nikic/FastRoute/tree/master"}, "time": "2018-02-13T20:26:39+00:00"}, {"name": "nyholm/psr7", "version": "1.8.1", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "aa5fc277a4f5508013d571341ade0c3886d4d00e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/aa5fc277a4f5508013d571341ade0c3886d4d00e", "reference": "aa5fc277a4f5508013d571341ade0c3886d4d00e", "shasum": ""}, "require": {"php": ">=7.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/message-factory": "^1.0", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.8.1"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2023-11-13T09:31:12+00:00"}, {"name": "opekunov/laravel-centrifugo-broadcaster", "version": "v1.2.7", "source": {"type": "git", "url": "https://github.com/Opekunov/laravel-centrifugo-broadcaster.git", "reference": "c7186fd0ba2af07014571dee30284049f0fef33c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Opekunov/laravel-centrifugo-broadcaster/zipball/c7186fd0ba2af07014571dee30284049f0fef33c", "reference": "c7186fd0ba2af07014571dee30284049f0fef33c", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~6.0|^7.0", "laravel/framework": "^8.75|^9.0", "php": "^7.4|^8.0"}, "require-dev": {"orchestra/testbench": "^v7.4.0", "phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"laravel": {"providers": ["Opekunov\\Centrifugo\\CentrifugoServiceProvider"]}}, "autoload": {"psr-4": {"Opekunov\\Centrifugo\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator"}], "description": "Centrifugo broadcaster for laravel 5.6-9.x and Centrifugo >= 2.8.1", "homepage": "https://github.com/opekunov/laravel-centrifugo-broadcaster", "keywords": ["Socket", "broadcaster", "centrifugo", "laravel", "laravel-broadcaster", "real time", "web socket", "web-socket"], "support": {"email": "<EMAIL>", "issues": "https://github.com/opekunov/laravel-centrifugo-broadcaster/issues", "source": "https://github.com/opekunov/laravel-centrifugo-broadcaster"}, "time": "2023-04-16T12:42:50+00:00"}, {"name": "opis/closure", "version": "3.6.3", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/3d81e4309d2a927abbe66df935f4bb60082805ad", "reference": "3d81e4309d2a927abbe66df935f4bb60082805ad", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Opis\\Closure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "support": {"issues": "https://github.com/opis/closure/issues", "source": "https://github.com/opis/closure/tree/3.6.3"}, "time": "2022-01-27T09:35:39+00:00"}, {"name": "palicao/php-rebloom", "version": "0.2.1", "source": {"type": "git", "url": "https://github.com/palicao/phpRebloom.git", "reference": "ea2c7568572548259667880b4ce9c4515407fcb8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/palicao/phpRebloom/zipball/ea2c7568572548259667880b4ce9c4515407fcb8", "reference": "ea2c7568572548259667880b4ce9c4515407fcb8", "shasum": ""}, "require": {"ext-redis": "*", "php": ">=7.2.0"}, "require-dev": {"phpunit/phpunit": "^8.3", "vimeo/psalm": "^3.5"}, "type": "library", "autoload": {"psr-4": {"Palicao\\PhpRebloom\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/palicao/phpRebloom/issues", "source": "https://github.com/palicao/phpRebloom/tree/0.2.1"}, "time": "2020-10-27T20:40:25+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.7.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/52a0d99e69f56b9ec27ace92ba56897fe6993105", "reference": "52a0d99e69f56b9ec27ace92ba56897fe6993105", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2024-05-08T12:18:48+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "paragonie/sodium_compat", "version": "v1.21.1", "source": {"type": "git", "url": "https://github.com/paragonie/sodium_compat.git", "reference": "bb312875dcdd20680419564fe42ba1d9564b9e37"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/sodium_compat/zipball/bb312875dcdd20680419564fe42ba1d9564b9e37", "reference": "bb312875dcdd20680419564fe42ba1d9564b9e37", "shasum": ""}, "require": {"paragonie/random_compat": ">=1", "php": "^5.2.4|^5.3|^5.4|^5.5|^5.6|^7|^8"}, "require-dev": {"phpunit/phpunit": "^3|^4|^5|^6|^7|^8|^9"}, "suggest": {"ext-libsodium": "PHP < 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security.", "ext-sodium": "PHP >= 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security."}, "type": "library", "autoload": {"files": ["autoload.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Pure PHP implementation of libsodium; uses the PHP extension if it exists", "keywords": ["Authentication", "BLAKE2b", "ChaCha20", "ChaCha20-Poly1305", "Chapoly", "Curve25519", "Ed25519", "EdDSA", "Edwards-curve Digital Signature Algorithm", "Elliptic Curve <PERSON>-<PERSON><PERSON>", "Poly1305", "Pure-PHP cryptography", "RFC 7748", "RFC 8032", "Salpoly", "Salsa20", "X25519", "XChaCha20-Poly1305", "XSalsa20-Poly1305", "Xchacha20", "Xsalsa20", "aead", "cryptography", "ecdh", "elliptic curve", "elliptic curve cryptography", "encryption", "libsodium", "php", "public-key cryptography", "secret-key cryptography", "side-channel resistant"], "support": {"issues": "https://github.com/paragonie/sodium_compat/issues", "source": "https://github.com/paragonie/sodium_compat/tree/v1.21.1"}, "time": "2024-04-22T22:05:04+00:00"}, {"name": "php-http/discovery", "version": "1.19.4", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "0700efda8d7526335132360167315fdab3aeb599"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/0700efda8d7526335132360167315fdab3aeb599", "reference": "0700efda8d7526335132360167315fdab3aeb599", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.19.4"}, "time": "2024-03-29T13:00:05+00:00"}, {"name": "php-http/httplug", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/625ad742c360c8ac580fcc647a1541d29e257f67", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.0"}, "time": "2023-04-14T15:10:03+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2024-07-20T21:41:07+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.39", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "211ebc399c6e73c225a018435fe5ae209d1d1485"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/211ebc399c6e73c225a018435fe5ae209d1d1485", "reference": "211ebc399c6e73c225a018435fe5ae209d1d1485", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.39"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-06-24T06:27:33+00:00"}, {"name": "predis/predis", "version": "v1.1.10", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.10"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2022-01-05T17:46:08+00:00"}, {"name": "printu/customerio", "version": "3.5.3", "source": {"type": "git", "url": "https://github.com/printu/customerio.git", "reference": "7583ee6d2bf33beeb5133eaded0e354cbeb565bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/printu/customerio/zipball/7583ee6d2bf33beeb5133eaded0e354cbeb565bf", "reference": "7583ee6d2bf33beeb5133eaded0e354cbeb565bf", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "~7.2", "php": "~7.3|^8.0"}, "conflict": {"userscape/customerio": "*"}, "require-dev": {"phpunit/phpunit": "~9.5"}, "type": "library", "autoload": {"psr-4": {"Customerio\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/krz<PERSON><PERSON>k"}], "description": "PHP API for Customer.io", "homepage": "http://customer.io/", "keywords": ["Customerio", "api", "client", "customer.io", "http", "rest"], "support": {"issues": "https://github.com/printu/customerio/issues", "source": "https://github.com/printu/customerio/tree/3.5.3"}, "time": "2023-06-15T07:36:33+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "pusher/pusher-php-server", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/pusher/pusher-http-php.git", "reference": "2279bcd21a608a76f9be1fe0675aa2dd1efb2fa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pusher/pusher-http-php/zipball/2279bcd21a608a76f9be1fe0675aa2dd1efb2fa0", "reference": "2279bcd21a608a76f9be1fe0675aa2dd1efb2fa0", "shasum": ""}, "require": {"ext-curl": "*", "paragonie/sodium_compat": "^1.6", "php": "^5.4 || ^7.0", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Pusher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Library for interacting with the Pusher REST API", "keywords": ["events", "messaging", "php-pusher-server", "publish", "push", "pusher", "real time", "real-time", "realtime", "rest", "trigger"], "support": {"issues": "https://github.com/pusher/pusher-http-php/issues", "source": "https://github.com/pusher/pusher-http-php/tree/master"}, "time": "2019-01-18T12:10:21+00:00"}, {"name": "queue-interop/amqp-interop", "version": "0.8.2", "source": {"type": "git", "url": "https://github.com/queue-interop/amqp-interop.git", "reference": "a893c72832784ca846fcc1bd86274a6a449a1ab7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/queue-interop/amqp-interop/zipball/a893c72832784ca846fcc1bd86274a6a449a1ab7", "reference": "a893c72832784ca846fcc1bd86274a6a449a1ab7", "shasum": ""}, "require": {"php": "^7.3 | ^8.0", "queue-interop/queue-interop": "^0.7|^0.8|^0.9"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.8.x-dev"}}, "autoload": {"psr-4": {"Interop\\Amqp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "AMQP interop", "support": {"issues": "https://github.com/queue-interop/amqp-interop/issues", "source": "https://github.com/queue-interop/amqp-interop/tree/0.8.2"}, "time": "2021-01-21T11:33:30+00:00"}, {"name": "queue-interop/queue-interop", "version": "0.8.1", "source": {"type": "git", "url": "https://github.com/queue-interop/queue-interop.git", "reference": "117043fd38490f8b5516622cd4b697b33a89ce2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/queue-interop/queue-interop/zipball/117043fd38490f8b5516622cd4b697b33a89ce2b", "reference": "117043fd38490f8b5516622cd4b697b33a89ce2b", "shasum": ""}, "require": {"php": "^7.1.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.5 || ^6.0 || ^7.0 || ^8.0 || ^9.0", "queue-interop/queue-spec": "^0.6@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.7-dev"}}, "autoload": {"psr-4": {"Interop\\Queue\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Promoting the interoperability of MQs objects. Based on Java JMS", "homepage": "https://github.com/queue-interop/queue-interop", "keywords": ["MQ", "jms", "message queue", "messaging", "queue"], "support": {"issues": "https://github.com/queue-interop/queue-interop/issues", "source": "https://github.com/queue-interop/queue-interop/tree/0.8.1"}, "time": "2020-12-21T13:14:51+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/ad7475d1c9e70b190ecffc58f2d989416af339b4", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "symfony/polyfill-php81": "^1.23"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.3.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2022-12-27T19:12:24+00:00"}, {"name": "ramsey/uuid", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df", "reference": "fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df", "shasum": ""}, "require": {"brick/math": "^0.8 || ^0.9", "ext-json": "*", "php": "^7.2 || ^8.0", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php80": "^1.14"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5 || ^9", "slevomat/coding-standard": "^7.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-ctype": "Enables faster processing of character classification using ctype functions.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}, "captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.2.3"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2021-09-25T23:10:38+00:00"}, {"name": "skipper/base-api", "version": "1.2.2.x-dev", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/white-label/base-api.git", "reference": "603e9cfd421e46136b51437aa580df51a14f2aea"}, "require": {"guzzlehttp/guzzle": "^7.0", "php": ">=7.4", "skipper/exceptions": "^1.1"}, "type": "library", "autoload": {"psr-4": {"Skipper\\ApiClient\\": "src/"}}, "authors": [{"name": "skipper", "email": "<EMAIL>"}], "description": "Boilerplate for building api clients", "time": "2024-08-29T12:59:56+00:00"}, {"name": "skipper/exceptions", "version": "1.2.0.x-dev", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/white-label/exceptions.git", "reference": "8a48dd1f69ab4d1ed0ce3aa91bdaa276e1138ded"}, "require": {"php": ">=7.4"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Skipper\\Exceptions\\": "src/"}}, "autoload-dev": {"psr-4": {"Skipper\\Exceptions\\Tests\\": "tests/"}}, "authors": [{"name": "skipper", "email": "<EMAIL>"}], "description": "Simple exception package", "time": "2022-12-07T21:58:03+00:00"}, {"name": "skipper/logger", "version": "1.1.0", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/white-label/logger.git", "reference": "1148b3644cfa154f8c346c6360c8bacbe5308a69"}, "require": {"ext-curl": "*", "ext-json": "*", "ext-openssl": "*", "graylog2/gelf-php": "^1.7", "monolog/monolog": "^2.2", "php": ">=7.4", "psr/log": "^1.0", "skipper/exceptions": "^1.1"}, "require-dev": {"kint-php/kint": "^3.3", "phpunit/phpunit": "^8.2", "raveren/kint": "^1.1"}, "type": "library", "autoload": {"psr-4": {"Skipper\\Logger\\": "src/"}}, "autoload-dev": {"psr-4": {"Skipper\\Logger\\Tests\\": "tests/"}}, "authors": [{"name": "skipper", "email": "<EMAIL>"}], "description": "simple logger library", "time": "2022-04-20T12:50:11+00:00"}, {"name": "skipper/repository", "version": "1.2.0.x-dev", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/white-label/repository.git", "reference": "8d5e13c10a8fad29deebefc04f028747dfe378ee"}, "require": {"php": "^7.4", "skipper/exceptions": "^1.1"}, "require-dev": {"kint-php/kint": "^3.3", "phpunit/phpunit": "^8.2", "react/promise": "^2.8"}, "type": "library", "autoload": {"psr-4": {"Skipper\\Repository\\": "src/"}, "files": ["src/React/helpers.php"]}, "autoload-dev": {"psr-4": {"Skipper\\Repository\\Tests\\": "tests/"}}, "authors": [{"name": "skipper", "email": "<EMAIL>"}], "time": "2022-12-07T18:52:44+00:00"}, {"name": "skipper/search", "version": "1.4.0.x-dev", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/white-label/search.git", "reference": "e7960a48bf0204a025b3e432481651ce51dd4288"}, "require": {"php": ">=7.4", "skipper/repository": "^1.1"}, "require-dev": {"react/promise": "^2.8", "symfony/validator": "^5.1"}, "type": "library", "autoload": {"psr-4": {"Skipper\\Search\\": "src/"}}, "authors": [{"name": "skipper", "email": "<EMAIL>"}], "time": "2024-04-11T11:13:09+00:00"}, {"name": "skipper/strategies", "version": "1.1.1", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/white-label/strategies.git", "reference": "3ebc9f7099cc3e25caab8a64e7fa2f5a396a1c5a"}, "require": {"php": ">=7.4", "psr/log": "^1.0", "skipper/repository": "^1.1"}, "require-dev": {"phpunit/phpunit": "^8.3"}, "type": "library", "autoload": {"psr-4": {"Skipper\\Strategies\\": "src/"}}, "autoload-dev": {"psr-4": {"Skipper\\Strategies\\Tests\\": "tests/"}}, "authors": [{"name": "skippe", "email": "<EMAIL>"}], "time": "2022-05-10T10:37:36+00:00"}, {"name": "spatie/laravel-package-tools", "version": "1.12.1", "source": {"type": "git", "url": "https://github.com/spatie/laravel-package-tools.git", "reference": "09f80fa240d44fafb1c70657c74ee44ffa929357"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-package-tools/zipball/09f80fa240d44fafb1c70657c74ee44ffa929357", "reference": "09f80fa240d44fafb1c70657c74ee44ffa929357", "shasum": ""}, "require": {"illuminate/contracts": "^7.0|^8.0|^9.0", "php": "^7.4|^8.0"}, "require-dev": {"mockery/mockery": "^1.4", "orchestra/testbench": "^5.0|^6.23|^7.0", "phpunit/phpunit": "^9.4", "spatie/test-time": "^1.2"}, "type": "library", "autoload": {"psr-4": {"Spatie\\LaravelPackageTools\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Tools for creating Laravel packages", "homepage": "https://github.com/spatie/laravel-package-tools", "keywords": ["laravel-package-tools", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-package-tools/issues", "source": "https://github.com/spatie/laravel-package-tools/tree/1.12.1"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2022-06-28T14:29:26+00:00"}, {"name": "spatie/laravel-responsecache", "version": "6.6.9", "source": {"type": "git", "url": "https://github.com/spatie/laravel-responsecache.git", "reference": "cab93fa63fd60e03dc5bb33aef7b45cc014ed6a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-responsecache/zipball/cab93fa63fd60e03dc5bb33aef7b45cc014ed6a3", "reference": "cab93fa63fd60e03dc5bb33aef7b45cc014ed6a3", "shasum": ""}, "require": {"illuminate/cache": "^7.13|^8.0", "illuminate/console": "^7.13|^8.0", "illuminate/container": "^7.13|^8.0", "illuminate/http": "^7.13|^8.0", "illuminate/support": "^7.13|^8.0", "nesbot/carbon": "^2.35", "php": "^7.4|^8.0", "spatie/laravel-package-tools": "^1.1"}, "require-dev": {"laravel/framework": "^7.13|^8.0", "mockery/mockery": "^1.4", "orchestra/testbench": "^5.2|^6.0", "phpunit/phpunit": "^9.1.5"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\ResponseCache\\ResponseCacheServiceProvider"], "aliases": {"ResponseCache": "Spatie\\ResponseCache\\Facades\\ResponseCache"}}}, "autoload": {"psr-4": {"Spatie\\ResponseCache\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Speed up a Laravel application by caching the entire response", "homepage": "https://github.com/spatie/laravel-responsecache", "keywords": ["cache", "laravel", "laravel-responsecache", "performance", "response", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-responsecache/issues", "source": "https://github.com/spatie/laravel-responsecache/tree/6.6.9"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}], "time": "2021-03-30T20:11:33+00:00"}, {"name": "spatie/url-signer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/spatie/url-signer.git", "reference": "801e65277e27fc7aba2abe6859284677aaf8b0a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/url-signer/zipball/801e65277e27fc7aba2abe6859284677aaf8b0a4", "reference": "801e65277e27fc7aba2abe6859284677aaf8b0a4", "shasum": ""}, "require": {"league/uri": "^6.0", "league/uri-components": "^2.2", "php": "^7.4|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Spatie\\UrlSigner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sebastian<PERSON><PERSON>ne", "role": "Developer"}], "description": "Generate a url with an expiration date and signature to prevent unauthorized access", "homepage": "https://github.com/spatie/url-signer", "keywords": ["encryption", "security", "sign", "spatie", "url"], "support": {"issues": "https://github.com/spatie/url-signer/issues", "source": "https://github.com/spatie/url-signer/tree/1.2.3"}, "time": "2022-09-20T15:05:57+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/config", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "d4e1db78421163b98dd9971d247fd0df4a57ee5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/d4e1db78421163b98dd9971d247fd0df4a57ee5e", "reference": "d4e1db78421163b98dd9971d247fd0df4a57ee5e", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}, "conflict": {"symfony/finder": "<4.4"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/messenger": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/console", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "cef62396a0477e94fc52e87a17c6e5c32e226b7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/cef62396a0477e94fc52e87a17c6e5c32e226b7f", "reference": "cef62396a0477e94fc52e87a17c6e5c32e226b7f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-26T12:21:55+00:00"}, {"name": "symfony/css-selector", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/ea43887e9afd2029509662d4f95e8b5ef6fc9bbb", "reference": "ea43887e9afd2029509662d4f95e8b5ef6fc9bbb", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "80d075412b557d41002320b96a096ca65aa2c98d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/80d075412b557d41002320b96a096ca65aa2c98d", "reference": "80d075412b557d41002320b96a096ca65aa2c98d", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-24T14:02:46+00:00"}, {"name": "symfony/error-handler", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "db15ba0fd50890156ed40087ccedc7851a1f5b76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/db15ba0fd50890156ed40087ccedc7851a1f5b76", "reference": "db15ba0fd50890156ed40087ccedc7851a1f5b76", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "require-dev": {"symfony/deprecation-contracts": "^2.1|^3", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-23T12:34:05+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "a54e2a8a114065f31020d6a89ede83e34c3b27a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/a54e2a8a114065f31020d6a89ede83e34c3b27a4", "reference": "a54e2a8a114065f31020d6a89ede83e34c3b27a4", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "540f4c73e87fd0c71ca44a6aa305d024ac68cb73"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/540f4c73e87fd0c71ca44a6aa305d024ac68cb73", "reference": "540f4c73e87fd0c71ca44a6aa305d024ac68cb73", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T13:51:25+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.41", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "6d29dd9340b372fa603f04e6df4dd76bb808591e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/6d29dd9340b372fa603f04e6df4dd76bb808591e", "reference": "6d29dd9340b372fa603f04e6df4dd76bb808591e", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/process": "^5.4|^6.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.41"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-28T09:36:24+00:00"}, {"name": "symfony/finder", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "0724c51fa067b198e36506d2864e09a52180998a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/0724c51fa067b198e36506d2864e09a52180998a", "reference": "0724c51fa067b198e36506d2864e09a52180998a", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-22T08:53:29+00:00"}, {"name": "symfony/http-foundation", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "9c375b2abef0b657aa0b7612b763df5c12a465ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/9c375b2abef0b657aa0b7612b763df5c12a465ab", "reference": "9c375b2abef0b657aa0b7612b763df5c12a465ab", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "^1.0|^2.0", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/rate-limiter": "^5.2|^6.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-26T11:59:59+00:00"}, {"name": "symfony/http-kernel", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "948db7caf761dacc8abb9a59465f0639c30cc6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/948db7caf761dacc8abb9a59465f0639c30cc6dd", "reference": "948db7caf761dacc8abb9a59465f0639c30cc6dd", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^5.0|^6.0", "symfony/http-foundation": "^5.4.21|^6.2.7", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.0", "symfony/config": "<5.0", "symfony/console": "<4.4", "symfony/dependency-injection": "<5.3", "symfony/doctrine-bridge": "<5.0", "symfony/form": "<5.0", "symfony/http-client": "<5.0", "symfony/mailer": "<5.0", "symfony/messenger": "<5.0", "symfony/translation": "<5.0", "symfony/twig-bridge": "<5.0", "symfony/validator": "<5.0", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0", "symfony/config": "^5.0|^6.0", "symfony/console": "^4.4|^5.0|^6.0", "symfony/css-selector": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^5.3|^6.0", "symfony/dom-crawler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2|^3", "symfony/process": "^4.4|^5.0|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/translation": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2|^3", "symfony/var-dumper": "^4.4.31|^5.4", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-26T14:46:22+00:00"}, {"name": "symfony/intl", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "75482b3b0aadc7f652d99b4f543b1d21f6562ff4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/75482b3b0aadc7f652d99b4f543b1d21f6562ff4", "reference": "75482b3b0aadc7f652d99b4f543b1d21f6562ff4", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/var-exporter": "^5.4|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Intl\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/", "/Resources/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a PHP replacement layer for the C intl extension that includes additional data from the ICU library", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/lock", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/lock.git", "reference": "d96955c9919fa8609b193ba76e9f527776b70176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/lock/zipball/d96955c9919fa8609b193ba76e9f527776b70176", "reference": "d96955c9919fa8609b193ba76e9f527776b70176", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/dbal": "<2.13"}, "require-dev": {"doctrine/dbal": "^2.13|^3|^4", "predis/predis": "^1.0|^2.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Lock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Creates and manages locks, a mechanism to provide exclusive access to a shared resource", "homepage": "https://symfony.com", "keywords": ["cas", "flock", "locking", "mutex", "redlock", "semaphore"], "support": {"source": "https://github.com/symfony/lock/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/mime", "version": "v5.4.41", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "c71c7a1aeed60b22d05e738197e31daf2120bd42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/c71c7a1aeed60b22d05e738197e31daf2120bd42", "reference": "c71c7a1aeed60b22d05e738197e31daf2120bd42", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4", "symfony/serializer": "<5.4.35|>=6,<6.3.12|>=6.4,<6.4.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/process": "^5.4|^6.4", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.4.35|~6.3.12|^6.4.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.4.41"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-28T09:36:24+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "0424dff1c58f028c451efff2045f5d92410bd540"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/0424dff1c58f028c451efff2045f5d92410bd540", "reference": "0424dff1c58f028c451efff2045f5d92410bd540", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "c027e6a3c6aee334663ec21f5852e89738abc805"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/c027e6a3c6aee334663ec21f5852e89738abc805", "reference": "c027e6a3c6aee334663ec21f5852e89738abc805", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "64647a7c30b2283f5d49b874d84a18fc22054b7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/64647a7c30b2283f5d49b874d84a18fc22054b7a", "reference": "64647a7c30b2283f5d49b874d84a18fc22054b7a", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/a95281b0be0d9ab48050ebd988b967875cdb9fdb", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fd22ab50000ef01661e2a31d850ebaa297f8e03c", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "10112722600777e02d2745716b70c5db4ca70442"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/10112722600777e02d2745716b70c5db4ca70442", "reference": "10112722600777e02d2745716b70c5db4ca70442", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "ec444d3f3f6505bb28d11afa41e75faadebc10a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/ec444d3f3f6505bb28d11afa41e75faadebc10a1", "reference": "ec444d3f3f6505bb28d11afa41e75faadebc10a1", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/77fa7995ac1b21ab60769b7323d600a991a90433", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "3fb075789fb91f9ad9af537c4012d523085bd5af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/3fb075789fb91f9ad9af537c4012d523085bd5af", "reference": "3fb075789fb91f9ad9af537c4012d523085bd5af", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "deedcb3bb4669cae2148bc920eafd2b16dc7c046"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/deedcb3bb4669cae2148bc920eafd2b16dc7c046", "reference": "deedcb3bb4669cae2148bc920eafd2b16dc7c046", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "581ca6067eb62640de5ff08ee1ba6850a0ee472e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/581ca6067eb62640de5ff08ee1ba6850a0ee472e", "reference": "581ca6067eb62640de5ff08ee1ba6850a0ee472e", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.5 || ^3.0", "symfony/http-foundation": "^5.4 || ^6.0"}, "require-dev": {"nyholm/psr7": "^1.1", "psr/log": "^1.1 || ^2 || ^3", "symfony/browser-kit": "^5.4 || ^6.0", "symfony/config": "^5.4 || ^6.0", "symfony/event-dispatcher": "^5.4 || ^6.0", "symfony/framework-bundle": "^5.4 || ^6.0", "symfony/http-kernel": "^5.4 || ^6.0", "symfony/phpunit-bridge": "^6.2"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-main": "2.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-07-26T11:53:26+00:00"}, {"name": "symfony/routing", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "f8dd6f80c96aeec9b13fc13757842342e05c4878"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/f8dd6f80c96aeec9b13fc13757842342e05c4878", "reference": "f8dd6f80c96aeec9b13fc13757842342e05c4878", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<5.3", "symfony/dependency-injection": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^5.3|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-09T20:57:15+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/a2329596ddc8fd568900e3fc76cba42489ecc7f3", "reference": "a2329596ddc8fd568900e3fc76cba42489ecc7f3", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-04-21T15:04:16+00:00"}, {"name": "symfony/string", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "909cec913edea162a3b2836788228ad45fcab337"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/909cec913edea162a3b2836788228ad45fcab337", "reference": "909cec913edea162a3b2836788228ad45fcab337", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "conflict": {"symfony/translation-contracts": ">=3.0"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-20T18:38:32+00:00"}, {"name": "symfony/translation", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "1d702caccb9f091b738696185f778b1bfef7b5b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/1d702caccb9f091b738696185f778b1bfef7b5b2", "reference": "1d702caccb9f091b738696185f778b1bfef7b5b2", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^2.3"}, "conflict": {"symfony/config": "<4.4", "symfony/console": "<5.3", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.3"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-26T12:14:19+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "b0073a77ac0b7ea55131020e87b1e3af540f4664"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/b0073a77ac0b7ea55131020e87b1e3af540f4664", "reference": "b0073a77ac0b7ea55131020e87b1e3af540f4664", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-01-23T13:51:25+00:00"}, {"name": "symfony/uid", "version": "v5.4.45", "source": {"type": "git", "url": "https://github.com/symfony/uid.git", "reference": "512de7894f93ad63a7d5e33f590a83e054f571bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/uid/zipball/512de7894f93ad63a7d5e33f590a83e054f571bc", "reference": "512de7894f93ad63a7d5e33f590a83e054f571bc", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^4.4|^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to generate and represent UIDs", "homepage": "https://symfony.com", "keywords": ["UID", "ulid", "uuid"], "support": {"source": "https://github.com/symfony/uid/tree/v5.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/validator", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "5ad62a0b29cca8b89753b6d5f2bc5a4294b997ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/5ad62a0b29cca8b89753b6d5f2bc5a4294b997ec", "reference": "5ad62a0b29cca8b89753b6d5f2bc5a4294b997ec", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "symfony/translation-contracts": "^1.1|^2|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/cache": "<1.11", "doctrine/lexer": "<1.1", "symfony/dependency-injection": "<4.4", "symfony/expression-language": "<5.1", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.4", "symfony/property-info": "<5.3", "symfony/translation": "<5.4.35|>=6.0,<6.3.12|>=6.4,<6.4.3", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.13|^2", "doctrine/cache": "^1.11|^2.0", "egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^4.4|^5.0|^6.0", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/expression-language": "^5.1|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/intl": "^4.4|^5.0|^6.0", "symfony/mime": "^4.4|^5.0|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.3|^6.0", "symfony/translation": "^5.4.35|~6.3.12|^6.4.3", "symfony/yaml": "^4.4|^5.0|^6.0"}, "suggest": {"egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the mapping cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator and the ExpressionLanguageSyntax constraints", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/property-info": "To automatically add NotNull and Type constraints", "symfony/translation": "For translating validation errors.", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-26T11:57:39+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.42", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "0c17c56d8ea052fc33942251c75d0e28936e043d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/0c17c56d8ea052fc33942251c75d0e28936e043d", "reference": "0c17c56d8ea052fc33942251c75d0e28936e043d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.42"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-07-26T12:23:09+00:00"}, {"name": "symfony/yaml", "version": "v5.4.40", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "81cad0ceab3d61fe14fe941ff18a230ac9c80f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/81cad0ceab3d61fe14fe941ff18a230ac9c80f83", "reference": "81cad0ceab3d61fe14fe941ff18a230ac9c80f83", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "require-dev": {"symfony/console": "^5.3|^6.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v5.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:33:22+00:00"}, {"name": "thedevsaddam/lumen-route-list", "version": "2.0", "source": {"type": "git", "url": "https://github.com/thedevsaddam/lumen-route-list.git", "reference": "198633b7e9c23a12290dbee80f4a4d011b855207"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thedevsaddam/lumen-route-list/zipball/198633b7e9c23a12290dbee80f4a4d011b855207", "reference": "198633b7e9c23a12290dbee80f4a4d011b855207", "shasum": ""}, "type": "package", "autoload": {"psr-4": {"Thedevsaddam\\LumenRouteList\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Display all the registered route list like laravel", "homepage": "https://github.com/thedevsaddam/lumen-route-list", "keywords": ["lumen", "lumen routes list", "lumen-route-list", "route-list", "routes", "thedevsaddam"], "support": {"issues": "https://github.com/thedevsaddam/lumen-route-list/issues", "source": "https://github.com/thedevsaddam/lumen-route-list/tree/master"}, "abandoned": true, "time": "2017-09-24T05:18:44+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.2.7", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/83ee6f38df0a63106a9e4536e3060458b74ccedb", "reference": "83ee6f38df0a63106a9e4536e3060458b74ccedb", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.2.7"}, "time": "2023-12-08T13:03:43+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.1", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/a59a13791077fe3d44f90e7133eb68e7d22eaff2", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2024-07-20T21:52:34+00:00"}, {"name": "voku/portable-ascii", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "87337c91b9dfacee02452244ee14ab3c43bc485a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/87337c91b9dfacee02452244ee14ab3c43bc485a", "reference": "87337c91b9dfacee02452244ee14ab3c43bc485a", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/1.6.1"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2022-01-24T18:55:24+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "white-label/internal-errors", "version": "1.0.2.x-dev", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/white-label/internal-errors.git", "reference": "1c515b0b312c44ffcbffab21247d172732b626fd"}, "require": {"php": ">=7.4"}, "require-dev": {"roave/security-advisories": "dev-latest"}, "type": "library", "autoload": {"psr-4": {"InternalErrors\\": "src/"}}, "description": "Package with custom core internal errors", "time": "2025-03-19T20:07:25+00:00"}, {"name": "white-label/redis-buffer", "version": "1.3.0.x-dev", "source": {"type": "git", "url": "https://token:<EMAIL>/internal-dependencies/white-label/redis-buffer.git", "reference": "0639b89a94a46ac2daa4edcd79f07d32aabd2623"}, "require": {"ext-json": "*", "ext-redis": "*", "laravel/lumen-framework": "^8.0", "php": ">=7.4", "predis/predis": "1.1.10"}, "require-dev": {"mockery/mockery": "^1.3.1", "phpunit/phpunit": "^9.3", "roave/security-advisories": "dev-latest"}, "type": "library", "autoload": {"psr-4": {"RedisBuffer\\": "src/"}}, "autoload-dev": {"classmap": ["tests/"]}, "authors": [{"name": "husky", "email": "<EMAIL>"}], "description": "Redis buffer with callbacks", "time": "2024-10-22T11:18:17+00:00"}, {"name": "zircote/swagger-php", "version": "3.3.7", "source": {"type": "git", "url": "https://github.com/zircote/swagger-php.git", "reference": "e8c3bb316e385e93a0c7e8b2aa0681079244c381"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zircote/swagger-php/zipball/e8c3bb316e385e93a0c7e8b2aa0681079244c381", "reference": "e8c3bb316e385e93a0c7e8b2aa0681079244c381", "shasum": ""}, "require": {"doctrine/annotations": "^1.7", "ext-json": "*", "php": ">=7.2", "psr/log": "^1.1 || ^2.0 || ^3.0", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"composer/package-versions-deprecated": "*********", "friendsofphp/php-cs-fixer": "^2.17 || ^3.0", "phpunit/phpunit": ">=8.5.14"}, "bin": ["bin/openapi"], "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://bfanger.nl"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://radebatz.net"}], "description": "swagger-php - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/3.3.7"}, "time": "2023-01-03T21:17:10+00:00"}], "packages-dev": [{"name": "barryvdh/laravel-ide-helper", "version": "v2.14.0", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-ide-helper.git", "reference": "485c756f6cff408d6b273274c5e86112c3973d98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-ide-helper/zipball/485c756f6cff408d6b273274c5e86112c3973d98", "reference": "485c756f6cff408d6b273274c5e86112c3973d98", "shasum": ""}, "require": {"barryvdh/reflection-docblock": "^2.0.6", "composer/class-map-generator": "^1.0", "doctrine/dbal": "^2.6 || ^3", "ext-json": "*", "illuminate/console": "^8 || ^9 || ^10", "illuminate/filesystem": "^8 || ^9 || ^10", "illuminate/support": "^8 || ^9 || ^10", "nikic/php-parser": "^4.18 || ^5", "php": "^7.3 || ^8.0", "phpdocumentor/type-resolver": "^1.1.0"}, "require-dev": {"ext-pdo_sqlite": "*", "friendsofphp/php-cs-fixer": "^2", "illuminate/config": "^8 || ^9 || ^10", "illuminate/view": "^8 || ^9 || ^10", "mockery/mockery": "^1.4", "orchestra/testbench": "^6 || ^7 || ^8", "phpunit/phpunit": "^8.5 || ^9", "spatie/phpunit-snapshot-assertions": "^3 || ^4", "vimeo/psalm": "^5.4"}, "suggest": {"illuminate/events": "Required for automatic helper generation (^6|^7|^8|^9|^10)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.14-dev"}, "laravel": {"providers": ["Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider"]}}, "autoload": {"psr-4": {"Barryvdh\\LaravelIdeHelper\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Laravel IDE Helper, generates correct PHPDocs for all Facade classes, to improve auto-completion.", "keywords": ["autocomplete", "codeintel", "helper", "ide", "laravel", "netbeans", "phpdoc", "phpstorm", "sublime"], "support": {"issues": "https://github.com/barryvdh/laravel-ide-helper/issues", "source": "https://github.com/barryvdh/laravel-ide-helper/tree/v2.14.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2024-02-05T08:16:36+00:00"}, {"name": "barryvdh/reflection-docblock", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/barryvdh/ReflectionDocBlock.git", "reference": "e6811e927f0ecc37cc4deaa6627033150343e597"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/ReflectionDocBlock/zipball/e6811e927f0ecc37cc4deaa6627033150343e597", "reference": "e6811e927f0ecc37cc4deaa6627033150343e597", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "^8.5.14|^9"}, "suggest": {"dflydev/markdown": "~1.0", "erusev/parsedown": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"Barryvdh": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"source": "https://github.com/barryvdh/ReflectionDocBlock/tree/v2.1.1"}, "time": "2023-06-14T05:06:27+00:00"}, {"name": "composer/class-map-generator", "version": "1.3.4", "source": {"type": "git", "url": "https://github.com/composer/class-map-generator.git", "reference": "b1b3fd0b4eaf3ddf3ee230bc340bf3fff454a1a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/class-map-generator/zipball/b1b3fd0b4eaf3ddf3ee230bc340bf3fff454a1a3", "reference": "b1b3fd0b4eaf3ddf3ee230bc340bf3fff454a1a3", "shasum": ""}, "require": {"composer/pcre": "^2.1 || ^3.1", "php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7"}, "require-dev": {"phpstan/phpstan": "^1.6", "phpstan/phpstan-deprecation-rules": "^1", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.1", "symfony/filesystem": "^5.4 || ^6", "symfony/phpunit-bridge": "^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Utilities to scan PHP code and generate class maps.", "keywords": ["classmap"], "support": {"issues": "https://github.com/composer/class-map-generator/issues", "source": "https://github.com/composer/class-map-generator/tree/1.3.4"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-06-12T14:13:04+00:00"}, {"name": "composer/pcre", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "ea4ab6f9580a4fd221e0418f2c357cdd39102a90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/ea4ab6f9580a4fd221e0418f2c357cdd39102a90", "reference": "ea4ab6f9580a4fd221e0418f2c357cdd39102a90", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.8"}, "require-dev": {"phpstan/phpstan": "^1.11.8", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.2.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-07-25T09:36:02+00:00"}, {"name": "dg/bypass-finals", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/dg/bypass-finals.git", "reference": "86b00f0d900c7e15d3341e687e0df89e8c2d4632"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/bypass-finals/zipball/86b00f0d900c7e15d3341e687e0df89e8c2d4632", "reference": "86b00f0d900c7e15d3341e687e0df89e8c2d4632", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "^2.3", "phpstan/phpstan": "^0.12"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Removes final keyword from source code on-the-fly and allows mocking of final methods and classes", "keywords": ["finals", "mocking", "phpunit", "testing", "unit"], "support": {"issues": "https://github.com/dg/bypass-finals/issues", "source": "https://github.com/dg/bypass-finals/tree/v1.8.0"}, "time": "2024-07-02T22:24:43+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.2", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.9.2"}, "abandoned": true, "time": "2020-12-11T09:56:16+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "kwn/php-rdkafka-stubs", "version": "v2.2.1", "source": {"type": "git", "url": "https://github.com/kwn/php-rdkafka-stubs.git", "reference": "23b865d6b3e8fe1f080aa7371dc1da3339361996"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kwn/php-rdkafka-stubs/zipball/23b865d6b3e8fe1f080aa7371dc1da3339361996", "reference": "23b865d6b3e8fe1f080aa7371dc1da3339361996", "shasum": ""}, "require": {"ext-rdkafka": ">=4.0"}, "require-dev": {"phpunit/phpunit": "^8.2.4"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Rdkafka extension stubs for your IDE", "support": {"issues": "https://github.com/kwn/php-rdkafka-stubs/issues", "source": "https://github.com/kwn/php-rdkafka-stubs/tree/v2.2.1"}, "time": "2022-08-16T15:27:51+00:00"}, {"name": "laravel/tinker", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "502e0fe3f0415d06d5db1f83a472f0f3b754bafe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/502e0fe3f0415d06d5db1f83a472f0f3b754bafe", "reference": "502e0fe3f0415d06d5db1f83a472f0f3b754bafe", "shasum": ""}, "require": {"illuminate/console": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0", "php": "^7.2.5|^8.0", "psy/psysh": "^0.11.1|^0.12.0", "symfony/var-dumper": "^4.3.4|^5.0|^6.0|^7.0"}, "require-dev": {"mockery/mockery": "~1.3.3|^1.4.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "suggest": {"illuminate/database": "The Illuminate Database package (^6.0|^7.0|^8.0|^9.0|^10.0|^11.0)."}, "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "support": {"issues": "https://github.com/laravel/tinker/issues", "source": "https://github.com/laravel/tinker/tree/v2.9.0"}, "time": "2024-01-04T16:10:04+00:00"}, {"name": "mockery/mockery", "version": "1.6.12", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2024-05-16T03:13:13+00:00"}, {"name": "myclabs/deep-copy", "version": "1.12.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "reference": "3a6b9a42cd8f8771bd4295d13e1423fa7f3d942c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.12.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2024-06-12T14:39:25+00:00"}, {"name": "nikic/php-parser", "version": "v5.1.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "683130c2ff8c2739f4822ff7ac5c873ec529abd1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/683130c2ff8c2739f4822ff7ac5c873ec529abd1", "reference": "683130c2ff8c2739f4822ff7ac5c873ec529abd1", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.1.0"}, "time": "2024-07-01T20:03:41+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "153ae662783729388a584b4361f2545e4d841e3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/153ae662783729388a584b4361f2545e4d841e3c", "reference": "153ae662783729388a584b4361f2545e4d841e3c", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.8.2"}, "time": "2024-02-23T11:10:43+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.29.1", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "fcaefacf2d5c417e928405b71b400d4ce10daaf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/fcaefacf2d5c417e928405b71b400d4ce10daaf4", "reference": "fcaefacf2d5c417e928405b71b400d4ce10daaf4", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.29.1"}, "time": "2024-05-31T08:52:43+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.31", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "48c34b5d8d983006bd2adc2d0de92963b9155965"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/48c34b5d8d983006bd2adc2d0de92963b9155965", "reference": "48c34b5d8d983006bd2adc2d0de92963b9155965", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.31"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:37:42+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:48:52+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.6.20", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "49d7820565836236411f5dc002d16dd689cde42f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/49d7820565836236411f5dc002d16dd689cde42f", "reference": "49d7820565836236411f5dc002d16dd689cde42f", "shasum": ""}, "require": {"doctrine/instantiator": "^1.5.0 || ^2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.12.0", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.31", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.4", "phpunit/php-timer": "^5.0.3", "sebastian/cli-parser": "^1.0.2", "sebastian/code-unit": "^1.0.8", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.6", "sebastian/environment": "^5.1.5", "sebastian/exporter": "^4.0.6", "sebastian/global-state": "^5.0.7", "sebastian/object-enumerator": "^4.0.4", "sebastian/resource-operations": "^3.0.4", "sebastian/type": "^3.2.1", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.6-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.6.20"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2024-07-10T11:45:39+00:00"}, {"name": "psy/psysh", "version": "v0.12.4", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "2fd717afa05341b4f8152547f142cd2f130f6818"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/2fd717afa05341b4f8152547f142cd2f130f6818", "reference": "2fd717afa05341b4f8152547f142cd2f130f6818", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-main": "0.12.x-dev"}, "bamarni-bin": {"bin-links": false, "forward-command": false}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.4"}, "time": "2024-06-10T01:18:23+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:27:43+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "reference": "fa0f136dd2334583309d32b62544682ee972b51a", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:41:17+00:00"}, {"name": "sebastian/complexity", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/25f207c40d62b8b7aa32f5ab026c53561964053a", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-22T06:19:30+00:00"}, {"name": "sebastian/diff", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/ba01945089c3a293b01ba9badc29ad55b106b0bc", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:30:58+00:00"}, {"name": "sebastian/environment", "version": "5.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:03:51+00:00"}, {"name": "sebastian/exporter", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/78c00df8f170e02473b682df15bfcdacc3d32d72", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:33:00+00:00"}, {"name": "sebastian/global-state", "version": "5.0.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.7"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:35:11+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/e1e4a170560925c26d424b6a03aed157e7dcc5c5", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-22T06:20:34+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:07:39+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-14T16:00:52+00:00"}, {"name": "sebastian/type", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.2.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:13:03+00:00"}, {"name": "sebastian/version", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:39:44+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^7.4", "ext-json": "*", "ext-newrelic": "*", "ext-redis": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}