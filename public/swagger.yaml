openapi: 3.0.0
info:
  title: WhiteLabel Admin
  description: 'Admin backend API'
  version: 0.0.1
paths:
  /api/v2/{url}:
    get:
      tags:
        - Api Proxy
      description: 'Admin Api Proxy'
      operationId: 9a3d2c4fdc013d90c7e8742d0117cf4c
      parameters:
        - $ref: '#/components/parameters/admin_proxy'
      responses:
        '200':
          description: 'post response'
      security:
        - oauth: [ ]
    put:
      tags:
        - Api Proxy
      description: 'Admin Api Proxy'
      operationId: 9a3d2c4fdc013d90c7e8742d0117cf4c
      parameters:
        - $ref: '#/components/parameters/admin_proxy'
      responses:
        '200':
          description: 'post response'
      security:
        - oauth: [ ]
    post:
      tags:
        - Api Proxy
      description: 'Admin Api Proxy'
      operationId: 9a3d2c4fdc013d90c7e8742d0117cf4c
      parameters:
        - $ref: '#/components/parameters/admin_proxy'
      responses:
        '200':
          description: 'post response'
      security:
        - oauth: [ ]
    delete:
      tags:
        - Api Proxy
      description: 'Admin Api Proxy'
      operationId: 9a3d2c4fdc013d90c7e8742d0117cf4c
      parameters:
        - $ref: '#/components/parameters/admin_proxy'
      responses:
        '200':
          description: 'post response'
      security:
        - oauth: [ ]
    patch:
      tags:
        - Api Proxy
      description: 'Admin Api Proxy'
      operationId: 9a3d2c4fdc013d90c7e8742d0117cf4c
      parameters:
        - $ref: '#/components/parameters/admin_proxy'
      responses:
        '200':
          description: 'post response'
      security:
        - oauth: [ ]
  /api/v4/player/verifications/templates:
    get:
      tags:
        - Player Verification
      description: 'Get verification templates'
      operationId: 'Verification templates'
      responses:
        '200':
          description: 'Success'
          content:
            application/json:
              schema:
                type: array
                items: { $ref: '#/components/schemas/verification_template' }
      security:
        - oauth: [ ]
  /api/v4/statistics/bonuses/balances:
    get:
      summary: "Get bonus balances"
      description: "Retrieve bonus balances with optional related entities."
      tags:
        - Bonus Balance
      parameters:
        - name: bonus_external_id
          in: query
          required: true
          schema:
            type: integer
          description: "The external ID of the bonus."
        - name: pagination[limit]
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 50
          description: "Number of records per page."
        - name: pagination[page]
          in: query
          required: false
          schema:
            type: integer
            minimum: 0
            maximum: 50000
          description: "Page number."
        - name: with
          in: query
          required: false
          schema:
            type: string
          description: "Comma-separated related entities to include (e.g., player,player.playerVerification,player.vipSubStatus)."
      responses:
        "200":
          description: "Successful response with bonus balances."
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                        balance:
                          type: object
                          properties:
                            amount:
                              type: number
                            currency:
                              type: string
                        wager:
                          type: object
                          properties:
                            amount:
                              type: string
                            currency:
                              type: string
                        transfer:
                          type: object
                          properties:
                            amount:
                              type: number
                            currency:
                              type: string
                        active:
                          type: boolean
                        popup_seen:
                          type: boolean
                        casino:
                          type: boolean
                        bets:
                          type: boolean
                        orig_bonus:
                          type: object
                          properties:
                            amount:
                              type: number
                            currency:
                              type: string
                        orig_wager:
                          type: object
                          properties:
                            amount:
                              type: string
                            currency:
                              type: string
                        status:
                          type: string
                        in_game:
                          type: object
                          properties:
                            amount:
                              type: number
                            currency:
                              type: string
                        expire_at:
                          type: integer
                        bonus:
                          type: object
                          properties:
                            id:
                              type: integer
                        created_at:
                          type: integer
                        min_bet:
                          type: string
                          nullable: true
                        bonus_external_id:
                          type: string
                          nullable: true
                        type:
                          type: string
                        player:
                          type: object
                          properties:
                            client_id:
                              type: integer
                            info:
                              type: object
                              properties:
                                first_name:
                                  type: string
                                last_name:
                                  type: string
                                email:
                                  type: string
                                email_confirmed:
                                  type: boolean
                                uuid:
                                  type: string
                                phone:
                                  type: object
                                  properties:
                                    phone:
                                      type: string
                                    code:
                                      type: string
                                    is_confirmed:
                                      type: boolean
                            meta:
                              type: object
                              properties:
                                last_seen_ip:
                                  type: string
                                  nullable: true
                                blocked:
                                  type: boolean
                                registered_at:
                                  type: integer
                            balance:
                              type: object
                              properties:
                                balance:
                                  type: object
                                  properties:
                                    amount:
                                      type: string
                                    currency:
                                      type: string
                                total_deposit:
                                  type: object
                                  properties:
                                    amount:
                                      type: string
                                    currency:
                                      type: string
                  meta:
                    type: object
                    properties:
                      page:
                        type: integer
                      limit:
                        type: integer
                      offset:
                        type: integer
                      total:
                        type: integer
                      count:
                        type: integer
        "400":
          description: "Validation error."
        "500":
          description: "Internal server error."
  /api/v2/api/v3/player/verifications:
    post:
      tags:
        - Player Verification
      description: 'Create player verification'
      operationId: 'Create verification'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                form_id:
                  description: 'Player verification template ID'
                  type: string
                  required: true
                player_uuid:
                  description: 'Player UUID'
                  type: string
                  required: true
                admin_name:
                  description: 'Admin name'
                  type: string
                  required: true
      responses:
        '200':
          description: 'Success'
          content:
            application/json:
              schema:
                { $ref: '#/components/schemas/verification' }
      security:
        - oauth: [ ]
  /api/v2/api/v3/player/verifications/cancel:
    post:
      tags:
        - Player Verification
      description: 'Cancel player verification'
      operationId: 'Cancel verification'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                player_uuid:
                  description: 'Player UUID'
                  type: string
                  required: true
                admin_name:
                  description: 'Admin name'
                  type: string
                  required: true
      responses:
        '200':
          description: 'Success'
          content:
            application/json:
              schema:
                { $ref: '#/components/schemas/verification_canceled' }
      security:
        - oauth: [ ]
  /api/v1/segments:
    get:
      tags:
        - Segments
      description: 'get all segments'
      responses:
        '200':
          description: 'Success'
          content:
            application/json:
              schema:
                properties:
                  data: {type: array, items: { $ref: '#/components/schemas/segment_resource'}}
      security:
        - oauth: [ ]
  /api/v1/settings/banners:
    get:
      tags:
        - Banners
      description: 'search for banners | only for admins'
      operationId: f3bac3249f64b6fa0a5485afbdd7b25f
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: location
          in: query
          description: 'banner location'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            format: url
        -
          name: name
          in: query
          description: 'banner name search'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            minLength: 4
        -
          name: type
          in: query
          description: 'banner type'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - big
              - small
              - upper
        -
          name: url
          in: query
          description: 'banner location search'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            minLength: 4
        -
          name: language
          in: query
          description: 'banner language'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: lang
          in: query
          description: 'banner language with All option'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: is_for_signed_in
          in: query
          description: 'banner enabled for signed in'
          required: false
          allowEmptyValue: false
          schema:
            type: boolean
        -
          name: enabled
          in: query
          description: enabled
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: valid
          in: query
          description: 'is valid'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/banner' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Banners
      description: 'create banner'
      operationId: e118561793f48eabb6c5159579a72522
      requestBody:
        content:
          application/json:
            schema:
              required:
                - image
                - location
              properties:
                name:
                  description: 'banner name'
                  type: string
                  maxLength: 255
                location:
                  description: 'banner url location'
                  type: string
                  maxLength: 255
                language:
                  description: 'banner language'
                  type: string
                  maxLength: 2
                is_for_signed_in:
                  description: 'is banner enabled for signed in'
                  type: boolean
                image:
                  description: 'banner image'
                  type: string
                  format: url
                weight:
                  description: 'banner sort weight'
                  type: integer
                type:
                  description: 'banner type'
                  type: string
                  enum: [big, small, upper]
                disposition:
                  description: 'banner key-value pages to show'
                  type: array
                  items: { type: string }
                enabled:
                  description: 'is banner enabled'
                  type: boolean
                start_at:
                  description: 'start timestamp'
                  type: integer
                end_at:
                  description: 'end timestamp'
                  type: integer
              type: object
      responses:
        '200':
          description: 'banner object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/banner'
      security:
        -
          oauth: []
  '/api/v1/settings/banners/{id}':
    put:
      tags:
        - Banners
      description: 'update banner'
      operationId: f99e720b84549aca6ebb6cac3ff11872
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                name:
                  description: 'banner name'
                  type: string
                  maxLength: 255
                location:
                  description: 'banner url location'
                  type: string
                  maxLength: 255
                language:
                  description: 'banner language'
                  type: string
                  maxLength: 2
                is_for_signed_in:
                  description: 'is banner enabled for signed in'
                  type: boolean
                image:
                  description: 'banner image'
                  type: string
                  format: url
                type:
                  description: 'banner type'
                  type: string
                  enum: [big, small, upper]
                small_image:
                  description: 'banner small image'
                  type: string
                  format: url
                weight:
                  description: 'banner sort weight'
                  type: integer
                disposition:
                  description: 'banner key-value pages to show'
                  type: array
                  items: { type: string }
                enabled:
                  description: 'is banner enabled'
                  type: boolean
                start_at:
                  description: 'start timestamp'
                  type: integer
                end_at:
                  description: 'end timestamp'
                  type: integer
              type: object
      responses:
        '200':
          description: 'updated banner object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/banner'
      security:
        -
          oauth: []
    delete:
      tags:
        - Banners
      description: 'delete banner'
      operationId: 7dedce61720e76e42d2492c51ec5251c
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      responses:
        '200':
          description: 'deleted banner object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/banner'
      security:
        -
          oauth: []
  /api/v1/bonuses/balances:
    get:
      tags:
        - Bonuses
      description: 'get bonus balances | only for admins'
      operationId: 5bdf668d105c313d9935abc924e49d1d
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: bonus
          in: query
          description: 'bonus id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: type
          in: query
          description: 'bonus type'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - wager
              - free_money
              - freebet
        -
          name: external_id
          in: query
          description: 'betby id'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: bonus_external_id
          in: query
          description: 'betby template id'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: currency
          in: query
          description: 'balance currency'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: active
          in: query
          description: enabled
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: external
          in: query
          description: betby
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: casino
          in: query
          description: casino
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: bets
          in: query
          description: bets
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
              enum:
                - player
                - bonus
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/bonus' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
  /api/v1/bonuses:
    get:
      tags:
        - Bonuses
      description: 'search for bonuses | only for admins | add /welcome for players | add /archives for all bonuses'
      operationId: 8efd70129a1d6a1e37f80fe06299409f
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: currency
          in: query
          description: 'bonus currency'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: external_id
          in: query
          description: 'betby id'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: type
          in: query
          description: 'bonus type'
          required: false
          allowEmptyValue: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
              enum:
                - wager
                - freebet
                - freemoney
        -
          name: active
          in: query
          description: enabled
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: welcome
          in: query
          description: welcome
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: promo
          in: query
          description: 'is promocode'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: onetime
          in: query
          description: onetime
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: valid
          in: query
          description: 'is valid'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: external
          in: query
          description: 'is betby'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: win_type
          in: query
          description: 'bonus win type'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - bonus
              - real
        - name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          explode: false
          schema:
            type: array
            items:
              type: string
              minimum: 1
              enum:
                - slots
                - slotProviders
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/bonus_template' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Bonuses
      description: 'create bonus | only for admins'
      operationId: 1518e73cc2ae24fe1f52e41e83d650bf
      requestBody:
        content:
          application/json:
            schema:
              required:
                - max_transfer
                - max_bonus
                - wager
                - currency
              properties:
                name:
                  description: 'bonus name'
                  type: string
                image:
                  description: 'bonus image'
                  type: string
                  format: url
                description:
                  description: 'bonus transaltions'
                  properties: { en: { description: 'english description', type: string } }
                  type: object
                condition:
                  description: 'bonus condition transaltions'
                  properties: { en: { description: 'english condition', type: string } }
                  type: object
                bonus_name:
                  description: 'bonus name transaltions'
                  properties: { en: { description: 'english condition', type: string } }
                  type: object
                max_bonus:
                  description: 'max bonus in cents'
                  type: integer
                duration:
                  description: 'bonus duration in secs'
                  type: integer
                max_transfer:
                  description: 'max transfer in cents'
                  type: integer
                wager:
                  description: 'wager factor'
                  type: number
                currency:
                  description: currency
                  type: string
                min_bet:
                  description: 'min bet in cents'
                  type: integer
                from:
                  description: 'start timestamp'
                  type: integer
                to:
                  description: 'end timestamp'
                  type: integer
                active:
                  description: 'active status'
                  type: boolean
                is_onetime:
                  description: 'onetime status'
                  type: boolean
                is_welcome:
                  description: 'welcome status'
                  type: boolean
                min_factor:
                  nullable: true
                  description: 'min factor to play'
                  type: number
                casino:
                  description: 'for casino'
                  type: boolean
                bets:
                  description: 'for bets'
                  type: boolean
                data:
                  description: 'bonus data'
                  properties: { deposit_factors: { description: 'deposit factors', type: array, items: { type: number } }, min_deposit: { description: 'min deposit in cents', type: integer }, tournament_name: { description: 'tournament requirement', type: string }, win_type: { description: 'freebet win type', type: string, enum: [real, bonus] }, sport_name: { description: 'sport requirement', type: string } }
                  type: object
                slot_providers_ids:
                  description: 'related providers ids'
                  type: array
                  items: { type: integer }
                slots_ids:
                  description: 'related providers ids'
                  type: array
                  items: { type: integer }
              type: object
      responses:
        '200':
          description: 'created bonus'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bonus_template'
      security:
        -
          oauth: []
  '/api/v1/bonuses/{id}':
    put:
      tags:
        - Bonuses
      description: 'update bonus | only for admins'
      operationId: dfede89c5b876c4d0d8e57ccf7f9a143
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                name:
                  description: 'bonus name'
                  type: string
                image:
                  description: 'bonus image'
                  type: string
                  format: url
                description:
                  description: 'bonus transaltions'
                  properties: { en: { description: 'english description', type: string } }
                  type: object
                condition:
                  description: 'bonus condition transaltions'
                  properties: { en: { description: 'english condition', type: string } }
                  type: object
                max_bonus:
                  description: 'max bonus in cents'
                  type: integer
                min_bet:
                  description: 'min bet in cents'
                  type: integer
                max_transfer:
                  description: 'max transfer in cents'
                  type: integer
                wager:
                  description: 'wager factor'
                  type: number
                from:
                  description: 'start timestamp'
                  type: integer
                duration:
                  description: 'bonus duration in secs'
                  type: integer
                to:
                  description: 'end timestamp'
                  type: integer
                active:
                  description: 'active status'
                  type: boolean
                casino:
                  description: 'for casino'
                  type: boolean
                bets:
                  description: 'for bets'
                  type: boolean
                is_onetime:
                  description: 'onetime status'
                  type: boolean
                is_welcome:
                  description: 'welcome status'
                  type: boolean
                min_factor:
                  nullable: true
                  description: 'min factor to play'
                  type: number
                data:
                  description: 'bonus data'
                  properties: { deposit_factors: { description: 'deposit factors', type: array, items: { type: number } }, min_deposit: { description: 'min deposit in cents', type: integer }, win_type: { description: 'freebet win type', type: string, enum: [real, bonus] }, tournament_name: { description: 'tournament requirement', type: string }, sport_name: { description: 'sport requirement', type: string } }
                  type: object
                slot_providers_ids:
                  description: 'related providers ids'
                  type: array
                  items: { type: integer }
                slots_ids:
                  description: 'related providers ids'
                  type: array
                  items: { type: integer }
              type: object
      responses:
        '200':
          description: 'created bonus'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bonus_template'
      security:
        -
          oauth: []
  /api/v1/bonuses/onetime:
    post:
      tags:
        - Bonuses
      description: 'apply custom bonus | only for admins'
      operationId: dba5fd7d4dc0b819df13d7925745f61d
      requestBody:
        content:
          application/json:
            schema:
              properties:
                bonus_id:
                  description: 'bonus id'
                  type: integer
                player_id:
                  description: 'player id'
                  type: integer
              type: object
      responses:
        '200':
          description: 'created bonus'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bonus_template'
      security:
        -
          oauth: []
  /api/v1/bonuses/info:
    get:
      tags:
        - Bonuses
      description: 'get bonuses info | only for admins'
      operationId: 5b3f904d2d82eff6d5cc708252f57805
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: currency
          in: query
          description: 'bonus currency'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: casino
          in: query
          description: casino
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: welcome
          in: query
          description: welcome
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: onetime
          in: query
          description: onetime
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: valid
          in: query
          description: 'valid datetime'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: name
          in: query
          description: 'bonus info name'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: button
          in: query
          description: 'button type'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - get_bonus
              - proceed
              - none
        -
          name: asc
          in: query
          description: 'asc sort by column'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: desc
          in: query
          description: 'desc sort by column'
          required: false
          allowEmptyValue: false
          schema:
            type: string
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/bonus_info' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Bonuses
      description: 'create bonus info | only for admins'
      operationId: 47ead8c46cbab6f4238bee19095ead91
      requestBody:
        content:
          application/json:
            schema:
              properties:
                name:
                  description: 'bonus name'
                  type: string
                currency:
                  description: currency
                  type: string
                active:
                  description: 'active status'
                  type: boolean
                visible_from:
                  description: 'start timestamp'
                  type: integer
                visible_to:
                  description: 'end timestamp'
                  type: integer
                casino:
                  description: 'for casino'
                  type: boolean
                is_welcome:
                  description: 'welcome status'
                  type: boolean
                is_onetime:
                  description: 'onetime status'
                  type: boolean
                image:
                  description: 'bonus image'
                  type: string
                  format: url
                bonus_name:
                  description: 'bonus name transaltions'
                  properties: { en: { description: 'english condition', type: string } }
                  type: object
                description:
                  description: 'bonus transaltions'
                  properties: { en: { description: 'english description', type: string } }
                  type: object
                description_info:
                  description: 'bonus description info transaltions'
                  properties: { en: { description: 'english descirption info', type: string } }
                  type: object
                condition_title:
                  description: 'bonus condition title transaltions'
                  properties: { en: { description: 'english condition title', type: string } }
                  type: object
                condition:
                  description: 'bonus condition transaltions'
                  properties: { en: { description: 'english condition', type: string } }
                  type: object
                sort_order:
                  description: 'bonus sort order'
                  type: number
                proceed_link:
                  description: 'proceed bonus link'
                  type: string
                bonus_id:
                  description: 'related bonus id'
                  type: integer
              type: object
      responses:
        '200':
          description: 'created bonus info'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bonus_info'
      security:
        -
          oauth: []
  '/api/v1/bonuses/info/{id}':
    put:
      tags:
        - Bonuses
      description: 'create bonus info | only for admins'
      operationId: f649b23c05f30f8295cb4e0c773c7a3c
      requestBody:
        content:
          application/json:
            schema:
              properties:
                name:
                  description: 'bonus name'
                  type: string
                currency:
                  description: currency
                  type: string
                active:
                  description: 'active status'
                  type: boolean
                visible_from:
                  description: 'start timestamp'
                  type: integer
                visible_to:
                  description: 'end timestamp'
                  type: integer
                casino:
                  description: 'for casino'
                  type: boolean
                is_welcome:
                  description: 'welcome status'
                  type: boolean
                is_onetime:
                  description: 'onetime status'
                  type: boolean
                image:
                  description: 'bonus image'
                  type: string
                  format: url
                bonus_name:
                  description: 'bonus name transaltions'
                  properties: { en: { description: 'english condition', type: string } }
                  type: object
                description:
                  description: 'bonus transaltions'
                  properties: { en: { description: 'english description', type: string } }
                  type: object
                description_info:
                  description: 'bonus description info transaltions'
                  properties: { en: { description: 'english descirption info', type: string } }
                  type: object
                condition_title:
                  description: 'bonus condition title transaltions'
                  properties: { en: { description: 'english condition title', type: string } }
                  type: object
                condition:
                  description: 'bonus condition transaltions'
                  properties: { en: { description: 'english condition', type: string } }
                  type: object
                sort_order:
                  description: 'bonus sort order'
                  type: number
                proceed_link:
                  description: 'proceed bonus link'
                  type: string
                bonus_id:
                  description: 'related bonus id'
                  type: integer
              type: object
      responses:
        '200':
          description: 'created bonus'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bonus_info'
      security:
        -
          oauth: []
  /api/v1/settings/countries:
    get:
      tags:
        - Settings
      description: 'search for countries | only for admins'
      operationId: 1be46387564e9a280915b4cfbeef6ecf
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: iso_code
          in: query
          description: 'country iso code'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: name
          in: query
          description: 'country name'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: registration_allow
          in: query
          description: 'if currency is allowed for register'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          explode: false
          schema:
            type: array
            items:
              type: string
              minimum: 1
              enum:
                - languages
                - currencies
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/country' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Settings
      description: 'manage currency'
      operationId: 1d89dd7c8712385141d0ed66be9c5f01
      requestBody:
        content:
          application/json:
            schema:
              properties:
                iso_code:
                  description: 'iso code'
                  type: string
                  maxLength: 2
                  minLength: 2
                name:
                  description: 'country name'
                  type: string
                registration_allow:
                  description: 'is alowed to registration'
                  type: boolean
                currencies:
                  description: 'connected with categories'
                  type: array
                  items: { type: integer }
                languages:
                  description: 'connected with languages'
                  type: array
                  items: { type: integer }
              type: object
      responses:
        '200':
          description: 'country object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/country'
      security:
        -
          oauth: []
  /api/v1/settings/countries/enabled:
    get:
      tags:
        - Settings
      description: 'Get enabled countries array'
      operationId: 9462a052248409f5719cfe73cf4ea855
      responses:
        '200':
          description: 'Enabled countries array'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { type: string } }
                type: object
      security:
        -
          oauth: []
  /api/v1/settings/currencies:
    get:
      tags:
        - Settings
      description: 'search for currencies | only for admins'
      operationId: 39af94e1191d15e9559abbaaf52783b8
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: iso
          in: query
          description: 'currency iso code'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: enabled
          in: query
          description: 'if currency is available for register'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/currency' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Settings
      description: 'manage currency'
      operationId: c040c39a89ca1cbfcc888b637d0f1793
      requestBody:
        content:
          application/json:
            schema:
              properties:
                iso_code:
                  description: 'currency iso code'
                  type: string
                  maxLength: 3
                  minLength: 3
                enabled:
                  description: 'is enabled for register'
                  type: boolean
                max_win:
                  description: 'max win amount in cents'
                  type: integer
                min_bet:
                  description: 'min bet amount in cents'
                  type: integer
              type: object
      responses:
        '200':
          description: 'language object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/currency'
      security:
        -
          oauth: []
  /api/v1/players/comments:
    post:
      tags:
        - Profile
      description: 'mass comments assign'
      operationId: 29e48406f82da70cdc3d3da0b94f0b75
      requestBody:
        content:
          application/json:
            schema:
              properties:
                emails:
                  description: 'emails to match'
                  type: array
                  items: { type: string, format: email }
                comment:
                  description: 'new comment'
                  type: string
                  maxLength: 255
              type: object
      responses:
        '200':
          description: 'players affected count'
          content:
            application/json:
              schema:
                properties:
                  players_affected: { description: 'players affected count', type: integer }
                type: object
      security:
        -
          oauth: []
  /api/v1/bonuses/betby:
    get:
      tags:
        - Bonuses
      description: 'get betby bonus templates'
      operationId: dfe21859029c915ab9e91570ac91a52d
      responses:
        '200':
          description: 'betby templates'
      security:
        -
          oauth: []
  '/api/v1/bonuses/betby/{id}':
    post:
      tags:
        - Bonuses
      description: 'convert betby bonus to internal'
      operationId: af21fe034be43011b47ddafb29c91192
      parameters:
        -
          name: id
          in: path
          description: 'betby bonus template id'
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              properties:
                amount:
                  description: 'money in cents'
                  type: integer
                currency:
                  description: currency
                  type: string
                  maxLength: 3
                  minLength: 3
              type: object
      responses:
        '200':
          description: 'internla bonus object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bonus_template'
      security:
        -
          oauth: []
  /info:
    get:
      tags:
        - Service Routes
      description: 'Check health'
      operationId: 001bff879362fa3727ef49c123d1a396
      responses:
        '200':
          description: 'Api info'
          content:
            application/json:
              schema:
                properties:
                  status: { type: string, default: ok }
                  name: { type: string, default: WhiteLabelApp }
                  timestamp: { type: integer, format: U }
                  time: { properties: { date: { type: string } }, type: object }
                  hostname: { description: hostname, type: string }
                type: object
  /docs:
    get:
      tags:
        - Service Routes
      description: 'get open api json docs'
      operationId: cebea2bb0a568be88b5568f2976aba7a
      responses:
        '200':
          description: 'Api docs'
  /api/v1/settings/languages:
    get:
      tags:
        - Settings
      description: 'search for languages | only for admins'
      operationId: 6f4ac425bc6bfc76a817b2d5c3d3a5b8
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: code
          in: query
          description: 'language code'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: enabled
          in: query
          description: 'language blockage status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/language' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Settings
      description: 'manage language'
      operationId: c6bc49711dbf1cc126cdc13867cd7c30
      requestBody:
        content:
          application/json:
            schema:
              properties:
                code:
                  description: 'language iso code'
                  type: string
                  maxLength: 2
                  minLength: 2
                enabled:
                  description: 'is enabled'
                  type: boolean
              type: object
      responses:
        '200':
          description: 'language object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/language'
      security:
        -
          oauth: []
  /api/v1/settings/pages:
    get:
      tags:
        - Pages
      description: 'search for pages | only for admins | add /enabled for players'
      operationId: 859b0c2d3e762e9fd74b6b675257943f
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: slug
          in: query
          description: 'page slug'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: search
          in: query
          description: ' slug search'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            minLength: 4
        -
          name: language
          in: query
          description: 'page language'
          required: false
          allowEmptyValue: false
          schema:
            type: string
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/page' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Pages
      description: 'create static page'
      operationId: 5ce440f66b43fe025138720d07724b89
      requestBody:
        content:
          application/json:
            schema:
              required:
                - slug
              properties:
                title:
                  description: 'page title'
                  type: string
                  maxLength: 255
                slug:
                  description: 'url slug'
                  type: string
                  maxLength: 255
                language:
                  description: 'page language'
                  type: string
                  maxLength: 2
                content:
                  description: 'page content'
                  type: string
                page:
                  description: 'bonus transaltions'
                  properties: { en: { description: 'english description', type: string } }
                  type: object
              type: object
      responses:
        '200':
          description: 'created page object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/page'
      security:
        -
          oauth: []
  '/api/v1/settings/pages/{id}':
    put:
      tags:
        - Pages
      description: 'update static page'
      operationId: 2a99262044c15877254476f80144fc4e
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                title:
                  description: 'page title'
                  type: string
                  maxLength: 255
                slug:
                  description: 'url slug'
                  type: string
                  maxLength: 255
                language:
                  description: 'page language'
                  type: string
                  maxLength: 2
                content:
                  description: 'page content'
                  type: string
                page:
                  description: 'bonus transaltions'
                  properties: { en: { description: 'english description', type: string } }
                  type: object
              type: object
      responses:
        '200':
          description: 'updated page object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/page'
      security:
        -
          oauth: []
    delete:
      tags:
        - Pages
      description: 'delete static page'
      operationId: ********************************
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      responses:
        '200':
          description: 'deleted page'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/page'
      security:
        -
          oauth: []
  /api/v1/payment/methods/decrypt:
    post:
      tags:
        - Payment Methods
      description: 'Decrypt Payment Method Name'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                value:
                  description: 'Encrypted method name'
                  type: string
              type: object
      responses:
        '200':
          content:
            application/json:
              schema:
                properties:
                  name:
                    description: 'Decrypted method name'
                    type: string
      security:
        - oauth: [ ]
  /api/v1/payments:
    get:
      tags:
        - Payments
      description: 'search for payments'
      operationId: af5333618b1a5a5566793f297df89207
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: currency
          in: query
          description: 'payment currency'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            maxLength: 3
            minLength: 3
        -
          name: player
          in: query
          description: 'player id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: status
          in: query
          description: 'payment status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - approved
              - pending
              - declined
              - cancelled
        -
          name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
              enum:
                - player
                - player.balances
                - slot.session
                - slot.provider
        -
          name: source
          in: query
          description: 'source filter'
          required: false
          allowEmptyValue: false
          style: form
          explode: true
          schema:
            properties:
              type:
                description: 'source type'
                type: string
                enum:
                  - bet
                  - limit
                  - wager
                  - deposit
                  - withdraw
              id:
                description: 'source id'
                type: integer
            type: object
        -
          name: only_bonus_balance
          in: query
          description: 'bonus type filter'
          required: false
          allowEmptyValue: false
          schema:
            type: boolean
        -
          name: time
          in: query
          description: 'time filter'
          required: false
          allowEmptyValue: false
          style: form
          explode: true
          schema:
            properties:
              tweek:
                description: 'current week'
                type: string
                enum:
                  - '1'
              lweek:
                description: 'last week'
                type: string
                enum:
                  - '1'
              month:
                description: 'this month'
                type: string
                enum:
                  - '1'
              quarter:
                description: 'last 3 months'
                type: string
                enum:
                  - '1'
              year:
                description: 'this year'
                type: string
                enum:
                  - '1'
            type: object
        -
          name: direction
          in: query
          description: 'payment direction'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - none
              - withdraw
              - deposit
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/payment' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
  '/api/v1/slot-provider/change-enable':
    put:
      tags:
        - Slot Providers
      description: 'update slots_providers_countries.is_enabled '
      operationId: e2a9532a245425483a654ea1db06a284
      requestBody:
        content:
          application/json:
            schema:
              properties:
                slot_provider_id:
                  description: slot_provider_id
                  type: integer
                  minimum: 1
                country_id:
                  description: country_id
                  type: integer
                  minimum: 1
                is_enabled:
                  description: is_enabled
                  type: boolean
              type: object
      responses:
        '200':
          description: 'updated payout'
          content:
           application/json:
                schema:
                  properties:
                    status: { type: string, default: ok }
                  type: object
      security:
        - oauth: [ ]
  '/api/v1/casino/providers/geolocations/{id}':
    delete:
      tags:
        - Slot Providers
      description: 'Delete location'
      operationId: e2a9532a245e425483ad4654ea1db06a3284
      parameters:
        - name: id
          in: path
          description: 'Id from country_slots_providers table'
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 'updated payout'
          content:
            application/json:
              schema:
                properties:
                  status: { type: string, default: ok }
                type: object
      security:
        - oauth: [ ]
  /api/v1/payment/deposits/{user_id}/failed:
    get:
      tags:
        - Payments Failed Deposits for user
      description: 'Payments Failed Deposits for user'
      operationId: af5333618b1a5a5566793f297df89207
#      parameters:
#        -
#          $ref: '#/components/parameters/page_param'
#        -
#          $ref: '#/components/parameters/limit_param'
#        -
#          $ref: '#/components/parameters/all_param'
#        -
#          $ref: '#/components/parameters/id_query_param'
#        -
#          $ref: '#/components/parameters/ids_param'
#        -
#          $ref: '#/components/parameters/desc_param'
#        -
#          $ref: '#/components/parameters/asc_param'
#        -
#          name: currency
#          in: query
#          description: 'payment currency'
#          required: false
#          allowEmptyValue: false
#          schema:
#            type: string
#            maxLength: 3
#            minLength: 3
#        -
#          name: player
#          in: query
#          description: 'player id'
#          required: false
#          allowEmptyValue: false
#          schema:
#            type: integer
#        -
#          name: status
#          in: query
#          description: 'payment status'
#          required: false
#          allowEmptyValue: false
#          schema:
#            type: string
#            enum:
#              - approved
#              - pending
#              - declined
#              - cancelled
#        -
#          name: with
#          in: query
#          description: 'entities to add'
#          required: false
#          allowEmptyValue: false
#          style: form
#          explode: false
#          schema:
#            type: array
#            items:
#              type: string
#              enum:
#                - player
#                - player.balances
#                - slot.session
#                - slot.provider
#        -
#          name: source
#          in: query
#          description: 'source filter'
#          required: false
#          allowEmptyValue: false
#          style: form
#          explode: true
#          schema:
#            properties:
#              type:
#                description: 'source type'
#                type: string
#                enum:
#                  - bet
#                  - limit
#                  - wager
#                  - deposit
#                  - withdraw
#              id:
#                description: 'source id'
#                type: integer
#            type: object
#        -
#          name: only_bonus_balance
#          in: query
#          description: 'bonus type filter'
#          required: false
#          allowEmptyValue: false
#          schema:
#            type: boolean
#        -
#          name: time
#          in: query
#          description: 'time filter'
#          required: false
#          allowEmptyValue: false
#          style: form
#          explode: true
#          schema:
#            properties:
#              tweek:
#                description: 'current week'
#                type: string
#                enum:
#                  - '1'
#              lweek:
#                description: 'last week'
#                type: string
#                enum:
#                  - '1'
#              month:
#                description: 'this month'
#                type: string
#                enum:
#                  - '1'
#              quarter:
#                description: 'last 3 months'
#                type: string
#                enum:
#                  - '1'
#              year:
#                description: 'this year'
#                type: string
#                enum:
#                  - '1'
#            type: object
#        -
#          name: direction
#          in: query
#          description: 'payment direction'
#          required: false
#          allowEmptyValue: false
#          schema:
#            type: string
#            enum:
#              - none
#              - withdraw
#              - deposit
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/failedPaymentDeposits'
      security:
        -
          oauth: []
  /api/v1/deposit/poll/one:
    post:
      tags:
        - Payments Failed Deposits for user
      description: 'Payments Failed Deposit resend'
      operationId: af5333618b1a5a5566793f297df89207
      requestBody:
        content:
          application/json:
            schema:
              properties:
                token:
                  description: 'token'
                  type: string
              type: object
      responses:
        '200':
          description: 'result'
          content:
            application/json:
              schema:

      security:
        -
          oauth: []
  '/api/v1/payouts/{id}':
    put:
      tags:
        - Payouts
      description: 'decline payout | only for admins'
      operationId: e2a9532a245425483a654ea1db06a284
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                player_id:
                  description: player_id
                  type: integer
                comment:
                  description: 'reject comment'
                  type: string
                  maxLength: 255
              type: object
      responses:
        '200':
          description: 'updated payout'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/payout'
      security:
        -
          oauth: []
    post:
      tags:
        - Payouts
      description: 'approve payout | only for admins'
      operationId: 24d3f2fd77f4ab6969cb9aa215fda0f8
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                player_id:
                  description: player_id
                  type: integer
                comment:
                  description: 'reject comment'
                  type: string
                  maxLength: 255
              type: object
      responses:
        '200':
          description: 'approved payout'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/payout'
      security:
        -
          oauth: []
  /api/v1/payouts:
    get:
      tags:
        - Payouts
      description: 'search for payouts | only for admins'
      operationId: fdad4d109ec30ac2d7368aa570986293
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: email
          in: query
          description: 'player''s email'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            format: email
        -
          name: status
          in: query
          description: 'payout status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - created
              - pending
              - rejected
              - declined
              - error
              - approved
        -
          name: player
          in: query
          description: 'player''s id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: transaction_id
          in: query
          description: 'transaction id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: transaction_or_id
          in: query
          description: 'transaction ot payout id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: amount_from
          in: query
          description: 'amount from'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: amount_to
          in: query
          description: 'amount to'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: created_from
          in: query
          description: 'payout created from'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: created_to
          in: query
          description: 'payout created to'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: updated_from
          in: query
          description: 'payout updated from'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: updated_to
          in: query
          description: 'payout updated to'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: updated_payment_from
          in: query
          description: 'payment system updated from'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: updated_payment_to
          in: query
          description: 'payment system updated to'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - player
        -
          name: asc
          in: query
          description: 'sort asc'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - activity
              - profit
              - payout
              - amount
              - action_at
        -
          name: desc
          in: query
          description: 'sort desc'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - activity
              - profit
              - payout
              - amount
              - action_at
        -
          name: wallet_type
          in: query
          description: 'payout system'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - CASHMAAL
        -
          name: suspicious
          in: query
          description: 'suspicious payouts'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - true
              - false
        -
          name: vip
          in: query
          description: 'vip players'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - true
              - false
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/payout' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
  /api/v1/payouts/stats:
    get:
      tags:
        - Payouts
      description: 'get payout statistics'
      operationId: 1cc7c8a2693cc51a8d0ef33167ab8b89
      parameters:
        -
          name: wallet_type
          in: query
          description: wallet_type
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - imps
              - bkash
              - upi
              - paytm
              - pix
              - bt
              - coinspaid
              - phone_pe
              - cashmaal
        -
          name: suspicios
          in: query
          description: 'is sus'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
              - 'true'
              - 'false'
      responses:
        '200':
          description: 'stats object'
      security:
        -
          oauth: []
  '/api/v1/players/{id}/balance':
    get:
      tags:
        - Balance
      description: 'get player balance'
      operationId: 5ecbef0afa4808ed76cefbaf3a04813d
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      responses:
        '200':
          description: 'player balance'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/player_resource'
      security:
        -
          oauth: []
    post:
      tags:
        - Balance
      description: 'update balance meta'
      operationId: d16708aaf4f31895c860c7e7d93b95ba
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                wager:
                  description: 'new wager amount in cents'
                  type: integer
                  minimum: 0
                withdraw:
                  description: 'new withdraw limit amount in cents'
                  type: integer
                  minimum: 0
                comment:
                  description: 'admin comment'
                  type: string
                  maxLength: 255
              type: object
      responses:
        '200':
          description: 'updated balance'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/player_resource'
      security:
        -
          oauth: []
  '/api/v1/players/{id}':
    get:
      tags:
        - Profile
      description: 'get player profile'
      operationId: 9dfdf6329a4a4b03f2a950652620e796
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      responses:
        '200':
          description: player
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/player_resource'
        '404':
          description: 'player was not found'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/exception'
      security:
        -
          oauth: []
    put:
      tags:
        - Profile
      description: 'change basic profile data'
      operationId: b1cd52cf0eba082d1ebb5b076a8efe3e
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                first_name:
                  description: 'new first name'
                  type: string
                  maxLength: 255
                last_name:
                  description: 'new last name'
                  type: string
                  maxLength: 255
                birth:
                  description: birthday
                  type: string
                  format: date
                city:
                  description: city
                  type: string
                language:
                  description: language
                  type: string
                country:
                  description: country
                  type: string
                gender:
                  description: gender
                  type: string
                  enum: [male, female, other]
                email:
                  description: email
                  type: string
                  format: email
                is_under_moderation:
                  description: 'is under moderation'
                  type: boolean
                payout_without_moderation:
                  description: 'payout will work without moderation'
                  type: boolean
                last_used_balance:
                  nullable: true
                  description: 'save balance id to use'
                  type: integer
                phone:
                  description: phone
                  type: string
                challenge_id:
                  description: 'phone challenge id'
                  type: integer
                phone_code:
                  description: 'phone code'
                  type: string
              type: object
      responses:
        '200':
          description: 'updated player'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/player_resource'
        '422':
          description: 'validation error'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/exception'
      security:
        -
          oauth: []
    post:
      tags:
        - Profile
      description: 'change password'
      operationId: c62ef3c14ec0c00498c8e44dbe52171a
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                password:
                  description: 'new password'
                  type: string
                  minLength: 6
                repeat_password:
                  description: 'repeat new password'
                  type: string
                  minLength: 6
                old_password:
                  description: 'old password'
                  type: string
                  minLength: 6
              type: object
      responses:
        '200':
          description: player
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/player_resource'
        '422':
          description: 'validation error'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/exception'
      security:
        -
          oauth: []
  '/api/v1/players/{id}/meta':
    post:
      tags:
        - Profile
      description: 'change players meta data'
      operationId: 407a4e919a81c8dfa3891e4c0e6e335d
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                blocked:
                  description: 'is player blocked'
                  type: boolean
                casino_access:
                  description: 'has player casino access'
                  type: boolean
                comment:
                  description: 'admin comment'
                  type: string
                  maxLength: 255
                is_fake:
                  description: 'mark player as fake'
                  type: boolean
                is_suspicious:
                  description: 'make player as suspicious'
                  type: boolean
                is_vip:
                  description: 'make player as vip'
                  type: boolean
              type: object
      responses:
        '200':
          description: 'modified player'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/player_resource'
      security:
        -
          oauth: []
  /api/v1/players:
    get:
      tags:
        - Profile
      description: 'search for players'
      operationId: 19fd2cf59a7857d5d860f926dcba23e0
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: search
          in: query
          description: 'player''s email, name'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: phone
          in: query
          description: 'player''s phone'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: currency
          in: query
          description: 'player''s currency'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: email
          in: query
          description: 'player''s email'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            format: email
        -
          name: ip
          in: query
          description: 'player''s last seen ip'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            format: ipv4
        -
          name: blocked
          in: query
          description: 'player''s blockage status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: uuid
          in: query
          description: 'player''s uuid'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - balances
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/player' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Profile
      description: 'Register action'
      operationId: f4dbd3736ad1a9557e8e23794bb8da11
      requestBody:
        content:
          application/json:
            schema:
              properties:
                username:
                  description: 'email address'
                  type: string
                  format: email
                phone_code:
                  description: 'phone code if username is a phone'
                  type: string
                password:
                  description: 'repeat password'
                  type: string
                  minLength: 6
                currency:
                  description: 'user currency'
                  type: string
                click_id:
                  description: 'click id'
                  type: string
              type: object
      responses:
        '200':
          description: player
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/player_resource'
      security:
        -
          oauth: []
  '/api/v2/api/v3/players/tag':
    post:
      tags:
        - Tags
      description: 'Add a tag to a player'
      operationId: 'Add a tag to a player'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                player_uuid:
                  description: 'Player UUID'
                  type: string
                tag:
                  description: 'A tag name'
                  type: string
                admin_email:
                  description: 'Admin Email'
                  type: string
              type: object
      responses:
        '200':
          description: 'Tag was added'
          content:
            application/json:
              schema:
                properties:
                  status: { type: boolean, default: success }
                type: object
      security:
        - oauth: []
    delete:
      tags:
        - Tags
      description: 'Delete a tag from a player'
      operationId: 'Delete a tag from a player'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                player_uuid:
                  description: 'Player UUID'
                  type: string
                tag:
                  description: 'A tag name'
                  type: string
                admin_email:
                  description: 'Admin Email'
                  type: string
              type: object
      responses:
        '200':
          description: 'Tag was deleted'
          content:
            application/json:
              schema:
                properties:
                  status: { type: boolean, default: success }
                type: object
      security:
        - oauth: []
  /api/v1/players/data-moderation:
    get:
      tags:
        - Players Moderation
      description: 'search for playes data moderation | only for admins'
      operationId: 65ba2c65038789b4a4f704d8360fc0d8
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: status
          in: query
          description: 'player data moderation status'
          required: false
          allowEmptyValue: true
          schema:
            type: string
            enum:
              - Pending
              - Confirmed
              - Rejected
        -
          name: player
          in: query
          description: 'player id'
          required: false
          allowEmptyValue: true
          schema:
            type: string
        -
          name: created_start
          in: query
          description: 'created start'
          required: false
          allowEmptyValue: true
          schema:
            type: string
        -
          name: created_end
          in: query
          description: 'created end'
          required: false
          allowEmptyValue: true
          schema:
            type: string
        -
          name: action_start
          in: query
          description: 'action start'
          required: false
          allowEmptyValue: true
          schema:
            type: string
        -
          name: action_end
          in: query
          description: 'action end'
          required: false
          allowEmptyValue: true
          schema:
            type: string
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/player-data-moderation' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    put:
      tags:
        - Players Moderation
      description: 'update player data moderation status'
      operationId: c8a29d6acd42a96b92dedd0da142ef17
      requestBody:
        content:
          application/json:
            schema:
              properties:
                moderator_id:
                  description: 'moderator id'
                  type: integer
                moderator_name:
                  description: 'moderator name'
                  type: string
                status:
                  description: status
                  type: string
                  enum: [Pending, Confirmed, Rejected]
                ids:
                  description: 'id`s of the moderation datas'
                  type: array
                  items: { type: integer }
              type: object
      responses:
        '200':
          description: 'updated player(s) data moderation status'
          content:
            application/json:
              schema:
                properties:
                  status: { description: ok, type: string, enum: [ok] }
                type: object
      security:
        -
          oauth: []
  /api/v1/bonuses/promo:
    get:
      tags:
        - Bonuses
      description: 'search for promocodes | only for admins'
      operationId: 3091ce0c668f826d07b859e4af547f7c
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: active
          in: query
          description: enabled
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: welcome
          in: query
          description: welcome
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: onetime
          in: query
          description: onetime
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/promocode' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Bonuses
      description: 'create promo code'
      operationId: d9410f968611fdb3a160d7f8528326bc
      requestBody:
        content:
          application/json:
            schema:
              properties:
                code:
                  description: 'promo code'
                  type: string
                name:
                  description: 'promo code name'
                  type: string
                description:
                  description: 'promo code description'
                  type: string
                active:
                  description: 'promo code is_active'
                  type: boolean
                start_at:
                  description: 'promo code start date'
                  type: integer
                end_at:
                  description: 'promo code end date'
                  type: integer
                limit:
                  description: 'promo code use limit | 0 to infinite'
                  type: integer
                bonus_id:
                  description: 'bonus to attach'
                  type: integer
                stream_id:
                  description: 'stream id of a partner'
                  type: integer
              type: object
      responses:
        '200':
          description: 'created promocode'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/promocode'
      security:
        -
          oauth: []
    patch:
      tags:
        - Bonuses
      description: 'check promo code'
      operationId: f53157f4efc16193d25828161ec5a11c
      requestBody:
        content:
          application/json:
            schema:
              properties:
                code:
                  description: 'promo code'
                  type: string
              type: object
      responses:
        '200':
          description: 'checked promocode'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/promocode'
      security:
        -
          oauth: []
  '/api/v1/bonuses/promo/{id}':
    put:
      tags:
        - Bonuses
      description: 'update promocode'
      operationId: ac309643bc95dca8ef771bd4624d0551
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                name:
                  description: 'promocode name'
                  type: string
                  maxLength: 255
                description:
                  description: 'promo code description'
                  type: string
                active:
                  description: 'promo code is_active'
                  type: boolean
                start_at:
                  description: 'promo code start date'
                  type: integer
                end_at:
                  description: 'promo code end date'
                  type: integer
                limit:
                  description: 'promo code use limit | 0 to infinite'
                  type: integer
              type: object
      responses:
        '200':
          description: 'updated promocode object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/promocode'
      security:
        -
          oauth: []
  '/api/v1/slots/providers/{id}':
    post:
      tags:
        - 'Slot Providers'
      description: 'update provider'
      operationId: 41daebc962d5d668d739e4ac90ef2ec6
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                suspended:
                  description: 'suspended status'
                  type: boolean
                image:
                  description: image
                  type: string
                  format: url
                section_id:
                  description: 'new section id'
                  type: integer
              type: object
      responses:
        '200':
          description: 'players affected count'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/slot_provider'
      security:
        -
          oauth: []
  /api/v1/slots/providers:
    get:
      tags:
        - 'Slot Providers'
      description: 'get external providers stats'
      operationId: df8efd52d9fde26b28fb2384473ff45c
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: name
          in: query
          description: 'provider name'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: section
          in: query
          description: 'provider section'
          required: false
          allowEmptyValue: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
        -
          name: suspended
          in: query
          description: 'block status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: categories
          in: query
          description: categories
          required: false
          allowEmptyValue: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: integer
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/slot_provider' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
  /api/v1/slots/providers/enabled:
    get:
      tags:
        - 'Slot Providers'
      description: 'get external providers'
      operationId: 750afe12db7b484a4b2dfd100b61dc4c
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: name
          in: query
          description: 'provider name'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: section
          in: query
          description: 'provider section'
          required: false
          allowEmptyValue: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
        -
          name: categories
          in: query
          description: categories
          required: false
          allowEmptyValue: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: integer
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/slot_provider' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
  /api/v1/settings:
    get:
      tags:
        - Service Routes
      description: 'get current client settings'
      operationId: 7eb9d2922625bef9ba9430c6e322c575
      parameters:
        -
          name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          explode: false
          schema:
            type: array
            items:
              type: string
              minimum: 1
              enum:
                - pages
                - languages
                - banners
                - currencies
                - countries
      responses:
        '200':
          description: 'currency and payment settings'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/client_settings'
      security:
        -
          oauth: []
    post:
      tags:
        - Service Routes
      description: 'update current client settings'
      operationId: 297071e2aa697bf7ad3d7c75c7d5e7e1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/client_settings'
      responses:
        '200':
          description: 'currency and payment settings'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/client_settings'
      security:
        -
          oauth: []
  /api/v1/slots-bets:
    put:
      tags:
        - 'Slots Bets'
      description: 'update slots bet'
      operationId: d7d511639611fa286ff5c2ca146f512a
      requestBody:
        content:
          application/json:
            schema:
              required:
                - transaction_id
                - player_id
                - status
              properties:
                transaction_id:
                  description: 'unique bet transaction id'
                  type: integer
                player_token:
                  description: 'player token'
                  type: string
                win_amount:
                  description: 'max possible win in cents | requried when status won'
                  type: integer
                status:
                  description: 'new bet status'
                  type: string
                  enum: [won, lost, refunded]
              type: object
      responses:
        '200':
          description: 'updated bet object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bet_response'
      security:
        -
          1stParty: []
    post:
      tags:
        - 'Slots Bets'
      description: 'create slots bet'
      operationId: 62a7e2a49eecde7686a4b88d74df5723
      requestBody:
        content:
          application/json:
            schema:
              properties:
                transaction_id:
                  description: 'unique bet transaction id'
                  type: integer
                player_token:
                  description: 'player token'
                  type: string
                amount:
                  description: 'all bets amount in cents'
                  type: integer
                currency:
                  description: 'all bets currency code'
                  type: string
                slot_id:
                  description: 'internal slot id'
                  type: integer
              type: object
      responses:
        '200':
          description: 'updated bet object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bet_response'
      security:
        -
          1stParty: []
  /api/v1/slots/bets:
    get:
      tags:
        - 'Slots Bets'
      description: 'search for deposits'
      operationId: 2a61b9ccd358d4a3daec1412a2d294a6
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: player
          in: query
          description: 'player''s id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: session
          in: query
          description: 'session id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: token
          in: query
          description: 'session token'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: external
          in: query
          description: 'external bet id'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: slot
          in: query
          description: 'slot id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: status
          in: query
          description: 'slot id'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - in_game
              - won
              - lost
              - refunded
              - jackpot
        -
          name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - session
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/slot_bet' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
  /api/v1/slots/categories:
    get:
      tags:
        - 'Slots Categories'
      description: 'search for slots categories, this route for admins only, also available copy of this route for players /api/v1/slots/categories/enabled'
      operationId: a81afc5afee2a0aa929b2c983e8c87a9
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: slug
          in: query
          description: 'category url slug'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: search
          in: query
          description: 'name search string'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            minLength: 4
        -
          name: section
          in: query
          description: 'section slug'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - casino
              - live-dealers
              - tv-games
        -
          name: section_id
          in: query
          description: 'section id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: enabled
          in: query
          description: 'category enabled status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: slots.mobile
          in: query
          description: 'has slots mobile status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: slots.desktop
          in: query
          description: 'has slots desktop status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: new
          in: query
          description: 'order new cats'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: provider
          in: query
          description: 'Get by providers'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: country
          in: query
          description: 'Get slots list with sertain country ordering settings'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - slots
      responses:
        '200':
          description: 'slot search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/slot_category' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - 'Slots Categories'
      description: 'create slot category'
      operationId: 3cd0352ebf1354e1f607ea8de9c2c4e6
      requestBody:
        content:
          application/json:
            schema:
              required:
                - name
                - slug
                - section_id
              properties:
                name:
                  description: 'category name'
                  type: string
                  maxLength: 255
                image:
                  description: 'category image'
                  type: string
                  format: url
                slug:
                  description: 'category url slug'
                  type: string
                  maxLength: 255
                enabled:
                  description: 'is category is enabled'
                  type: boolean
                section_id:
                  description: 'section id'
                  type: integer
                weight:
                  description: 'sorting weight'
                  type: integer
              type: object
      responses:
        '200':
          description: 'created slot category'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/slot_category'
      security:
        -
          oauth: []
  '/api/v1/slots/categories/{id}':
    put:
      tags:
        - 'Slots Categories'
      description: 'update slot category'
      operationId: 6eb1210c3ae36587356e2c76f0c3e6b8
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                name:
                  description: 'category name'
                  type: string
                  maxLength: 255
                slug:
                  description: 'category url slug'
                  type: string
                  maxLength: 255
                enabled:
                  description: 'is category is enabled'
                  type: boolean
                image:
                  description: 'category image'
                  type: string
                  format: url
                section_id:
                  description: 'section id'
                  type: integer
                weight:
                  description: 'sorting weight'
                  type: integer
              type: object
      responses:
        '200':
          description: 'created slot category'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/slot_category'
      security:
        -
          oauth: []
  /api/v1/slots:
    get:
      tags:
        - Slots
      description: 'search for slots, this route for admins only, also available copy of this route for players /api/v1/slots/enabled'
      operationId: 13b80701f39c29fcbecb196b4906bfd5
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: provider
          in: query
          description: 'slot provider'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - betconstruct
        -
          name: external_provider
          in: query
          description: 'coma separated provider names'
          required: false
          allowEmptyValue: false
          schema:
            type: array
            items:
              type: string
        -
          name: external_id
          in: query
          description: 'slot external id'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: internal_id
          in: query
          description: 'slot internal id'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: slug
          in: query
          description: 'slot url slug'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: search
          in: query
          description: 'name search string'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            minLength: 4
        -
          name: name
          in: query
          description: 'slot name'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: enabled
          in: query
          description: 'slot enabled status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: favorite
          in: query
          description: 'get only favorites'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: suspended
          in: query
          description: 'slot suspended status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: lobby
          in: query
          description: 'has lobby status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: section
          in: query
          description: 'slot section'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - casino
              - live_dealers
              - virtual_sport
        -
          name: mobile
          in: query
          description: 'is mobile status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: desktop
          in: query
          description: 'is desktop status'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
        -
          name: weighted
          in: query
          description: 'sort by category weight'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: category
          in: query
          description: 'in categories'
          required: false
          allowEmptyValue: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: integer
        -
          name: with
          in: query
          description: 'entities to add'
          required: false
          allowEmptyValue: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
              enum:
                - category
                - provider
      responses:
        '200':
          description: 'slot search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/slot' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
  '/api/v1/slots/{id}':
    put:
      tags:
        - Slots
      description: 'change slot options | admin panel only'
      operationId: b23f8d0e77aa653574a2a1295710bbde
      parameters:
        -
          $ref: '#/components/parameters/id_param'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                slug:
                  description: 'url slug'
                  type: string
                categories:
                  description: 'categories objects'
                  type: array
                  items: { properties: { id: { description: 'category id', type: integer }, weight: { description: 'category weight', type: integer } }, type: object }
                meta:
                  description: 'array of html meta tags'
                  properties: { description: { type: string } }
                  type: object
                suspended:
                  description: 'is slot suspended in admin panel'
                  type: boolean
                is_bonus_available:
                  description: 'is slot bonus ready'
                  type: boolean
                is_wager_burning:
                  description: 'is slot bets burn wager'
                  type: boolean
                image:
                  description: 'slot image'
                  type: string
                  format: url
              type: object
      responses:
        '200':
          description: 'modified slot'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/slot'
      security:
        -
          oauth: []
  /api/v1/slots/ids:
    put:
      tags:
        - Slots
      description: 'change slot options | admin panel only'
      operationId: e803a2155146e84dc4580a6bf688241b
      requestBody:
        content:
          application/json:
            schema:
              properties:
                categories:
                  description: 'categories objects'
                  type: array
                  items: { properties: { id: { description: 'category id', type: integer }, slot_id: { description: 'slot id', type: integer }, weight: { description: 'category weight', type: integer }, country_id: { description: 'connected country id', type: integer } }, type: object }
              type: object
      responses:
        '200':
          description: 'modified slot'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/slot'
      security:
        -
          oauth: []

  /api/v1/slots/country-settings:
    get:
      tags:
        - Slots
      description: 'get all exists country with slots settings | admin panel only'
      responses:
        '200':
          description: 'countries list'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/country'
      security:
        - oauth: [ ]
    post:
      tags:
        - Slots
      description: 'add country for slots settings | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                country_id:
                  description: 'creating country id'
                  type: integer
      responses:
        '200':
          description: 'country settings created'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/country'
      security:
        - oauth: [ ]
    delete:
      tags:
        - Slots
      description: 'delete country for slots settings | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                country_id:
                  description: 'deleting country id'
                  type: integer
      responses:
        '200':
          description: 'country settings deleted'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/country'
      security:
        - oauth: [ ]

  /api/v1/subscriptions:
    get:
      tags:
        - Service Routes
      description: 'search for subscriptions'
      operationId: b7f40c63cb4c0725e37b0c1175a5a0f2
      responses:
        '200':
          description: 'slot search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { properties: { provider: { description: provider, type: string }, access_token: { description: token, type: string }, client_id: { description: client, type: integer }, subscriber_id: { description: subscriber, type: integer }, is_active: { description: 'is active', type: boolean }, type: { description: 'type of subsscription', type: string, enum: [slot, line] } }, type: object } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
  /api/v1/slots/sections:
    get:
      tags:
        - Slots Sections
      description: 'search for sections | only for admins | add /enabled for players'
      operationId: 2bec313ad3f4c35d9915b36314566aa6
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: name
          in: query
          description: 'section name search'
          required: false
          allowEmptyValue: false
          schema:
            type: string
            minLength: 4
        -
          name: slug
          in: query
          description: 'section name search'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: enabled
          in: query
          description: enabled
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - '1'
              - '0'
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/slot_section' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
    post:
      tags:
        - Slots Sections
      description: 'create slot section'
      operationId: cd252160bea4e831739c88e7255df4fe
      requestBody:
        content:
          application/json:
            schema:
              required:
                - name
                - slug
              properties:
                name:
                  description: 'section name'
                  type: string
                  maxLength: 255
                slug:
                  description: 'section slug'
                  type: string
                  maxLength: 255
                enabled:
                  description: 'is section enabled'
                  type: boolean
              type: object
      responses:
        '200':
          description: 'section object'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/slot_section'
      security:
        -
          oauth: []
  /api/v1/sports-bets:
    get:
      tags:
        - 'Sports Bets'
      description: 'search for sports bets'
      operationId: 0a3644cd3427c9b314f56aee00676296
      parameters:
        -
          $ref: '#/components/parameters/page_param'
        -
          $ref: '#/components/parameters/limit_param'
        -
          $ref: '#/components/parameters/all_param'
        -
          $ref: '#/components/parameters/id_query_param'
        -
          $ref: '#/components/parameters/ids_param'
        -
          $ref: '#/components/parameters/desc_param'
        -
          $ref: '#/components/parameters/asc_param'
        -
          name: player
          in: query
          description: 'player''s id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: provider
          in: query
          description: provider
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - betby
              - eugene
        -
          name: session
          in: query
          description: 'session id'
          required: false
          allowEmptyValue: false
          schema:
            type: integer
        -
          name: token
          in: query
          description: 'session token'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: external
          in: query
          description: 'external id'
          required: false
          allowEmptyValue: false
          schema:
            type: string
        -
          name: status
          in: query
          description: status
          required: false
          allowEmptyValue: false
          schema:
            type: string
            enum:
              - pending
              - won
              - lost
              - cancelled
              - cashed_out
      responses:
        '200':
          description: 'search result'
          content:
            application/json:
              schema:
                properties:
                  data: { type: array, items: { $ref: '#/components/schemas/sports_bet' } }
                  meta: { $ref: '#/components/schemas/meta' }
                type: object
      security:
        -
          oauth: []
  /api/v1/countries/bind-providers-to-country/{countryId}:
    post:
      tags:
        - Countries
      description: 'bind country to slot provider'
      operationId: 7eb9d2922625bef9ba9430c6e322c575
      parameters:
        - name: countryId
          in: query
          description: 'country id'
          required: true
          allowEmptyValue: false
          explode: false
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: 'currency and payment settings'
          content:
            application/json:
              schema:
                properties:
                  status: { type: string, default: ok }
                type: object
      security:
        - oauth: [ ]
  /api/v1/casino/providers/geolocations:
    get:
      tags:
        - Casino
      description: 'get countries for slot provider'
      operationId: 7eb9d2922625bef9ba9430c6e322c575
      responses:
        '200':
          description: 'get list'
          content:
            application/json:
              schema:
                properties:
                  data: {type: array, items: { $ref: '#/components/schemas/countries_casino_resource'}}
      security:
        - oauth: [ ]
  /api/v1/free-spins/all:
    get:
      tags:
        - Free Spins
      description: 'get all free spins | admin panel only'
      responses:
        '200':
          description: 'free spins list'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/freeSpin'
      security:
        - oauth: [ ]
  /api/v1/free-spins/show/{freeSpinId}:
    get:
      tags:
        - Free Spins
      description: 'get free spin by id | admin panel only'
      responses:
        '200':
          description: 'free spin'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/freeSpin'
      security:
        - oauth: [ ]
  /api/v1/free-spins/games-additional-data:
    get:
      tags:
        - Free Spins
      description: 'get denominations and bets list | admin panel only'
      responses:
        '200':
          description: 'free spin'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/freeSpinGamesAdditional'
      security:
        - oauth: [ ]
  /api/v1/free-spins/add:
    post:
      tags:
        - Free Spins
      description: 'add free spins | admin panel only'
      responses:
        '200':
          description: 'country settings created'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/country'
      security:
        - oauth: [ ]
  /api/v1/free-spins/delete/{freeSpinId}/{slotId}:
    put:
      tags:
        - Free Spins
      description: 'delete free spins | admin panel only'
      responses:
        '200':
          description: 'free spins delete'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/freeSpin'
      security:
        - oauth: [ ]
  /api/v1/free-spins/available-providers:
    get:
      summary: "Get available providers for free spins"
      description: 'Returns a list of available providers for free spins, filtered by supplier and aggregator if specified.'
      operationId: 'getAvailableProviders'
      tags:
        - Free Spins
      parameters:
        - in: query
          name: supplier
          required: false
          schema:
            type: integer
          description: 'Filter by supplier ID'
        - in: query
          name: aggregator
          required: false
          schema:
            type: string
          description: 'Filter by aggregator name'
      responses:
        '200':
          description: 'List of available providers successfully retrieved'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/free_spins_available_providers'
        '401':
          description: 'Unauthorized.'
      security:
        - oauth: [ ]
  /api/v1/free-spins/available-suppliers:
    get:
      summary: 'Get available suppliers for free spins'
      description: 'Returns a list of available suppliers for free spins, including their provider details.'
      operationId: 'getAvailableSuppliers'
      tags:
        - Free Spins
      responses:
        '200':
          description: 'List of available suppliers successfully retrieved'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/free_spins_available_suppliers'
        '401':
          description: 'Unauthorized.'
      security:
        - oauth: [ ]
  /api/v1/free-spins/{id}/available-aggregators:
    get:
      summary: 'Get available aggregators for free spins by supplier ID'
      description: 'Returns a list of available aggregators for free spins for the specified supplier ID.'
      operationId: 'getSupplierAvailableAggregators'
      tags:
        - 'Free Spins'
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: 'Supplier ID to get the available aggregators for'
      responses:
        '200':
          description: 'List of available aggregators successfully retrieved'
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
                  description: 'Aggregator name'
                  example: 'Softswiss'
        '401':
          description: 'Unauthorized.'
      security:
        - oauth: [ ]
  /api/v1/free-spins/available-aggregators:
    get:
      summary: 'Get available aggregators for free spins.'
      description: 'Returns a list of available aggregators for free spins.'
      operationId: 'getAvailableAggregators'
      tags:
        - 'Free Spins'
      responses:
        '200':
          description: 'List of available aggregators successfully retrieved'
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
                  description: 'Aggregator name'
                  example: 'Gamicorp'  # You can list "Gamicorp" as the example
              examples:
                availableAggregators:
                  value: ["Gamicorp", "SoftSwiss"]
        '401':
          description: 'Unauthorized.'
      security:
        - oauth: [ ]
  /api/v1/free-spins/update/{freeSpinId}:
    put:
      tags:
        - Free Spins
      description: 'update free spins | admin panel only'
      responses:
        '200':
          description: 'free spins update'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/freeSpin'
      security:
        - oauth: [ ]
  /api/v1/unsuccessful-deposits:
    get:
      tags:
        - Unsuccessful Deposits
      description: 'get all unsuccessful deposits | admin panel only'
      responses:
        '200':
          description: 'unsuccessful deposits list'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/unsuccessfulDeposits'
      security:
        - oauth: [ ]
  /api/v1/deposits-set-not-actual:
    post:
      tags:
        - Unsuccessful Deposits
      description: 'close unsuccessful deposits | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                admin_username:
                  description: 'closed admin username'
                  type: string
                comment:
                  description: 'comment'
                  type: string
                id:
                  description: 'id'
                  type: string
      responses:
        '200':
          description: 'unsuccessful deposits close'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/closeUnsuccessfulDeposits'
      security:
        - oauth: [ ]
  /api/v1/players/email-changes-history:
    get:
      tags:
        - Email Changes History
      description: 'get all changed emails history | admin panel only'
      responses:
        '200':
          description: 'changed emails history list'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/changedEmails'
      security:
        - oauth: [ ]
  /api/v1/admin/transaction/token:
    get:
      tags:
        - Determine user ID
      description: 'determine user id | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                token:
                  description: 'token'
                  type: string
      responses:
        '200':
          description: 'determine user id'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/determineUserID'
      security:
        - oauth: [ ]
  /api/v1/domains/mirror-domains:
    get:
      tags:
        - Mirror management
      description: 'Mirror management list | admin panel only'
      responses:
        '200':
          description: 'Mirror management'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/mirrorManagement'
      security:
        - oauth: [ ]
    post:
      tags:
        - Mirror management
      description: 'Mirror management add | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                domain:
                  description: 'domain'
                  type: string
                gtm:
                  description: 'gtm'
                  type: string
                url:
                  description: 'url'
                  type: string
                web_push:
                  description: 'web_push'
                  type: string
                is_active:
                  description: 'is_active'
                  type: boolean
                is_redirect:
                  description: 'is_redirect'
                  type: boolean
      responses:
        '200':
          description: 'Mirror management'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/mirrorManagement'
      security:
        - oauth: [ ]
  /api/v1/domains/mirror-domains/{id}:
    delete:
      tags:
        - Mirror management
      description: 'Mirror management delete | admin panel only'
      responses:
        '200':
          description: 'Mirror management'
          content:
            application/json:
              schema:
                properties:

      security:
        - oauth: [ ]
    patch:
      tags:
        - Mirror management
      description: 'Mirror management update | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                domain:
                  description: 'domain'
                  type: string
                gtm:
                  description: 'gtm'
                  type: string
                url:
                  description: 'url'
                  type: string
                web_push:
                  description: 'web_push'
                  type: string
                is_active:
                  description: 'is_active'
                  type: boolean
                is_redirect:
                  description: 'is_redirect'
                  type: boolean
      responses:
        '200':
          description: 'Mirror management'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/mirrorManagement'
      security:
        - oauth: [ ]
  /api/v1/tickets/get:
    get:
      tags:
        - Big Wins
      description: 'get all big wins | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                platform:
                  description: 'platform'
                  type: string
      responses:
        '200':
          description: 'big wins list'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/bigWIns'
      security:
        - oauth: [ ]
  /api/v1/tickets/close/{id}:
    post:
      tags:
        - Big Wins
      description: 'close big wins | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                close_description:
                  description: 'close_description'
                  type: string
                dangerous:
                  description: 'dangerous'
                  type: boolean
                manager_close_id:
                  description: 'manager_close_id'
                  type: integer
      responses:
        '200':
          description: 'big wins list'
          content:
            application/json:
              schema:
                type: string
                items:

      security:
        - oauth: [ ]
  /api/v1/tickets/get_big_balances:
    get:
      tags:
        - Balance Grow
      description: 'get all balance grow list | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                platform:
                  description: 'platform'
                  type: string
      responses:
        '200':
          description: 'balance grow list'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/balanceGrow'
      security:
        - oauth: [ ]
  /api/v1/tickets/get_big_balances/{id}:
    post:
      tags:
        - Balance Grow
      description: 'close balance grow | admin panel only'
      requestBody:
        content:
          application/json:
            schema:
              properties:
                dangerous:
                  description: 'dangerous'
                  type: boolean
                close_description:
                  description: 'close_description'
                  type: string
                manager_close_id:
                  description: 'manager_close_id'
                  type: integer
                profit_factor:
                  description: 'profit_factor'
                  type: string
      responses:
        '200':
          description: 'balance grow list'
          content:
            application/json:
              schema:
                type: string
                items:
      security:
        - oauth: [ ]


  /api/v4/smart-links:
    get:
      tags:
        - Smart Links
      description: 'Search for smart links by keyword'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                search:
                  type: string
                  description: The search keyword to search for.
                  example: "test"
                page:
                  type: integer
                  description: The page number for pagination.
                  example: 1
                per_page:
                  type: integer
                  description: The number of items per page.
                  example: 5
      responses:
        '200':
          description: 'Successful search response'
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          description: 'The ID of the smart link.'
                          example: "105"
                        name:
                          type: string
                          description: 'The name of the smart link.'
                          example: "CPA 30"
                        targetLink:
                          type: string
                          description: 'The target link of the smart link.'
                          example: "https://jtstage.xyz?click=jtc_7a57b0c1_{click_id}"
                        domain:
                          type: string
                          description: 'The domain of the smart link.'
                          example: "jtstage.xyz"
                        trackingDomainId:
                          type: integer
                          description: 'The ID of the tracking domain.'
                          example: 4
                        privacyLevel:
                          type: integer
                          description: 'The privacy level of the smart link.'
                          example: 1
                  page:
                    type: integer
                    description: 'The current page number.'
                    example: 1
                  perPage:
                    type: integer
                    description: 'The number of items per page.'
                    example: 5
                  total:
                    type: integer
                    description: 'The total number of items.'
                    example: 5
                  lastPage:
                    type: integer
                    description: 'The last page number.'
                    example: 1
        '422':
          description: 'Validation error'
          content:
            application/json:
              schema:
                properties:
                  error:
                    type: string
                  message:
                    type: string
      security:
        - oauth: [ ]

  /api/v4/smart-links/history:
    get:
      tags:
        - Smart Links
      description: 'Get the history of changes for a specific smart link.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                offer_id:
                  type: integer
                  description: 'The ID of the offer.'
                  example: 104
                date_from:
                  type: string
                  format: date
                  description: 'The start date for the history range.'
                  example: "2024-12-05"
                date_to:
                  type: string
                  format: date
                  description: 'The end date for the history range.'
                  example: "2024-12-09"
                page:
                  type: integer
                  description: 'The page number for pagination.'
                  example: 1
                per_page:
                  type: integer
                  description: 'The number of items per page.'
                  example: 2
                order:
                  type: string
                  description: 'Order by asc or desc'
                  example: 'desc'
      responses:
        '200':
          description: 'Successful history response'
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: 'The ID of the history record.'
                          example: 5
                        smart_link_id:
                          type: integer
                          description: 'The ID of the smart link.'
                          example: 104
                        name:
                          type: string
                          description: 'The name of the smart link.'
                          example: "test"
                        old_value:
                          type: string
                          description: 'The old target link of the smart link.'
                          example: "https://jtdev.xyz?click=jtc_7a57b0c1_{click_id}"
                        new_value:
                          type: string
                          description: 'The new target link of the smart link.'
                          example: "https://mok.com?click=jtc_7a57b0c1_{click_id}"
                        old_domain:
                          type: string
                          description: 'The old domain of the smart link.'
                          example: "jtdev.com"
                        new_domain:
                          type: string
                          description: 'The new domain of the smart link.'
                          example: "mok.com"
                        admin_email:
                          type: string
                          description: 'The email of the admin who made the change.'
                          example: "email.com"
                        created_at:
                          type: string
                          format: date-time
                          description: 'The date and time when the change was created.'
                          example: "2024-12-06T16:42:34.000000Z"
                        updated_at:
                          type: string
                          format: date-time
                          description: 'The date and time when the change was last updated.'
                          example: "2024-12-11T16:42:34.000000Z"
                  page:
                    type: integer
                    description: 'The current page number.'
                    example: 1
                  perPage:
                    type: integer
                    description: 'The number of items per page.'
                    example: 2
                  total:
                    type: integer
                    description: 'The total number of items.'
                    example: 1
                  lastPage:
                    type: integer
                    description: 'The last page number.'
                    example: 1
        '422':
          description: 'Validation error'
          content:
            application/json:
              schema:
                properties:
                  error:
                    type: string
                  message:
                    type: string
      security:
        - oauth: [ ]

  /api/v4/smart-links/{id:\d+}/domain:
    patch:
      tags:
        - Smart Links
      description: 'Update a domain in the smart link.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                domain:
                  type: string
                  description: 'The new domain for the smart link.'
                  example: "new-domain.com"
      responses:
        '200':
          description: 'Successful update response'
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    description: 'The ID of the updated smart link.'
                    example: true
        '422':
          description: 'Validation error'
          content:
            application/json:
              schema:
                properties:
                  error:
                    type: string
                  message:
                    type: string
        '400':
            description: 'Bad request'
            content:
              application/json:
                schema:
                  properties:
                    code:
                        type: integer
                        example: 400
                    message:
                        type: string
                        example: 'New domain is the same as the old one'
      security:
        - oauth: [ ]

components:
  schemas:
    free_spins_available_suppliers:
      type: object
      properties:
        id:
          type: integer
          description: 'Supplier ID'
        provider:
          type: string
          description: 'Supplier name'
        provider_company:
          type: string
          description: 'The company name associated with the supplier'
        is_approved:
          type: boolean
          description: 'Approval status of the supplier (true if approved)'
        is_slot:
          type: integer
          description: 'Indicates if the supplier is related to slot games (1 for yes, 0 for no)'
    free_spins_available_providers:
      type: object
      properties:
        id:
          type: integer
          description: 'Provider ID'
        client_id:
          type: integer
          description: 'Client ID'
        name:
          type: string
          description: 'Provider name'
        image:
          type: string
          nullable: true
          description: 'Image URL for the provider (if available)'
        section_id:
          type: integer
          nullable: true
          description: 'Section ID the provider belongs to'
        suspended:
          type: integer
          description: 'Suspension status (0 - active, 1 - suspended)'
        created_at:
          type: string
          format: date-time
          description: 'Timestamp when the provider was created'
        updated_at:
          type: string
          format: date-time
          description: 'Timestamp when the provider was last updated'
    meta:
      description: 'search metadata'
      properties:
        pagination:
          description: 'search pagination'
          properties:
            limit:
              description: 'search result limit'
              type: integer
              minimum: 1
            offset:
              description: 'search offset'
              type: integer
              minimum: 0
            page:
              description: 'search page'
              type: integer
              minimum: 1
          type: object
        counts:
          description: 'search counts'
          properties:
            total:
              description: 'total results'
              type: integer
              minimum: 0
            count:
              description: 'page results'
              type: integer
              minimum: 0
          type: object
        links:
          description: 'page links'
          properties:
            next:
              description: 'next page'
              type: string
            prev:
              description: 'prev page'
              type: string
          type: object
      type: object
    bet_response:
      description: 'betting response'
      properties:
        bet:
          description: bet
          anyOf:
            -
              $ref: '#/components/schemas/sports_bet'
            -
              $ref: '#/components/schemas/slot_bet'
        player:
          description: 'player object'
          properties:
            user_id:
              description: 'player id'
              type: string
              format: uuid
            username:
              description: 'player full name'
              type: string
            lang:
              description: 'player language'
              type: string
            feature_flags:
              description: 'player flags'
              properties:
                is_cashout_available:
                  description: 'i dont know what the fuck this is'
                  type: boolean
              type: object
          type: object
        balance:
          $ref: '#/components/schemas/money'
      type: object
    transaction_ids_resource:
      description: 'transaction ids resource data'
      properties:
        transaction_id:
          description: transaction_id
          type: integer
        status:
          description: status
          type: boolean
      type: object
    bonus_template_resource:
      description: 'bonus template resource data'
      properties:
        id:
          description: id
          type: integer
        duration:
          nullable: true
          description: 'bonus duration in sec'
          type: integer
        name:
          description: 'bonus name'
          type: string
        image:
          description: 'bonus image'
          type: string
          format: url
        description:
          description: 'bonus description transaltions'
          properties:
            en:
              description: 'english descirption'
              type: string
          type: object
        condition:
          description: 'bonus condition transaltions'
          properties:
            en:
              description: 'english condition'
              type: string
          type: object
        bonus_name:
          description: 'bonus name transaltions'
          properties:
            en:
              description: 'english bonus name'
              type: string
          type: object
        max_bonus:
          $ref: '#/components/schemas/money'
        wager:
          description: 'wager factor'
          type: number
        active:
          description: 'is active'
          type: boolean
        is_welcome:
          description: 'is welcome bonus'
          type: boolean
        casino:
          description: 'is casino available'
          type: boolean
        bets:
          description: 'is sports bets active'
          type: boolean
        from:
          nullable: true
          description: 'start timestamp for bonus'
          type: number
        to:
          nullable: true
          description: 'end timestamp for bonus'
          type: number
        type:
          description: 'bonus type'
          type: string
          enum:
            - wager
            - no_risk
            - freebet
            - free_money
        bonus_data:
          description: 'custom bonus data'
          properties:
            deposit_factors:
              description: 'deposit bonus multipliers'
              type: array
              items:
                type: number
            promo_code:
              description: promocode
              type: string
            min_deposit:
              description: 'minimum deposit'
              type: integer
            external_id:
              description: 'betby template id'
              type: string
            win_type:
              description: 'freebet win type'
              type: string
              enum:
                - real
                - bonus
            tournament_name:
              description: 'tournament requirement'
              type: string
            sport_name:
              description: 'sport requirement'
              type: string
          type: object
      type: object
    player_resource:
      description: 'player resource object'
      properties:
        id:
          description: 'player''s id'
          type: integer
          minimum: 1
        info:
          $ref: '#/components/schemas/user_info'
        meta:
          $ref: '#/components/schemas/user_meta_resource'
        balance:
          $ref: '#/components/schemas/user_balance'
        plt:
          description: 'Affileate program id. See issue https://jira.deversin.com/browse/NEW-4227'
          type: integer
          maximum: 2
          minimum: 1
      type: object
    user_meta_resource:
      description: 'players meta data'
      properties:
        blocked:
          description: 'is player''s blocked'
          type: boolean
        welcome_bonus_id:
          nullable: true
          description: 'welcome bonus id'
          type: integer
        last_used_bonus_id:
          nullable: true
          description: 'saved bonus balance id'
          type: integer
        casino_access:
          description: 'is player''s has access to casino'
          type: boolean
        last_seen_at:
          description: 'player''s last action at'
          type: integer
        is_fake:
          description: 'is fake player'
          type: boolean
      type: object
    money:
      description: 'money object'
      properties:
        amount:
          description: 'money amount in cents'
          type: string
        currency:
          description: 'currency iso2 code'
          type: string
      type: object
    user_balance:
      description: 'player''s balance'
      properties:
        balance:
          $ref: '#/components/schemas/money'
        total_deposit:
          $ref: '#/components/schemas/money'
        wager:
          $ref: '#/components/schemas/money'
        withdraw_limit:
          $ref: '#/components/schemas/money'
        in_game:
          $ref: '#/components/schemas/money'
        total_payout:
          $ref: '#/components/schemas/money'
        current_payout:
          $ref: '#/components/schemas/money'
        total_bet:
          $ref: '#/components/schemas/money'
        total_profit:
          $ref: '#/components/schemas/money'
        debt:
          $ref: '#/components/schemas/money'
        payout_available:
          $ref: '#/components/schemas/money'
      type: object
    balance_session:
      description: 'balance session'
      properties:
        id:
          description: id
          type: integer
        player:
          $ref: '#/components/schemas/player'
        balance:
          description: 'balance object'
          anyOf:
            -
              $ref: '#/components/schemas/user_balance'
            -
              $ref: '#/components/schemas/bonus'
        created_at:
          description: 'session create timestamp'
          type: integer
        token:
          description: 'session token'
          type: string
      type: object
    banner:
      description: 'banner object'
      properties:
        id:
          description: id
          type: integer
        client_id:
          description: client_id
          type: integer
        name:
          description: 'banner name'
          type: string
        language:
          nullable: true
          description: 'banner language'
          type: string
        is_for_signed_in:
          description: 'if banner enabled for signed in'
          type: boolean
        location:
          description: 'page to redirect'
          type: string
          format: url
        image:
          description: 'image to show'
          type: string
          format: url
        weight:
          description: 'banner weight'
          type: integer
        disposition:
          description: 'pages to show'
          type: array
          items:
            type: string
        enabled:
          description: 'if banner enabled'
          type: boolean
        start_at:
          nullable: true
          description: 'start timestamp'
          type: integer
        end_at:
          nullable: true
          description: 'end timestamp'
          type: integer
      type: object
    verification_template:
      description: 'Player Verification template'
      properties:
        id:
          type: string
          nullable: false
        name:
          type: string
          nullable: false
    verification:
      description: 'Player Verification'
      properties:
        player_uuid:
          type: string
          nullable: false
        verification_id:
          type: string
          nullable: false
        form_id:
          type: string
          description: 'Player verification template ID'
          nullable: false
        form_url:
          type: string
          nullable: false
        status:
          type: string
          enum:
            - unused
            - pending
            - completed
            - canceled
          nullable: false
        verified:
          type: boolean
          nullable: true
        verified_at:
          type: string
          nullable: true
      type: object
    verification_canceled:
      description: 'Player Verification'
      properties:
        player_uuid:
          type: string
          nullable: false
        verification_id:
          type: string
          nullable: false
        form_id:
          type: string
          description: 'Player verification template ID'
          nullable: false
        form_url:
          type: string
          nullable: false
        status:
          type: string
          enum:
            - unused
            - pending
            - completed
            - canceled
          nullable: false
          example: canceled
        verified:
          type: boolean
          nullable: true
        verified_at:
          type: string
          nullable: true
      type: object
    sports_bet:
      description: 'sports bet object'
      properties:
        id:
          description: 'bet id'
          type: integer
        player_id:
          description: 'player id'
          type: integer
        bonus_id:
          nullable: true
          description: 'bonus balance id'
          type: integer
        external_id:
          description: 'external betby id'
          type: string
        status:
          description: 'bet status'
          type: string
          enum:
            - pending
            - lost
            - won
            - cancelled
            - cashed_out
        bet:
          $ref: '#/components/schemas/money'
        possible_win:
          $ref: '#/components/schemas/money'
        original_win:
          $ref: '#/components/schemas/money'
        provider:
          description: 'provider name'
          type: string
          enum:
            - betby
        id_hash:
          description: 'wtf?'
          type: string
        factor:
          description: 'total odds factor'
          type: number
        action_at:
          description: 'bet timestamp'
          type: integer
        updated_at:
          description: 'updated timestamp'
          type: integer
        session_id:
          description: 'balance session id'
          type: integer
        is_comboboost:
          description: 'is bet a comboboost'
          type: boolean
        events:
          description: 'bet events'
          type: array
          items:
            $ref: '#/components/schemas/sports_bet_event'
      type: object
    sports_bet_event:
      description: 'bet event object'
      properties:
        id:
          description: 'bet event id'
          type: integer
        bet_id:
          description: 'bet id'
          type: integer
        external_id:
          description: 'bet event betby id'
          type: string
        category:
          description: 'sports category'
          type: string
        odds:
          description: 'event odds'
          type: string
        outcome:
          description: 'event outcome'
          type: string
        market:
          description: market
          type: string
        sport:
          description: 'sport name'
          type: string
        tournament:
          description: 'tournament name'
          type: string
        bet:
          $ref: '#/components/schemas/money'
        place_at:
          description: 'scheduled event time'
          type: integer
        status:
          description: 'event status'
          type: string
        competitors:
          description: competitors
          type: array
          items:
            type: string
      type: object
    bonus_template:
      description: 'bonus template data'
      properties:
        id:
          description: id
          type: integer
        client_id:
          description: 'client id'
          type: integer
        duration:
          nullable: true
          description: 'bonus duration in sec'
          type: integer
        name:
          description: 'bonus name'
          type: string
        image:
          description: 'bonus image'
          type: string
          format: url
        description:
          description: 'bonus description transaltions'
          properties:
            en:
              description: 'english descirption'
              type: string
          type: object
        condition:
          description: 'bonus condition transaltions'
          properties:
            en:
              description: 'english condition'
              type: string
          type: object
        bonus_name:
          description: 'bonus name transaltions'
          properties:
            en:
              description: 'english bonus name'
              type: string
          type: object
        max_transfer:
          $ref: '#/components/schemas/money'
        max_bonus:
          $ref: '#/components/schemas/money'
        min_bet:
          $ref: '#/components/schemas/money'
        wager:
          description: 'wager factor'
          type: number
        min_factor:
          description: 'minimum bet factor'
          type: number
        active:
          description: 'is active'
          type: boolean
        is_welcome:
          description: 'is welcome bonus'
          type: boolean
        is_onetime:
          description: 'is one time bonus'
          type: boolean
        is_external:
          description: 'is betby bonus'
          type: boolean
        is_promo:
          description: 'is promocode'
          type: boolean
        casino:
          description: 'is casino available'
          type: boolean
        bets:
          description: 'is sports bets active'
          type: boolean
        from:
          nullable: true
          description: 'start timestamp for bonus'
          type: number
        to:
          nullable: true
          description: 'end timestamp for bonus'
          type: number
        type:
          description: 'bonus type'
          type: string
          enum:
            - wager
            - no_risk
            - freebet
            - free_money
        uses:
          description: 'total bonus balances'
          type: integer
        transfers:
          description: 'total bonus transfers'
          type: integer
        total_transferred:
          $ref: '#/components/schemas/money'
        total_uses:
          $ref: '#/components/schemas/money'
        bonus_data:
          description: 'custom bonus data'
          properties:
            deposit_factors:
              description: 'deposit bonus multipliers'
              type: array
              items:
                type: number
            promo_code:
              description: promocode
              type: string
            min_deposit:
              description: 'minimum deposit'
              type: integer
            external_id:
              description: 'betby template id'
              type: string
            win_type:
              description: 'freebet win type'
              type: string
              enum:
                - real
                - bonus
            tournament_name:
              description: 'tournament requirement'
              type: string
            sport_name:
              description: 'sport requirement'
              type: string
          type: object
        slot_providers:
          description: 'related slot providers'
          type: array
          items:
            $ref: '#/components/schemas/slot_provider'
        slots:
          description: 'related slots'
          type: array
          items:
            $ref: '#/components/schemas/slot'
      type: object
    bonus:
      description: 'bonus balance object'
      properties:
        id:
          description: id
          type: integer
        created_at:
          description: 'created at timestamp'
          type: integer
        player:
          description: 'player objetc'
          type: object
          anyOf:
            -
              $ref: '#/components/schemas/player'
            -
              properties:
                id:
                  description: id
                  type: integer
              type: object
        bonus:
          description: 'bonus template object'
          type: object
          anyOf:
            -
              $ref: '#/components/schemas/bonus_template'
            -
              properties:
                id:
                  description: id
                  type: integer
              type: object
        balance:
          $ref: '#/components/schemas/money'
        wager:
          $ref: '#/components/schemas/money'
        orig_wager:
          $ref: '#/components/schemas/money'
        in_game:
          $ref: '#/components/schemas/money'
        orig_bonus:
          $ref: '#/components/schemas/money'
        expire_at:
          nullable: true
          description: 'expire timestamp'
          type: integer
        status:
          description: 'balance status'
          type: string
          enum:
            - pending
            - expired
            - transferred
            - deactivated
            - bankrupt
        transfer:
          $ref: '#/components/schemas/money'
        active:
          description: active
          type: boolean
        valid:
          description: 'is ready for betting'
          type: boolean
        casino:
          description: 'casino available'
          type: boolean
        bets:
          description: 'sports bets available'
          type: boolean
        bonus_external_id:
          nullable: true
          description: 'betby external id'
          type: string
        type:
          description: 'bonus type'
          type: string
          enum:
            - wager
            - freebet
            - freemoney
            - no_risk
      type: object
    countries_casino_resource:
      description: 'Countries casino resource'
      properties:
          id:
            type: integer
            example: 1
          name:
            type: string
            example: "Example Name"
    bonus_info:
      description: 'bonus info template data'
      properties:
        id:
          description: id
          type: integer
        client_id:
          description: 'client id'
          type: integer
        name:
          description: 'bonus name'
          type: string
        currency:
          description: 'bonus currency'
          type: string
        active:
          description: 'is active'
          type: boolean
        visible_from:
          nullable: true
          description: 'start timestamp for bonus'
          type: number
        visible_to:
          nullable: true
          description: 'end timestamp for bonus'
          type: number
        casino:
          description: 'is casino available'
          type: boolean
        is_welcome:
          description: 'is welcome bonus'
          type: boolean
        is_onetime:
          description: 'is one time bonus'
          type: boolean
        image:
          description: 'bonus image'
          type: string
          format: url
        bonus_name:
          description: 'bonus name transaltions'
          properties:
            en:
              description: 'english bonus name'
              type: string
          type: object
        description:
          description: 'bonus description transaltions'
          properties:
            en:
              description: 'english descirption'
              type: string
          type: object
        description_info:
          description: 'bonus description info transaltions'
          properties:
            en:
              description: 'english descirption info'
              type: string
          type: object
        condition_title:
          description: 'bonus condition title transaltions'
          properties:
            en:
              description: 'english condition title'
              type: string
          type: object
        condition:
          description: 'bonus condition transaltions'
          properties:
            en:
              description: 'english condition'
              type: string
          type: object
        sort_order:
          description: 'bonus sort order'
          type: number
        proceed_link:
          description: 'proceed bonus link (if button_type=proceed)'
          type: string
        bonus_id:
          description: 'related bonus id'
          type: integer
        created_at:
          nullable: false
          description: 'created at'
          type: integer
        updated_at:
          nullable: false
          description: 'updated at'
          type: integer
        player_data:
          $ref: '#/components/schemas/bonus_info_player_data'
      type: object
    bonus_transfer:
      description: 'bonus transfer object'
      properties:
        id:
          description: id
          type: integer
        bonus_id:
          description: 'bonus balance id'
          type: integer
        player_id:
          description: 'player id'
          type: integer
        action_at:
          description: 'action at timestamp'
          type: integer
        amount:
          $ref: '#/components/schemas/money'
      type: object
    client_settings:
      description: 'main client settings'
      properties:
        client_id:
          description: 'client id'
          type: integer
        payment_gateways:
          description: 'array of available payment gateways'
          type: array
          items:
            type: string
        name:
          description: 'client''s name'
          type: string
        domain:
          description: 'client''s domain'
          type: string
          format: url
        links:
          description: 'array of social links'
          type: array
          items:
            type: string
        pages:
          nullable: true
          description: 'array of Page objects'
          type: array
          items:
            $ref: '#/components/schemas/page'
        banners:
          description: 'array of Banner objects'
          type: array
          items:
            $ref: '#/components/schemas/banner'
        languages:
          description: 'array of Language objects'
          type: array
          items:
            $ref: '#/components/schemas/language'
        currencies:
          description: 'array of Currency objects'
          type: array
          items:
            $ref: '#/components/schemas/currency'
        default_currency:
          $ref: '#/components/schemas/currency'
        min_factor:
          nullable: true
          description: 'min bet factor to burn wager'
          type: number
        countries:
          description: 'array of Country objects'
          type: array
          items:
            $ref: '#/components/schemas/country'
      type: object
    freeSpin:
      description: 'free spin object'
      properties:
        id:
          description: id
          type: integer
        author_id:
          description: author_id
          type: integer
        bet:
          description: bet
          type: integer
        count:
          description: count
          type: integer
        created_at:
          description: created_at
          type: string
        currency:
          description: currency
          type: string
        denomination:
          description: denomination
          type: string
        expired_at:
          description: expired_at
          type: string
        in_process:
          description: in_process
          type: boolean
        is_active:
          description: is_active
          type: boolean
        name:
          description: name
          type: string
        provider_id:
          description: provider_id
          type: integer
        provider:
          description: 'provider object'
          type: object
        slot_id:
          description: 'slot id'
          type: integer
        slot:
          description: 'slot object'
          type: object
        start_at:
          description: 'start_at time'
          type: string
        status:
          description: 'free spin status'
          type: string
        updated_at:
          description: 'free spin updated time'
          type: string
        updated_by_author_id:
          description: 'free spin updated object'
          type: object
        users:
          description: 'free spin applied users object'
          type: object
      type: object
    freeSpinGamesAdditional:
      description: 'free spin games additional data'
      properties:
        denominations:
          description: 'denominations object'
          type: object
        bets:
          description: 'bets object'
          type: object
      type: object
    unsuccessfulDeposits:
      description: 'unsuccessful deposits object'
      properties:
        id:
          description: id
          type: string
        admin_username:
          description: admin_username
          type: string
        amount:
          description: amount
          type: integer
        comment:
          description: comment
          type: string
        commented_at:
          description: commented_at
          type: string
        created_at:
          description: created_at
          type: object
        email:
          description: email
          type: string
        is_actual:
          description: is_actual
          type: boolean
        is_last:
          description: is_last
          type: boolean
        method:
          description: method
          type: string
        payment_method:
          description: payment_method
          type: string
        payment_system:
          description: payment_system
          type: string
        phone:
          description: 'phone'
          type: string
        registration_date:
          description: 'registration_date'
          type: string
        successful_deposits:
          description: 'successful deposits'
          type: integer
        tag:
          description: 'tag'
          type: string
        transaction_id:
          description: 'transaction_id'
          type: string
        trigger:
          description: 'trigger'
          type: string
        unsuccessful_deposit:
          description: 'unsuccessful_deposit'
          type: integer
        user_id:
          description: 'user id'
          type: integer
        uuid:
          description: 'uuid'
          type: string
      type: object
    closeUnsuccessfulDeposits:
      description: 'closed unsuccessful deposits object'
      properties:
        id:
          description: id
          type: string
        admin_username:
          description: admin_username
          type: string
        amount:
          description: amount
          type: integer
        comment:
          description: comment
          type: string
        commented_at:
          description: commented_at
          type: string
        is_actual:
          description: is_actual
          type: boolean
        method:
          description: method
          type: string
        payment_method:
          description: payment_method
          type: string
        payment_system:
          description: payment_system
          type: string
        transaction_id:
          description: 'transaction_id'
          type: string
        user_id:
          description: 'user id'
          type: integer
      type: object
    changedEmails:
      description: 'changed emails object'
      properties:
        id:
          description: id
          type: integer
        client_id:
          description: client_id
          type: integer
        moderator_name:
          description: moderator_name
          type: string
        player_id:
          description: player_id
          type: integer
        new_email:
          description: new_email
          type: string
        old_email:
          description: old_email
          type: string
        created_at:
          description: created_at
          type: object
      type: object
    determineUserID:
      description: 'determine user id'
      properties:
        label:
          description: label
          type: integer
      type: object
    mirrorManagement:
      description: 'Mirror management'
      properties:
        id:
          description: id
          type: integer
        domain:
          description: domain
          type: string
        gtm:
          description: gtm
          type: string
        is_active:
          description: is_active
          type: boolean
        is_redirect:
          description: is_redirect
          type: boolean
        url:
          description: url
          type: string
        web_push:
          description: web_push
          type: string
      type: object
    bigWIns:
      description: 'Big Wins'
      properties:
        id:
          description: id
          type: integer
        amount:
          description: amount
          type: integer
        bet_id:
          description: bet_id
          type: integer
        case_id:
          description: case_id
          type: integer
        currency:
          description: currency
          type: string
        dangerous:
          description: dangerous
          type: boolean
        date:
          description: date
          type: string
        external_id:
          description: external_id
          type: integer
        factor:
          description: factor
          type: string
        game_id:
          description: game_id
          type: string
        parameter:
          description: parameter
          type: string
        platform:
          description: platform
          type: string
        session_id:
          description: session_id
          type: string
        session_token:
          description: session_token
          type: string
        status:
          description: status
          type: string
        user_id:
          description: user_id
          type: integer
        win:
          description: win
          type: integer
      type: object
    balanceGrow:
      description: 'Balance Grow'
      properties:
        id:
          description: id
          type: integer
        balance:
          description: balance
          type: integer
        case__creation_date:
          description: case__creation_date
          type: string
        case_id:
          description: case_id
          type: integer
        currency:
          description: currency
          type: string
        dangerous:
          description: dangerous
          type: boolean
        case__user_data__current_balance:
          description: case__user_data__current_balance
          type: string
        close_date:
          description: close_date
          type: string
        close_description:
          description: close_description
          type: string
        deposit_date:
          description: deposit_date
          type: string
        parameter:
          description: parameter
          type: string
        platform:
          description: platform
          type: string
        factor:
          description: factor
          type: string
        session_token:
          description: session_token
          type: string
        last_deposit:
          description: last_deposit
          type: integer
        user_id:
          description: user_id
          type: integer
        manager_close_id:
          description: manager_close_id
          type: integer
        status:
          description: status
          type: string
      type: object
    failedPaymentDeposits:
      description: 'Failed Payment Deposits'
      properties:
        id:
          description: id
          type: integer
        aggregator_activate_response:
          description: aggregator_activate_response
          type: string
        aggregator_response:
          description: aggregator_response
          type: string
        aggregator_status:
          description: aggregator_status
          type: string
        currency:
          description: currency
          type: string
        amount:
          description: amount
          type: string
        date:
          description: date
          type: string
        payment_aggregator:
          description: payment_aggregator
          type: string
        payment_method:
          description: payment_method
          type: string
        status:
          description: status
          type: integer
        transaction_token:
          description: transaction_token
          type: string
        user_fields:
          description: user_fields
          type: string
      type: object
    country:
      description: 'banner object'
      properties:
        id:
          description: id
          type: integer
        client_id:
          description: client_id
          type: integer
        iso_code:
          description: 'iso code'
          type: string
          maxLength: 2
          minLength: 2
        name:
          description: 'country name'
          type: string
        registration_allow:
          description: 'is alowed to registration'
          type: boolean
        languages:
          description: 'array of Language objects'
          type: array
          items:
            $ref: '#/components/schemas/language'
        currencies:
          description: 'array of Currency objects'
          type: array
          items:
            $ref: '#/components/schemas/currency'
      type: object
    currency:
      description: 'system currency object'
      properties:
        client_id:
          description: client_id
          type: integer
        iso_code:
          description: 'currency iso code'
          type: string
        name:
          description: 'currency name'
          type: string
        min_bet:
          description: 'minimum bet amount in cents'
          type: integer
        max_win:
          description: 'max win amount in cents'
          type: integer
        enabled:
          description: 'if currency is available for register'
          type: boolean
      type: object
    deposit:
      description: 'deposit object'
      properties:
        id:
          type: integer
        player_id:
          type: integer
        amount:
          $ref: '#/components/schemas/money'
        action_at:
          type: integer
        token:
          type: string
        details:
          nullable: true
          description: 'deposit details'
          type: array
          items:
            type: string
      type: object
    language:
      description: 'language object'
      properties:
        client_id:
          description: 'client id'
          type: integer
        code:
          description: 'iso code'
          type: string
        name:
          description: 'language name'
          type: string
        enabled:
          description: 'is enabled'
          type: boolean
      type: object
    challenge:
      description: 'mfa challenge'
      properties:
        id:
          description: id
          type: integer
        resource:
          description: resource
          type: object
          oneOf:
            -
              $ref: '#/components/schemas/phone.resource'
        requested_at:
          description: 'date of creation in timestamp'
          type: integer
        valid_until:
          description: 'date of expiration in timestamp'
          type: integer
        is_valid:
          description: 'is valid'
          type: boolean
        sent_at:
          nullable: true
          description: 'date of sending in timestamp'
          type: integer
        approved_at:
          nullable: true
          description: 'date of approval in timestamp'
          type: integer
      type: object
    phone.resource:
      description: 'phone resource'
      properties:
        id:
          description: id
          type: integer
        phone:
          description: 'phone full number'
          type: string
        country:
          description: 'country code'
          type: string
        country_code:
          description: 'phone country code'
          type: string
        district_code:
          description: 'phone area code'
          type: string
        number:
          description: 'phone local number'
          type: string
      type: object
    page:
      description: 'static page object'
      properties:
        id:
          description: 'page id'
          type: integer
        client_id:
          description: 'client id'
          type: integer
        title:
          description: 'page title'
          type: string
        slug:
          description: 'page unique slug'
          type: string
        language:
          nullable: true
          description: 'page language'
          type: string
        page:
          description: 'bonus description transaltions'
          properties:
            en:
              description: 'english descirption'
              type: string
          type: object
        content:
          description: 'page content'
          type: string
      type: object
    payment:
      description: 'payment object'
      properties:
        id:
          description: 'payment id'
          type: integer
        type:
          description: 'source type'
          type: string
          enum:
            - bet
            - slot
            - limit
            - withdraw
            - wager
            - deposit
            - bonus_transfer
            - bonus_grant
        source:
          description: 'payment source |deposit|bet|...'
          anyOf:
            -
              $ref: '#/components/schemas/deposit'
            -
              $ref: '#/components/schemas/payout'
            -
              $ref: '#/components/schemas/wager_change'
            -
              $ref: '#/components/schemas/limit_change'
            -
              $ref: '#/components/schemas/sports_bet'
            -
              $ref: '#/components/schemas/slot_bet'
            -
              $ref: '#/components/schemas/bonus_transfer'
            -
              $ref: '#/components/schemas/bonus'
        amount:
          $ref: '#/components/schemas/money'
        action_at:
          description: 'action timestamp'
          type: integer
        updated_at:
          description: 'updated timestamp'
          type: integer
        created_at:
          description: 'created timestamp'
          type: integer
        player:
          description: 'player object'
          anyOf:
            -
              $ref: '#/components/schemas/player'
            -
              properties:
                id:
                  description: 'player id'
                  type: integer
              type: object
        direction:
          description: 'payment direction'
          type: string
          enum:
            - none
            - deposit
            - withdraw
        status:
          description: 'payment status'
          type: string
          enum:
            - approved
            - declined
            - pending
            - cashed_out
      type: object
    payout:
      description: 'money withdraw'
      properties:
        id:
          type: integer
        player:
          anyOf:
            -
              $ref: '#/components/schemas/player'
            -
              properties:
                id:
                  type: integer
              type: object
        action_at:
          type: integer
        amount:
          $ref: '#/components/schemas/money'
        status:
          type: string
          enum:
            - pending
            - approved
            - declined
            - error
            - refected
            - created
        gateway:
          description: 'payment gateway'
          type: string
        aggregator:
          description: 'payment aggregator'
          type: string
        transaction_id:
          description: 'external transaction id'
          type: string
        hash:
          description: 'wtf? some tough shit'
          type: string
        payout_comment:
          description: 'rejection comment'
          type: string
        details:
          nullable: true
          description: 'payout details'
          type: array
          items:
            type: string
      type: object
    player:
      description: 'player object'
      properties:
        id:
          description: 'player''s id'
          type: integer
          minimum: 1
        client_id:
          description: 'player''s client id'
          type: integer
          minimum: 1
        info:
          $ref: '#/components/schemas/user_info'
        meta:
          $ref: '#/components/schemas/user_meta'
        balance:
          $ref: '#/components/schemas/user_balance'
        plt:
          description: 'Affileate program id. See issue https://jira.deversin.com/browse/NEW-4227'
          type: integer
          maximum: 2
          minimum: 1
      type: object
    player-data-moderation:
      description: 'player data moderation object'
      properties:
        id:
          description: id
          type: integer
        player_id:
          description: 'player id'
          type: integer
        moderator_name:
          nullable: true
          description: 'moderator name'
          type: string
        old_email:
          nullable: true
          description: 'old email'
          type: string
        old_first_name:
          nullable: true
          description: 'old first name'
          type: string
        old_last_name:
          nullable: true
          description: 'old last name'
          type: string
        old_birth:
          nullable: true
          description: 'old birth'
          type: string
        old_gender:
          nullable: true
          description: 'old gender'
          type: string
        old_country:
          nullable: true
          description: 'old country'
          type: string
        old_city:
          nullable: true
          description: 'old city'
          type: string
        old_phone:
          nullable: true
          description: 'old phone'
          type: string
        new_email:
          nullable: true
          description: 'new email'
          type: string
        new_first_name:
          nullable: true
          description: 'new first name'
          type: string
        new_last_name:
          nullable: true
          description: 'new last name'
          type: string
        new_birth:
          nullable: true
          description: 'new birth'
          type: string
        new_gender:
          nullable: true
          description: 'new gender'
          type: string
        new_country:
          nullable: true
          description: 'new country'
          type: string
        new_city:
          nullable: true
          description: 'new city'
          type: string
        new_phone:
          nullable: true
          description: 'new phone'
          type: string
        status:
          nullable: true
          description: status
          type: string
        created_at:
          nullable: true
          description: 'created at'
          type: string
        updated_at:
          nullable: true
          description: 'updated at'
          type: string
      type: object
    promocode:
      description: 'promo code object'
      properties:
        id:
          description: id
          type: integer
        name:
          description: name
          type: string
        code:
          description: code
          type: string
        stream_id:
          description: stram_id
          type: integer
        description:
          description: 'code description'
          type: string
        active:
          description: 'is active'
          type: boolean
        valid:
          description: 'is available'
          type: boolean
        is_alanbase:
          description: 'is promo relative to alanbase'
          type: boolean
        limit:
          nullable: true
          description: 'use limit'
          type: integer
        uses:
          description: 'use count'
          type: integer
        start_at:
          description: 'start date'
          type: integer
        end_at:
          nullable: true
          description: 'end date'
          type: integer
        bonus:
          $ref: '#/components/schemas/bonus_template'
      type: object
    slot_section:
      description: 'ultra mega category'
      properties:
        id:
          description: id
          type: integer
        client_id:
          description: 'client id'
          type: integer
        name:
          description: 'section name'
          type: string
        slug:
          description: 'section slug'
          type: string
        enabled:
          description: 'visible flag'
          type: boolean
        categories:
          description: categories
          type: array
          items:
            $ref: '#/components/schemas/slot_category'
      type: object
    slot_bet:
      description: 'slot bet object'
      properties:
        id:
          description: 'bet id'
          type: integer
        slot:
          $ref: '#/components/schemas/slot'
        bet:
          $ref: '#/components/schemas/money'
        win:
          $ref: '#/components/schemas/money'
        status:
          description: 'current status'
          type: string
          enum:
            - in_game
            - won
            - lost
            - refunded
        is_new_flow:
          nullable: true
          description: 'Is bet created by API v3'
          type: boolean
        action_at:
          description: 'bet timestamp'
          type: integer
        bonus_id:
          nullable: true
          description: 'bonus balance id'
          type: integer
        session_id:
          description: 'bonus balance session id'
          type: integer
        external_id:
          description: 'unique external transaction id'
          type: string
        parent_external_id:
          nullable: true
          description: 'unique parent external transaction id'
          type: string
        session:
          nullable: true
          description: 'session object'
          properties:
            token:
              type: string
            id:
              type: integer
          type: object
        balance:
          $ref: '#/components/schemas/money'
      type: object
    slot_category:
      description: 'slot category object'
      properties:
        id:
          description: 'category id'
          type: integer
        client_id:
          description: 'client id'
          type: integer
        name:
          description: 'category name'
          type: string
        slug:
          description: 'category name'
          type: string
        image:
          description: 'category image'
          type: string
          format: url
        enabled:
          description: 'if category enabled'
          type: boolean
        weight:
          description: 'sort weight'
          type: integer
        section_id:
          description: section
          type: integer
        slots:
          nullable: true
          description: slots
          type: array
          items:
            $ref: '#/components/schemas/slot'
        active_slots_count:
          nullable: true
          description: 'slot count'
          type: integer
      type: object
    slot:
      description: 'slot game object'
      properties:
        id:
          description: 'slot id in db'
          type: integer
        client_id:
          description: 'client id'
          type: integer
        name:
          description: 'slot name'
          type: string
        provider:
          description: 'slot provider'
          type: string
        slug:
          description: 'slot url address'
          type: string
        internal_id:
          description: 'slot id in provider''s system'
          type: string
        weight:
          description: 'category weight'
          type: integer
        enabled:
          description: 'is slot enabled'
          type: boolean
        suspended:
          description: 'is slot suspended from admin panel'
          type: boolean
        categories:
          nullable: true
          description: 'slot category'
          type: array
          items:
            $ref: '#/components/schemas/slot_category'
        image:
          description: 'image logo'
          type: string
          format: url
        description:
          description: 'slot description'
          type: string
        is_mobile:
          description: 'is mobile available'
          type: boolean
        is_bonus_available:
          description: 'is bonus available'
          type: boolean
        is_wager_burning:
          description: 'are bets burn wager'
          type: boolean
        is_desktop:
          description: 'is desktop available'
          type: boolean
        has_lobby:
          description: 'has lobby'
          type: boolean
        meta:
          description: 'meta tags'
          type: array
          items:
            type: string
        external_id:
          description: 'slot uuid'
          type: string
        favorite:
          description: 'is current user favorite'
          type: boolean
        external_provider:
          description: 'slot external_provider'
          anyOf:
            -
              $ref: '#/components/schemas/slot_provider'
            -
              properties:
                id:
                  description: 'external_provider id'
                  type: integer
              type: object
      type: object
    slot_provider:
      description: 'slot provider object'
      properties:
        id:
          description: id
          type: integer
        client_id:
          description: 'client id'
          type: integer
        count:
          description: 'slot count'
          type: integer
        name:
          description: 'provider name'
          type: string
        section_id:
          nullable: true
          description: 'default section id'
          type: integer
        image:
          nullable: true
          description: image
          type: string
          format: url
        suspended:
          description: 'suspended status'
          type: boolean
        slots_providers:
          description: 'providers list from related slots'
          type: array
        related_sections:
          description: 'slots related sections'
          properties:
            slug:
              description: 'section slug'
              type: string
            count:
              description: 'sections with current slug count'
              type: integer
          type: object
      type: object
    wager_change:
      description: 'payment source of changing wager'
      properties:
        id:
          type: integer
        player_id:
          type: integer
        wager:
          $ref: '#/components/schemas/money'
        comment:
          description: 'change comment'
          type: string
      type: object
    limit_change:
      description: 'payment source of changing withdraw limit'
      properties:
        id:
          type: integer
        player_id:
          type: integer
        limit:
          $ref: '#/components/schemas/money'
        comment:
          description: 'change comment'
          type: string
      type: object
    segment_resource:
      description: 'Segment Resource'
      properties:
       id:
        type: integer
        example: 1
       external_id:
        type: string
        example: "ext-123"
       name:
        type: string
        example: "Example Name"
       is_active:
        type: boolean
        example: true
       created_at:
        type: string
        format: date-time
        example: "2023-09-25T12:34:56Z"
       updated_at:
        type: string
        format: date-time
        example: "2023-09-25T12:45:00Z"
    exception:
      description: 'Class UnauthorizedException'
      properties:
        message:
          description: 'exception message'
          type: string
        code:
          description: 'exception response code'
          type: integer
        context:
          description: 'exception context'
          type: object
        errors:
          type: array
          items:
            properties:
              message:
                description: 'error message'
                type: string
              reason:
                description: 'error reason phrase'
                type: string
              location:
                description: 'error location'
                type: string
            type: object
        previous:
          nullable: true
          properties:
            message:
              description: 'previous exception message'
              type: string
          type: object
      type: object
    bonus_info_player_data:
      description: 'player data object'
      properties:
        status:
          nullable: true
          description: 'bonus status for current player'
          type: string
          enum:
            - activated
            - deposit
            - expired
      type: object
    user_info:
      description: 'player''s basic info'
      properties:
        first_name:
          description: 'player''s first name'
          type: string
        last_name:
          nullable: true
          description: 'player''s last name'
          type: string
        email:
          description: 'player''s email'
          type: string
          format: email
        email_confirmed:
          description: email_confirmed
          type: boolean
        is_bonus_ready:
          description: 'is player allowed to play with welcome bonus'
          type: boolean
        uuid:
          description: 'player''s uuid'
          type: string
          format: uuid
        phone:
          nullable: true
          description: 'player''s phone'
          type: string
        birth:
          nullable: true
          description: 'player''s birthday'
          type: string
          format: date
        country:
          nullable: true
          description: 'player''s country'
          type: string
        currency:
          nullable: false
          description: 'player''s currency'
          type: string
        city:
          nullable: true
          description: 'player''s city'
          type: string
        language:
          nullable: true
          description: 'player''s language'
          type: string
        is_under_moderation:
          description: 'is under moderation'
          type: boolean
        gender:
          nullable: true
          description: 'player''s gender'
          type: string
          enum:
            - male
            - female
      type: object
    user_meta:
      description: 'players meta data'
      properties:
        last_seen_ip:
          description: 'player''s ip during last login'
          type: string
          format: ipv4
        blocked:
          description: 'is player''s blocked'
          type: boolean
        payout_without_moderation:
          description: 'payout will work without moderation'
          type: boolean
        welcome_bonus_id:
          nullable: true
          description: 'welcome bonus id'
          type: integer
        last_used_bonus_id:
          nullable: true
          description: 'saved bonus balance id'
          type: integer
        casino_access:
          description: 'is player''s has access to casino'
          type: boolean
        registered_at:
          description: 'when player has joined'
          type: integer
        last_seen_at:
          description: 'player''s last action at'
          type: integer
        comment:
          description: 'player''s comment assigned by system/admin'
          type: string
        is_suspicious:
          description: 'is player suspicious'
          type: boolean
        is_fake:
          description: 'is fake player'
          type: boolean
        is_vip:
          description: 'is vip player'
          type: boolean
        is_pre_vip:
          description: 'is pre vip player'
          type: boolean
        is_subscribed:
          description: 'is email subscribed'
          type: boolean
        tags:
          description: 'players tags'
          type: array
          items:
            type: string
        promo_codes:
          nullable: true
          description: 'players promo codes'
          type: array
          items:
            $ref: '#/components/schemas/promocode'
      type: object
    bonus_mass:
      description: 'Mass Applied bonus data'
      properties:
        id:
          description: id
          type: integer
        bonus_id:
          description: 'related bonus id'
          type: integer
        template_id:
          description: 'template id'
          type: integer
        external_player_id:
          description: 'player uuid'
          type: string
        currency:
          description: 'bonus currency'
          type: string
        amount:
          description: 'bonus amount'
          type: number
        status:
          description: 'bonus status'
          type: string
        created_at:
          nullable: false
          description: 'created at'
          type: string
        updated_at:
          nullable: false
          description: 'updated at'
          type: string
      type: object
  responses:
    '200':
      description: 'updated notification object'
      content:
        application/json:
          schema:
            properties:
              status:
                description: status
                type: string
                enum:
                  - 'Entity found'
            type: object
    '500':
      description: 'internal error'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/exception'
  parameters:
    page_param:
      name: pagination
      in: query
      description: 'pagination object'
      required: false
      allowEmptyValue: false
      style: deepObject
      explode: true
      schema:
        properties:
          limit:
            description: 'search limit'
            type: integer
            minimum: 1
          offset:
            description: 'search offset'
            type: integer
            minimum: 0
          page:
            description: 'search page'
            type: integer
            minimum: 1
        type: object
    limit_param:
      name: limit
      in: query
      description: limit
      required: false
      allowEmptyValue: false
      schema:
        type: integer
        default: 50
        minimum: 1
    all_param:
      name: all
      in: query
      description: offset
      required: false
      allowEmptyValue: false
      schema:
        type: string
        enum:
          - '1'
          - 'yes'
          - 'true'
          - 'on'
    id_query_param:
      name: id
      in: query
      description: id
      required: false
      allowEmptyValue: false
      schema:
        type: integer
        minimum: 1
    desc_param:
      name: desc
      in: query
      description: 'sort desc'
      required: false
      allowEmptyValue: false
      schema:
        type: string
    asc_param:
      name: asc
      in: query
      description: 'sort asc'
      required: false
      allowEmptyValue: false
      schema:
        type: string
    ids_param:
      name: ids
      in: query
      description: 'comma separated ids'
      required: false
      allowEmptyValue: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: integer
          minimum: 1
    id_param:
      name: id
      in: path
      description: 'entity id'
      required: true
      schema:
        type: integer
        minimum: 1
    admin_proxy:
      name: url
      in: path
      description: 'api url'
      schema:
        type: string
  securitySchemes:
    oauth:
      type: oauth2
      description: 'default api auth'
      flows:
        password:
          tokenUrl: /api/v1/oauth/token
          refreshUrl: /api/v1/oauth/token/refresh
          scopes: {  }
        clientCredentials:
          tokenUrl: /api/v1/oauth/token
          refreshUrl: /api/v1/oauth/token/refresh
          scopes: {  }
    1stParty:
      type: apiKey
      description: '1st party auth'
      name: access_token
      in: query
