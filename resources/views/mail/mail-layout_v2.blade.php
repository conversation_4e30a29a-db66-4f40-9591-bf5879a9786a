<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta name="x-apple-disable-message-reformatting">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="telephone=no" name="format-detection">
    <title>Transactional 1 (ENG)</title><!--[if (mso 16)]>
    <style type="text/css">
        a {text-decoration: none;}
    </style>
    <![endif]--><!--[if gte mso 9]><style>sup { font-size: 100% !important; }</style><![endif]--><!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG></o:AllowPNG>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <![endif]--><!--[if !mso]><!-- -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,700;1,700&display=swap"><!--<![endif]-->
    <style type="text/css">
        .rollover:hover .rollover-first {
            max-height:0px!important;
            display:none!important;
        }
        .rollover:hover .rollover-second {
            max-height:none!important;
            display:inline-block!important;
        }
        .rollover div {
            font-size:0px;
        }
        u ~ div img + div > div {
            display:none;
        }
        #outlook a {
            padding:0;
        }
        span.MsoHyperlink,
        span.MsoHyperlinkFollowed {
            color:inherit;
            mso-style-priority:99;
        }
        a.es-button {
            mso-style-priority:100!important;
            text-decoration:none!important;
        }
        a[x-apple-data-detectors] {
            color:inherit!important;
            text-decoration:none!important;
            font-size:inherit!important;
            font-family:inherit!important;
            font-weight:inherit!important;
            line-height:inherit!important;
        }
        .es-desk-hidden {
            display:none;
            float:left;
            overflow:hidden;
            width:0;
            max-height:0;
            line-height:0;
            mso-hide:all;
        }
        .es-header-body a:hover {
            color:#2cb543!important;
        }
        .es-content-body a:hover {
            color:#2cb543!important;
        }
        .es-footer-body a:hover {
            color:#ffffff!important;
        }
        .es-infoblock a:hover {
            color:#454570!important;
        }
        .es-button-border:hover > a.es-button {
            color:#281109!important;
        }
        td .es-button-border:hover a.es-button-5781 {
            color:#004797!important;
        }
        td .es-button-border:hover a.es-button-8059 {
            color:#252327!important;
        }
        td .es-button-border:hover a.es-button-5598 {
            color:#ffffff!important;
        }
        td .es-button-border:hover a.es-button-3958 {
            color:#ffffff!important;
        }
        @media only screen and (max-width:600px) {.es-m-p0t { padding-top:0!important } .es-m-p15t { padding-top:15px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10b { padding-bottom:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p0r { padding-right:0px!important } .es-m-p10r { padding-right:10px!important } .es-m-p15b { padding-bottom:15px!important } .es-m-p10l { padding-left:10px!important } .es-m-p0r { padding-right:0px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p20t { padding-top:20px!important } .es-m-p40r { padding-right:40px!important } .es-m-p15b { padding-bottom:15px!important } .es-m-p40l { padding-left:40px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p15b { padding-bottom:15px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p30r { padding-right:30px!important } .es-m-p5b { padding-bottom:5px!important } .es-m-p20r { padding-right:20px!important } .es-m-p5b { padding-bottom:5px!important } .es-m-p20l { padding-left:20px!important } .es-m-p20r { padding-right:20px!important } .es-m-p5b { padding-bottom:5px!important } .es-m-p20l { padding-left:20px!important } .es-m-p30l { padding-left:30px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p25b { padding-bottom:25px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p25b { padding-bottom:25px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10b { padding-bottom:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10 { padding:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p10l { padding-left:10px!important } .es-m-p10r { padding-right:10px!important } .es-m-p20b { padding-bottom:20px!important } .es-m-p25l { padding-left:25px!important } .es-m-p20b { padding-bottom:20px!important } .es-m-p20b { padding-bottom:20px!important } .es-m-p20b { padding-bottom:20px!important } .es-m-p10r { padding-right:10px!important } .es-m-p20b { padding-bottom:20px!important } .es-m-p10l { padding-left:10px!important } .es-m-p25r { padding-right:25px!important } .es-m-p20b { padding-bottom:20px!important } .es-m-p15t { padding-top:15px!important } .es-m-p20r { padding-right:20px!important } .es-m-p30b { padding-bottom:30px!important } .es-m-p20l { padding-left:20px!important } *[class="gmail-fix"] { display:none!important } p, a { line-height:150%!important } h1, h1 a { line-height:100%!important } h2, h2 a { line-height:120%!important } h3, h3 a { line-height:120%!important } h4, h4 a { line-height:120%!important } h5, h5 a { line-height:120%!important } h6, h6 a { line-height:120%!important } h1 { font-size:28px!important; text-align:center } h2 { font-size:15px!important; text-align:left } h3 { font-size:20px!important; text-align:left } h4 { font-size:24px!important; text-align:left } h5 { font-size:12px!important; text-align:center } h6 { font-size:12px!important; text-align:left } .es-content-body td { background-image: none!important; } .es-content-body td.bgcolor { background-color: #005ABF!important; } .es-content-body, .es-footer-body { background-color: #1A215B!important; } .es-header-body h1 a, .es-content-body h1 a, .es-footer-body h1 a { font-size:28px!important } .es-header-body h2 a, .es-content-body h2 a, .es-footer-body h2 a { font-size:16px!important } .es-header-body h3 a, .es-content-body h3 a, .es-footer-body h3 a { font-size:20px!important } .es-header-body h4 a, .es-content-body h4 a, .es-footer-body h4 a { font-size:24px!important } .es-header-body h5 a, .es-content-body h5 a, .es-footer-body h5 a { font-size:12px!important } .es-header-body h6 a, .es-content-body h6 a, .es-footer-body h6 a { font-size:12px!important } .es-menu td a { font-size:14px!important } .es-header-body p, .es-header-body a { font-size:12px!important } .es-content-body p, .es-content-body a { font-size:12px!important } .es-footer-body p, .es-footer-body a { font-size:12px!important } .es-infoblock p, .es-infoblock a { font-size:10px!important } .es-m-txt-c, .es-m-txt-c h1, .es-m-txt-c h2, .es-m-txt-c h3, .es-m-txt-c h4, .es-m-txt-c h5, .es-m-txt-c h6 { text-align:center!important } .es-m-txt-r, .es-m-txt-r h1, .es-m-txt-r h2, .es-m-txt-r h3, .es-m-txt-r h4, .es-m-txt-r h5, .es-m-txt-r h6 { text-align:right!important } .es-m-txt-j, .es-m-txt-j h1, .es-m-txt-j h2, .es-m-txt-j h3, .es-m-txt-j h4, .es-m-txt-j h5, .es-m-txt-j h6 { text-align:justify!important } .es-m-txt-l, .es-m-txt-l h1, .es-m-txt-l h2, .es-m-txt-l h3, .es-m-txt-l h4, .es-m-txt-l h5, .es-m-txt-l h6 { text-align:left!important } .es-m-txt-r img, .es-m-txt-c img, .es-m-txt-l img { display:inline!important } .es-m-txt-r .rollover:hover .rollover-second, .es-m-txt-c .rollover:hover .rollover-second, .es-m-txt-l .rollover:hover .rollover-second { display:inline!important } .es-m-txt-r .rollover div, .es-m-txt-c .rollover div, .es-m-txt-l .rollover div { line-height:0!important; font-size:0!important } .es-spacer { display:inline-table } a.es-button, button.es-button { font-size:18px!important } a.es-button, button.es-button { display:inline-block!important } .es-button-border { display:inline-block!important } .es-m-fw, .es-m-fw.es-fw, .es-m-fw .es-button { display:block!important } .es-m-il, .es-m-il .es-button, .es-social, .es-social td, .es-menu { display:inline-block!important } .es-adaptive table, .es-left, .es-right { width:100%!important } .es-content table, .es-header table, .es-footer table, .es-content, .es-footer, .es-header { width:100%!important; max-width:600px!important } .adapt-img { width:100%!important; height:auto!important } .es-mobile-hidden, .es-hidden { display:none!important } .es-desk-hidden { width:auto!important; overflow:visible!important; float:none!important; max-height:inherit!important; line-height:inherit!important } tr.es-desk-hidden { display:table-row!important } table.es-desk-hidden { display:table!important } td.es-desk-menu-hidden { display:table-cell!important } .es-menu td { width:1%!important } table.es-table-not-adapt, .esd-block-html table { width:auto!important } .es-social td { padding-bottom:10px } .h-auto { height:auto!important } .block-links a { width: 100%!important; height: 60px!important; } .block-links a.btn1 { background-image: url(https://xiqaer.stripocdn.email/content/guids/CABINET_81bd729ab475fc3a014f3c7cf1c233dff56670424014b825aa9eb40871e3534c/images/frame_23.png)!important; } .block-links a.btn2 { background-image: url(https://xiqaer.stripocdn.email/content/guids/CABINET_81bd729ab475fc3a014f3c7cf1c233dff56670424014b825aa9eb40871e3534c/images/frame_24.png)!important; } .block-links a.btn3 { background-image: url(https://xiqaer.stripocdn.email/content/guids/CABINET_81bd729ab475fc3a014f3c7cf1c233dff56670424014b825aa9eb40871e3534c/images/frame_25.png)!important; } .block-links a.btn4 { background-image: url(https://xiqaer.stripocdn.email/content/guids/CABINET_81bd729ab475fc3a014f3c7cf1c233dff56670424014b825aa9eb40871e3534c/images/frame_26.png)!important; margin-bottom: 15px; } .img-2937 { height:15px!important } .img-9580 { height:15px!important } .img-3078 { height:15px!important } .img-5442 { height:15px!important } .img-8954 { width:15px!important } }
    </style>
</head>
<body style="width:100%;height:100%;padding:0;Margin:0">
<div class="es-wrapper-color" style="background-color:#1A215B"><!--<[if gte mso 9]>
    <v:background xmlns:v="urn:schemas-microsoft-com:vml" fill="t">
        <v:fill type="tile"  color="#1A215B" origin="0.5, 0" position="0.5, 0"></v:fill>
    </v:background>
    <![endif]-->
    @yield('content')
</div>
</body>
</html>
