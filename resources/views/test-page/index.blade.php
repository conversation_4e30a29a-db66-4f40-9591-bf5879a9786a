<!DOCTYPE html>
<html lang="en">
<head>
    <title>Test page</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
</head>
<body>
<main role="main" class="container">
    <div class="d-flex justify-content-around">
        <div class="card mt-4" style="width: 18rem; height: min-content">
            <div class="card-body">
                <h5 class="card-title bold">Deposit</h5>
                <form class="card-text" action="{{route('test_deposit_hook_v3')}}" method="POST">
                    <div class="form-group">
                        <input type="number" name="player_id" class="form-control" id="playerId" placeholder="Player id" required>
                    </div>
                    <div class="form-group">
                        <input type="number" name="amount" class="form-control" id="amount" placeholder="Amount without cents"
                               required>
                    </div>
                    <div class="form-group">
                        <input type="text" name="currency" class="form-control" id="currency" placeholder="Currency" required>
                    </div>

                    <button type="submit" class="btn btn-dark w-100">Make deposit</button>
                </form>
            </div>
        </div>
        <div class="card mt-4" style="width: 18rem; height: min-content">
            <div class="card-body">
                <h5 class="card-title bold">Send Email</h5>
                <form class="card-text" action="{{route('send_test_email')}}" method="POST">
                    <div class="form-group">
                        <input type="number" name="player_id" class="form-control" id="playerId" placeholder="Player id" required>
                    </div>
                    <button type="submit" class="btn btn-dark w-100">Send Email</button>
                </form>
            </div>
        </div>
        <div class="card mt-4" style="width: 30rem;">
            <div class="card-body">
                <h5 class="card-title bold">Websocket events</h5>
                <form class="card-text" onsubmit="sendWsEvent(event)">
                    <div class="form-group">
                        <input type="text" class="form-control" id="wsPlayerId" placeholder="Player id or uuid" required>
                    </div>
                    <div class="form-group">
                        <select onchange="selectedEventChanged(this)" class="form-control" id="wsEventType" required>
                            <option disabled selected value>-- Select event type --</option>
                            @foreach($broadcastEventsList as $eventType)
                                <option value="{{$eventType}}">{{$eventType}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <textarea oninput="prettyJson()" class="form-control" id="wsData" placeholder="Json data" required rows="10"></textarea>
                    </div>

                    <div class="form-group">
                        <input type="text" class="form-control" id="wsReason" placeholder="Reason (nullable)">
                    </div>

                    <button class="btn btn-dark w-100">Send event</button>
                </form>
            </div>
        </div>
    </div>

</main>

<script>
    function sendWsEvent(event) {
        event.preventDefault()

        let data = {
            method: 'POST',
            body: JSON.stringify({
                player_id: document.getElementById('wsPlayerId').value,
                event: document.getElementById('wsEventType').value,
                data: document.getElementById('wsData').value,
                reason: document.getElementById('wsReason').value,
            }),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            }
        }

        fetch(`{{route('test_websocket_event', [], env('APP_ENV') !== 'local')}}`, data)
            .then(function(response) {
                if (response.status === 200) {
                    showToast("Success", true)
                } else {
                    showToast("Something went wrong", false)
                }
            })

        return false;
    }

    function prettyJson() {
        let raw = document.getElementById('wsData').value;

        try {
            let obj = JSON.parse(raw);
            document.getElementById('wsData').value = JSON.stringify(obj, undefined, 4);
            document.getElementById('wsData').classList.remove('is-invalid')
            document.getElementById('wsData').classList.add('is-valid')
        } catch (e) {
            document.getElementById('wsData').classList.remove('is-valid')
            document.getElementById('wsData').classList.add('is-invalid')
        }
    }

    function showToast(text, isSuccess) {
        let background = isSuccess
            ? "linear-gradient(to right, rgb(8 148 17), rgb(1 206 82))"
            : "linear-gradient(to right, rgb(255 51 51), rgb(242 89 89))"

        Toastify({
            text: text,
            duration: 5000,
            close: true,
            gravity: "bottom", // `top` or `bottom`
            position: "right", // `left`, `center` or `right`
            stopOnFocus: true, // Prevents dismissing of toast on hover
            style: {
                background: background,
            },
        }).showToast();
    }

    function selectedEventChanged(item) {
        const examples = @json($broadcastEventsExamples, JSON_THROW_ON_ERROR);

        document.getElementById('wsData').value = JSON.stringify(examples[item.value] ?? {})
        prettyJson()
    }
</script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
</body>
