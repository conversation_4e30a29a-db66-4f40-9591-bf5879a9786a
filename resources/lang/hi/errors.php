<?php

return [
    'entity_not_found' => 'इकाई नहीं मिली',
    'error_saving_model' => ':model मॉडल सहेजने में त्रुटि',
    'deleting_prohibited' => 'संवेदनशील जानकारी को हटाना प्रतिबंधित है',
    'only_wager_bonuses' => 'केवल दांव बोनस खेलने की अनुमति है',
    'bonus_balance_reqs' => 'बोनस शेष न्यूनतम आवश्यकताओं को पूरा नहीं करता है',
    'username_already_exists' => ':username उपयोगकर्ता नाम वाला प्लेयर पहले से मौजूद है',
    'not_enough_money' => 'पर्याप्त पैसा नहीं हैं',
    'bonus_expired' => 'बोनस समाप्त हो गया है या निष्क्रिय कर दिया गया है',
    'invalid_argument_provided' => ':argument - :value के लिए अमान्य तर्क प्रदान किया गया',
    'unauthorized' => 'अनधिकृत',
    'unauthorized_user' => 'अनधिकृत उपयोगकर्ता',
    'password_mismatch' => 'पासवर्ड एक दूसरे से मेल नहीं खाते',
    'password_like_email' => 'पासवर्ड आपके ईमेल जैसा नहीं हो सकता',
    'old_password_not_same' => 'आपने गलत पुराना पासवर्ड दर्ज किया है',
    'password_same_like_old' => 'आपने पहले भी इस पासवर्ड का उपयोग किया है, कृपया इसके स्थान पर एक नया पासवर्ड चुनें।',
    'withdrawal_request_limit' => 'प्रत्येक 24 घंटे में केवल :count बार निकासी अनुरोध सबमिट करने की सुविधा उपलब्ध है',
    'token_expired' => 'टोकन समाप्त हो गया है या निष्क्रिय कर दिया गया है',
    'birth_date_already_set' => 'जन्म तिथि पहले से ही निर्धारित है',
    'phone_already_exists' => 'फोन :phone पहले से मौजूद है',
    'player_phone_already_exists' => ':phone फोन वाला प्लेयर पहले से मौजूद है',
    'user_email_already_exists' => ':email ईमेल वाला उपयोगकर्ता पहले से पंजीकृत है',
    'resource_locked' => 'संसाधन :resource बंद है',
    'validation_exception' => 'मान्यता त्रुटि',
    'no_changes_applied' => 'कोई परिवर्तन लागू नहीं किया गया',
    'transaction_retrieving' => 'लेन-देन पुनर्प्राप्त करने की अनुमति नहीं है',
    'already_have_bet' => 'इस मैच पर आपके पास पहले से ही एक सक्रिय बेट है',
    'phone_number_is_not_registered' => 'फोन नंबर रजिस्टर नहीं किया गया है',
    'account_blocked_by_verification' => 'आपका खाता असफल सत्यापन के कारण ब्लॉक कर दिया गया है',
    'incorrect_code' => 'गलत कोड। कृपया, दोबारा प्रयास करें',
    'phone_already_exists_profile' => ':phone फोन पहले से मौजूद है',
    'phone_already_exists_send_code' => ':phone फ़ोन नंबर पहले से मौजूद है',
    'validation_error' => 'प्रमाणीकरण त्रुटि',
    'too_many_attempts' => 'प्रयासों की सीमा पार हो गई है',
];
