<?php

return [
    'entity_not_found' => 'Objeto não encontrado',
    'error_saving_model' => 'Erro ao salvar :model',
    'deleting_prohibited' => 'Remoção da informação confidencial é proibida',
    'only_wager_bonuses' => 'Permitido jogar apenas com bônus de aposta',
    'bonus_balance_reqs' => 'Saldo de bônus disponível não cumpre com os requisitos mínimos',
    'username_already_exists' => 'O jagador com o nome do utilizador :username já existe',
    'not_enough_money' => 'Saldo insuficiente',
    'bonus_expired' => 'O bônus expirou ou foi deativado',
    'invalid_argument_provided' => 'Parâmetro inadmissível Indicado :argument - :value',
    'unauthorized' => 'Não logado',
    'unauthorized_user' => 'Utilizador logado',
    'password_mismatch' => 'As senhas não coincidem',
    'password_like_email' => 'A senha não pode ser igual ao seu e-mail',
    'old_password_not_same' => 'Você digitou uma senha antiga incorreta',
    'password_same_like_old' => 'Você já usou essa senha antes, por favor escolha uma nova.',
    'withdrawal_request_limit' => 'O pedido de retirada é disponível apenas :count vezes em 24 horas',
    'token_expired' => 'O token expirou ou foi deativado',
    'birth_date_already_set' => 'A data de nascimento já está indicada',
    'phone_already_exists' => 'O número telefônico :phone já consta na base',
    'player_phone_already_exists' => 'O jogador com o número telefônico :phone já foi cadastrado',
    'user_email_already_exists' => 'O jogador com o email :email já foi cadastrado',
    'resource_locked' => 'A fonte :resource está bloqueada',
    'validation_exception' => 'Error de validação',
    'no_changes_applied' => 'Mudanças não foram aplicadas',
    'transaction_retrieving' => 'Recebimento da transação é proibida',
    'already_have_bet' => 'Você já tem a aposta ativa para esta partida',
    'account_blocked_by_verification' => 'Sua conta foi bloqueada devido à falha na verificação',
    'incorrect_code' => 'Código incorreto. Por favor, tente novamente',
    'phone_already_exists_profile' => ':phone o número de telefone já existe',
    'phone_already_exists_send_code' => ':phone o número de telefone já existe',
    'validation_error' => 'Erro de validação',
    'too_many_attempts' => 'O limite de tentativas foi excedido',
];
