<?php declare(strict_types=1);

use InternalErrors\InternalErrorCodeEnum;

return [
    InternalErrorCodeEnum::GENERAL_ERROR => 'General error',
    InternalErrorCodeEnum::PLAYER_HAS_NO_ACCESS => 'Player has no access',
    InternalErrorCodeEnum::SESSION_HAS_ENDED => 'Session has ended',
    InternalErrorCodeEnum::BONUS_GAMES_BARRED => 'Bonus games barred',
    InternalErrorCodeEnum::NOT_ENOUGH_MONEY => 'Not enough money',
    InternalErrorCodeEnum::WRONG_CURRENCY => 'Wrong currency',
    InternalErrorCodeEnum::WAGER_MORE_THAN_0 => 'Wager more than 0',
    InternalErrorCodeEnum::BET_NOT_FOUND => 'Bet not found',
    InternalErrorCodeEnum::WRONG_AMOUNT => 'Wrong amount',
    InternalErrorCodeEnum::ALREADY_CREATED => 'Already created',
    InternalErrorCodeEnum::WRONG_BALANCE => 'Wrong balance',
    InternalErrorCodeEnum::BET_STATUS_ERROR => 'Bet status error',
    InternalErrorCodeEnum::FREESPIN_NOT_FOUND => 'Freespin not found',
    InternalErrorCodeEnum::FREESPIN_QUANTITY_MISMATCH => 'Freespin quantity mismatch, transaction declined',
    InternalErrorCodeEnum::MIN_BET_ERROR => 'Min bet error',
    InternalErrorCodeEnum::NO_CHANGES => 'No changes',
    InternalErrorCodeEnum::SLOT_NOT_FOUND => 'Slot not found',
    InternalErrorCodeEnum::BONUS_SAME_MATCH => 'Bonus same match',
    InternalErrorCodeEnum::BONUS_UNAVAILABLE => 'Bonus unavailable',
    InternalErrorCodeEnum::BONUS_GAME_IS_NOT_AVAILABLE => 'This game does not support playing with bonus balances',
    InternalErrorCodeEnum::WRONG_MAX_REAL_BALANCE => 'Amount on main balance more than allowed to use bonus balance',
];
