<?php

return [
    'entity_not_found' => 'Entity not found',
    'error_saving_model' => 'Error saving model :model',
    'deleting_prohibited' => 'Deleting sensitive info is prohibited',
    'only_wager_bonuses' => 'Only wager bonuses are allowed to be played',
    'bonus_balance_reqs' => 'Bonus balance does not meet min reqs',
    'username_already_exists' => 'Player with :username username is already exist',
    'not_enough_money' => 'Not enough money',
    'bonus_expired' => 'Bonus is expired or has been deactivated',
    'invalid_argument_provided' => 'Invalid argument provided for :argument - :value',
    'unauthorized' => 'Unauthorized',
    'unauthorized_user' => 'Unauthorized user',
    'password_mismatch' => 'Passwords do not match each other',
    'password_like_email' => 'Password cannot be the same as your email',
    'old_password_not_same' => 'You entered an incorrect old password',
    'password_same_like_old' => 'You\'ve used this password before, please choose a new one instead.',
    'withdrawal_request_limit' => 'Submitting a withdrawal request is available only :count times every 24 hours',
    'token_expired' => 'Token is expired or has been deactivated',
    'birth_date_already_set' => 'Birth date is already set',
    'phone_already_exists' => 'Phone :phone already exists',
    'player_phone_already_exists' => 'Player with :phone phone is already exist',
    'user_email_already_exists' => 'User with :email email is already registered',
    'resource_locked' => 'Resource :resource is locked',
    'validation_exception' => 'Validation exception',
    'no_changes_applied' => 'No changes applied',
    'transaction_retrieving' => 'Transaction retrieving is not allowed',
    'already_have_bet' => 'You already have an active bet on this match',
    'game_barred_bonus_balances' => 'This game does not support playing with bonus balances',
    'too_many_attempts' => 'Attempts limit has been exceeded',
    'phone_number_is_not_registered' => 'Phone number is not registered',
    'account_blocked' => 'Your account has been blocked',
    'account_blocked_hint' => 'Your account has been blocked. Please contact support for assistance.',
    'account_blocked_by_verification' => 'Your account has been blocked due to failed verification',
    'incorrect_code' => 'Incorrect code.Please, try again',
    'phone_already_exists_profile' => ':phone phone already exists',
    'phone_already_exists_send_code' => ':phone phone number already exists',
    'validation_error' => 'Validation error',
    'user_already_registered' => 'The user is already registered. Do you want to log in?|errorCode=:error_code',
    'cannot_cancel_recommended_verification' => 'You can\'t  cancel recommended verification',
    'vip_status' => 'You don\'t have VIP status',
    'verification_is_required' => 'Verification is required',
];
