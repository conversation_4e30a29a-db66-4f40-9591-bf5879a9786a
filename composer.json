{"name": "whitelabel/app", "require": {"php": "^7.4", "ext-json": "*", "ext-newrelic": "*", "ext-redis": "*", "brick/phonenumber": "^0.4.0", "deversin/personal-responsecache": "^1.0", "deversin/signature": "1.2.*", "doctrine/annotations": "^1.10", "doctrine/cache": "^1.10", "doctrine/dbal": "^3.0", "dusterio/lumen-passport": "^0.3.1", "elasticsearch/elasticsearch": "8.8.0", "enqueue/fs": "^0.10.4", "enqueue/laravel-queue": "^0.10.3", "enqueue/rdkafka": "^0.10.5", "enqueue/simple-client": "^0.10.4", "fruitcake/laravel-cors": "^2.0", "geoip2/geoip2": "^2.11", "google/apiclient": "^2.14", "hedii/laravel-gelf-logger": "^6.0", "illuminate/auth": "*", "illuminate/mail": "^8.7", "illuminate/redis": "^8.7", "intouch/newrelic": "^1.0", "jarvis/integrator-core": "^2.0.50", "jenssegers/agent": "^2.6", "laravel/lumen-framework": "^8.0", "league/oauth2-client": "^2.5", "moneyphp/money": "^3.3", "opekunov/laravel-centrifugo-broadcaster": "^1.2", "palicao/php-rebloom": "^0.2.1", "predis/predis": "^1.1", "printu/customerio": "^3.4", "pusher/pusher-php-server": "~3.0", "ramsey/uuid": "^4.1", "skipper/base-api": "^1.2.2", "skipper/exceptions": "^1.2", "skipper/logger": "^1.1", "skipper/repository": "^1.2", "skipper/search": "^1.4", "skipper/strategies": "^1.1", "spatie/laravel-responsecache": "^6.6.9", "spatie/url-signer": "^1.2", "symfony/intl": "^5.1", "symfony/lock": "^5.1", "symfony/uid": "^5.4", "symfony/validator": "^5.1", "thedevsaddam/lumen-route-list": "^2.0", "white-label/internal-errors": "^1.0.2", "white-label/redis-buffer": "^1.3", "zircote/swagger-php": "^3.1"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.12", "dg/bypass-finals": "^1.7", "fzaninotto/faker": "^1.9.1", "kwn/php-rdkafka-stubs": "^2.2", "laravel/tinker": "^2.4", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^9.3"}, "autoload": {"psr-4": {"App\\": "app/", "AppV3\\": "appV3/", "WhiteLabelApp\\": "src/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["SxGeo.php", "app/Support/helpers.php"]}, "autoload-dev": {"classmap": ["tests/"]}, "config": {"preferred-install": "dist", "sort-packages": true, "secure-http": false, "optimize-autoloader": true, "allow-plugins": {"php-http/discovery": true}}, "repositories": [{"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/repository.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/exceptions.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/search.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/base-api.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/strategies.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/logger.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/microservices/integrator-core.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/white-label/wl-crypto-signature.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/personal-responsecache.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/redis-buffer.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/internal-errors.git"}], "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}