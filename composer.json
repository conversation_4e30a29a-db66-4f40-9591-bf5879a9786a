{"name": "whitelabel/admin", "require": {"php": "^7.4", "ext-json": "*", "ext-newrelic": "*", "deversin/signature": "^1.2", "doctrine/annotations": "^1.10", "doctrine/cache": "^1.10", "doctrine/dbal": "^3.6", "dusterio/lumen-passport": "^0.3.1", "fruitcake/laravel-cors": "^2.0", "hedii/laravel-gelf-logger": "^6.0", "illuminate/redis": "^8.8", "intouch/newrelic": "^2.0", "jarvis/integrator-core": "^2.0", "laravel/lumen-framework": "^8.0", "league/flysystem": "1.1.9", "league/oauth2-client": "^2.5", "moneyphp/money": "^3.3", "mongodb/mongodb": "^1.7", "predis/predis": "^1.1", "skipper/base-api": "^1.2.2", "skipper/exceptions": "^1.2", "skipper/repository": "^1.2", "skipper/search": "^1.4", "symfony/intl": "^5.1", "symfony/validator": "^5.1", "thedevsaddam/lumen-route-list": "^2.0", "zircote/swagger-php": "^3.1"}, "require-dev": {"fzaninotto/faker": "^1.9.1", "laravel/tinker": "^2.4", "mockery/mockery": "^1.3.1", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5"}, "autoload": {"psr-4": {"App\\": "app/", "AppV2\\": "AppV2/", "WhiteLabelAdmin\\": "src/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Support/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "config": {"preferred-install": "dist", "sort-packages": true, "secure-http": false, "optimize-autoloader": true}, "repositories": [{"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/repository.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/exceptions.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/search.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/white-label/base-api.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/white-label/wl-crypto-signature.git"}, {"type": "vcs", "url": "https://token:<EMAIL>/internal-dependencies/microservices/integrator-core.git"}], "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}